.content {
    display: flex;
    flex-direction: column;
    height: 100%;
     background: #ffffff;
    .form {
        display: flex;
        background: #ffffff;
        box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.06);
        padding-left: 22px;
        flex-direction: column;
        border-radius: 10px;
        margin: 10px 10px;
        .item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #dcdfe6;
            .label {
                font-size: 14px;
                font-weight: 500;
                color: #303133;
                position: relative;
                &.required::before {
                    content: '*';
                    color: #f56c6c;
                    left: -10px;
                    position: absolute;
                }
            }
            .value {
                padding-right: 12px;
                flex: 1;
            }
        }
    }
    .action {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        .tip {
            font-size: 12px;
            color: #999999;
            margin-bottom: 10px;
            margin-left: 10px;
        }
        .reg-action {
            font-size: 14px;
            color: #ffffff;
            background: #00b678;
            padding: 10px 0;
            text-align: center;
            border-radius: 4px;
        }
    }
}
