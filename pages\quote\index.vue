<template>
    <view class="content">
        <view class="header" :style="{ height: statusBarHeight + 'px' }">
            <view class="title">玲珑AI报价助手</view>
        </view>
        <view class="body">
            <view class="action">
                <view class="item item-bg" @click="onTakePicture()">
                    <view class="name">拍照上传</view>
                    <view class="icon-box">
                        <image class="camera" src="/static/camera.png"></image>
                    </view>
                    <image class="redirect" src="/static/redirect.png"></image>
                </view>
                <view class="item item-bg" @click="onChooseAlbum()">
                    <view class="name">相册选择</view>
                    <view class="icon-box">
                        <image class="camera" src="/static/album.png"></image>
                    </view>
                    <image class="redirect" src="/static/redirect.png"></image>
                </view>
                <view class="chat-voice">
                    <view class="chat-action item-bg" @click="handleChooseMessageFile()">
                        <view class="name">聊天文件</view>
                        <view class="chat-action-chat-file">
                            <image class="redirect" src="/static/redirect.png"></image>
                            <image class="chat-file" src="/static/chat-file.png"></image>
                        </view>
                    </view>
                    <view class="voice-action item-bg" @click="handleVoice()">
                        <view class="name">语音输入</view>
                        <view class="chat-action-microphone">
                            <image class="redirect" src="/static/redirect.png"></image>
                            <image class="microphone" src="/static/microphone.png"></image>
                        </view>
                    </view>
                </view>
            </view>
            <view class="quote">
                <image class="recent-inquiries" src="/static/recent-inquiries.png"></image>

                <scroll-view scroll-y class="scroll-view">
                    <view class="record">
                        <view class="item" v-for="(item, index) in state.quotationHistoryList || []" :key="index" @click="reQuote(item)">
                            <view class="company">
                                <image class="company_icon" src="/static/company.png"></image>
                                <view class="company_info">
                                    <view class="name">{{ item.inquiryOrgName }}</view>
                                    <view class="date">{{ item.inquiryTime }}</view>
                                </view>
                            </view>
                            <view class="price">
                                <view class="price_icon">￥</view>
                                <view class="total">{{ formatThousands(item.quotationAmount) }}</view>
                            </view>
                        </view>
                    </view>
                </scroll-view>
            </view>
        </view>
        <loading-page :loading="loading"></loading-page>
        <uv-popup ref="jumpingSpectrumPopup" :round="8" :closeOnClickOverlay="false" bgColor="none" :safeAreaInsetBottom="false" mode="bottom" closeable>
            <view class="voice_container">
                <view class="voice" v-show="state.showVoiceContent">
                    <uv-textarea
                        v-model="state.voiceContent"
                        placeholder="请输入内容"
                        :customStyle="{
                            'border-width': '1px !important'
                        }"
                    ></uv-textarea>

                    <view class="again-voice">
                        <view @click="onAgainVoice()">重新录入</view>
                    </view>

                    <view class="action_box">
                        <view class="cancel" @click="onCancelVoiceContent()">取消</view>
                        <view class="confirm" @click="onConfirmVoiceContent()">确定</view>
                    </view>
                </view>

                <JumpingSpectrum
                    v-if="state.buildJumpingSpectrum"
                    :height="50"
                    v-show="!state.showVoiceContent"
                    @onCancel="handleOnCancelVoice()"
                    @onConfirmVoice="handleOnConfirmVoice"
                />
            </view>
        </uv-popup>
    </view>
</template>

<script setup lang="ts">
import { nextTick, onMounted, reactive, ref } from 'vue';
import { useSystemInfo } from '@/hooks/systemInfo';
import { queryQuotationHistory } from '@/api/quotation-history';
import { IAuth, IQuotationHistory } from '@/models';
import { checkAuthorizeScope, chooseMedia, chooseMessageFile, formatThousands, login, navigateTo, reLaunch, redirectTo, showToast } from '@/utils';
import { getLatestVersion } from '@/api/product-version';
import { uploadImage } from '@/api/file';
import { pasteContentInquiry, quotationInquiryParseInquiry } from '@/api/quotation';
import { useLoadingFn } from '@/hooks';
import { getStorageValue } from '@/utils/storage';
import { AuthAccessToken } from '@/consts';
import { getWeChatUserOpenId } from '@/api/we-chat';
import { onPullDownRefresh, onShow, onHide } from '@dcloudio/uni-app';
import JumpingSpectrum from '@/components/jumping-spectrum/jumping-spectrum.vue';
import { QuotationInquiryParseEnum } from '../../enums';

const loading = ref(true);
const jumpingSpectrumPopup = ref();
const { statusBarHeight, menuButtonHeight } = useSystemInfo();
const handlequeryQuotationHistory = useLoadingFn(onQueryQuotationHistory, loading);

const state = reactive<{
    quotationHistoryList: Array<IQuotationHistory>;
    voiceContent: string;
    showVoiceContent: boolean;
    buildJumpingSpectrum: boolean;
}>({
    quotationHistoryList: [],
    voiceContent: '',
    showVoiceContent: false,
    buildJumpingSpectrum: false
});

onMounted(async () => {
    const checkAuth = await checkAccountLogin();
    if (!checkAuth) {
        reLaunch({ url: '/pages/login/index' });
        return;
    }
});

onShow(() => {
    state.buildJumpingSpectrum = false;
    jumpingSpectrumPopup.value?.close();
    handlequeryQuotationHistory();
});

onHide(() => {
    state.buildJumpingSpectrum = false;
    jumpingSpectrumPopup.value?.close();
});

onPullDownRefresh(async () => {
    handlequeryQuotationHistory();
    uni.stopPullDownRefresh();
});

const onTakePicture = async () => {
    const res = await chooseMedia({ mediaType: ['image'], sourceType: ['camera'], sizeType: ['compressed'], camera: 'back' });
    if (res.errMsg != 'chooseMedia:ok') {
        return;
    }
    navigateTo({ url: `/pages/inquiry-photo/index?url=${res.tempFiles[0].tempFilePath}` });
};

const onChooseAlbum = async () => {
    const res = await chooseMedia({ mediaType: ['image'], sourceType: ['album'], sizeType: ['compressed'], count: 3 });
    if (res.errMsg != 'chooseMedia:ok') {
        return;
    }
    const paths = res.tempFiles.map((x) => x.tempFilePath).join(',');
    navigateTo({ url: `/pages/inquiry-photo/index?url=${paths}` });
};

const handleChooseMessageFile = async () => {
    uni.chooseMessageFile({
        type: 'all',
        count: 1,
        success: async (res) => {
            console.log(res);
            if (res.errMsg != 'chooseMessageFile:ok') {
                showToast({ title: '请选择文件', icon: 'none' });
                return;
            }

            const { data: uploadRes } = await uploadImage(res.tempFiles[0].path);
            if (!uploadRes.id) {
                showToast({ title: '上传失败，请重新上传' });
                return;
            }

            const { data: productVersion } = await getLatestVersion();

            if (!productVersion.id) {
                showToast({ title: '获取产品版本失败，请联系管理员' });
                return;
            }

            const { data: taskId } = await quotationInquiryParseInquiry({
                versionId: productVersion.id,
                inquiryFileIdList: [uploadRes.id]
            });
            navigateTo({ url: `/pages/inquiry/index?taskId=${taskId}` });
        },
        fail: (e) => {
            console.log('e', e);
        }
    });
};

const handleVoice = async () => {
    // 检查语音权限
    const recordPermission = await checkAuthorizeScope('record', '麦克风');
    if (!recordPermission) {
        return;
    }
    state.buildJumpingSpectrum = true;
    state.showVoiceContent = false;
    jumpingSpectrumPopup.value?.open();
};

function handleOnCancelVoice() {
    state.buildJumpingSpectrum = false;
    jumpingSpectrumPopup.value?.close();
}

function onAgainVoice() {
    state.voiceContent = '';
    state.showVoiceContent = false;
}

function onCancelVoiceContent() {
    state.voiceContent = '';
    jumpingSpectrumPopup.value?.close();
}

async function onConfirmVoiceContent() {
    if (!state.voiceContent?.trim()) {
        showToast({ title: '型号规格不能为空', icon: 'none' });
        return;
    }

    const { data } = await getLatestVersion();

    const { data: taskId } = await pasteContentInquiry({ versionId: data.id, pastedContent: state.voiceContent.trim(), sourceType: QuotationInquiryParseEnum.MINI_PROGRAM });
    if (!taskId) {
        showToast({ title: '解析失败，请重新录入录音', icon: 'none' });
        return;
    }
    jumpingSpectrumPopup.value?.close();
    navigateTo({ url: `/pages/inquiry/index?taskId=${taskId}` });
}

function handleOnConfirmVoice(value: string) {
    state.voiceContent = value;
    state.showVoiceContent = true;
}

async function onQueryQuotationHistory() {
    const { data } = await queryQuotationHistory({ pageNo: 1, pageSize: 10 });
    state.quotationHistoryList = data.list;
}

const reQuote = (data: IQuotationHistory) => {
    navigateTo({ url: `/pages/inquiry/index?taskId=${data.id}` });
};

const checkAccountLogin = async () => {
    const auth = getStorageValue<IAuth>(AuthAccessToken);
    if (auth && auth.accessToken) {
        return true;
    }

    // 没有登录
    const loginRes = await login();

    if (loginRes.errMsg != 'login:ok') {
        showToast({ title: '微信登录失败，请重试', icon: 'error' });
        return false;
    }

    const { data } = await getWeChatUserOpenId(loginRes.code);
    return data.allowLogin;
};
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>
