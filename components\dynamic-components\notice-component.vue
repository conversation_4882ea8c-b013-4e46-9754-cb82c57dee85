<template>
  <view 
    class="notice-component" 
    :style="containerStyle"
    @click="handleClick"
  >
    <view class="notice-content" :style="contentStyle">
      <!-- 图标 -->
      <view v-if="config.showIcon" class="notice-icon">
        <uv-icon 
          :name="config.iconType" 
          :color="config.iconColor" 
          :size="config.fontSize"
        />
      </view>
      
      <!-- 滚动文本 -->
      <view class="notice-text-container" :style="textContainerStyle">
        <text 
          v-if="config.scrollable"
          class="notice-text scrolling-text" 
          :style="textStyle"
        >
          {{ config.content }}
        </text>
        <text 
          v-else
          class="notice-text" 
          :style="textStyle"
        >
          {{ config.content }}
        </text>
      </view>
      
      <!-- 关闭按钮 -->
      <view v-if="config.showCloseButton && !closed" class="notice-close" @click.stop="handleClose">
        <uv-icon name="close" :color="config.textColor" :size="config.fontSize" />
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { NoticeConfig } from '../../types/dynamic-page';
import { convertStyleToCSS, colorUtils, sizeUtils } from '../../utils/style-helper';
import { theme } from '../../config/theme';

interface Props {
  config: NoticeConfig['config'];
  style?: NoticeConfig['style'];
}

const props = defineProps<Props>();
const closed = ref(false);



// 容器样式
const containerStyle = computed(() => {
  try {
    const baseStyle = convertStyleToCSS(props.style);
    return {
      ...baseStyle,
      display: closed.value ? 'none' : 'block',
    };
  } catch (error) {
    return {
      display: closed.value ? 'none' : 'block',
    };
  }
});

// 内容样式
const contentStyle = computed(() => {
  return {
    backgroundColor: colorUtils.getThemeColorOrDefault(props.config.backgroundColor, theme.colors.warning),
    height: sizeUtils.convertSize(props.config.height),
    borderRadius: sizeUtils.convertSize(props.config.borderRadius),
    padding: sizeUtils.convertSize(props.config.padding),
    margin: sizeUtils.convertSize(props.config.margin),
    display: 'flex',
    alignItems: 'center',
    overflow: 'hidden',
  };
});

// 文本容器样式
const textContainerStyle = computed(() => {
  return {
    flex: '1',
    overflow: 'hidden',
    whiteSpace: props.config.scrollable ? 'nowrap' : 'normal',
  };
});

// 文本样式
const textStyle = computed(() => {
  return {
    color: colorUtils.getThemeColorOrDefault(props.config.textColor, theme.colors.text.primary),
    fontSize: sizeUtils.convertSize(props.config.fontSize),
    fontWeight: theme.fontWeight.normal,
    lineHeight: '1.4',
  };
});

// 处理点击事件
const handleClick = () => {
  if (!props.config.clickable) return;
  
  switch (props.config.clickType) {
    case 'navigate':
      if (props.config.url) {
        uni.navigateTo({
          url: props.config.url,
          fail: () => {
            uni.switchTab({ url: props.config.url });
          }
        });
      }
      break;
    case 'message':
      if (props.config.message) {
        uni.showToast({
          title: props.config.message,
          icon: 'none'
        });
      }
      break;
  }
};

// 处理关闭事件
const handleClose = () => {
  closed.value = true;
};
</script>

<style lang="scss" scoped>
.notice-component {
  width: 100%;
}

.notice-content {
  position: relative;
  box-sizing: border-box;
}

.notice-icon {
  margin-right: 8px;
  flex-shrink: 0;
}

.notice-text-container {
  position: relative;
}

.notice-text {
  display: block;
  word-break: break-all;
  
  &.scrolling-text {
    animation: scroll-text 10s linear infinite;
  }
}

.notice-close {
  margin-left: 8px;
  flex-shrink: 0;
  padding: 2px;
}

@keyframes scroll-text {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

// 响应式适配
@media screen and (max-width: 750px) {
  .notice-text {
    font-size: 14px;
  }
}
</style>
