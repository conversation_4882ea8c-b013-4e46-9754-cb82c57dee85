<template>
  <view class="product-list-component" :style="containerStyle">
    <!-- 标题栏 -->
    <view v-if="config.title" class="product-list-header">
      <text class="header-title">{{ config.title }}</text>
      <view 
        v-if="config.showMore" 
        class="header-more"
        @click="handleMoreClick"
      >
        <text class="more-text">{{ config.moreText || '更多' }}</text>
        <uv-icon name="arrow-right" :color="theme.colors.text.secondary" size="16" />
      </view>
    </view>
    
    <!-- 商品列表 -->
    <view class="product-list-content" :style="contentStyle">
      <view 
        class="product-item"
        v-for="product in config.products"
        :key="product.id"
        :style="itemStyle"
        @click="handleProductClick(product)"
      >
        <!-- 商品图片 -->
        <view class="product-image-wrapper">
          <uv-image
            :src="product.image"
            :width="imageSize"
            :height="imageSize"
            :borderRadius="theme.borderRadius.md"
            mode="aspectFill"
            :showLoading="true"
            :showError="true"
            :fade="true"
          />
          <!-- 标签 -->
          <view v-if="product.tags && product.tags.length" class="product-tags">
            <text 
              v-for="tag in product.tags.slice(0, 2)" 
              :key="tag"
              class="product-tag"
            >
              {{ tag }}
            </text>
          </view>
        </view>
        
        <!-- 商品信息 -->
        <view class="product-info">
          <text class="product-name">{{ product.name }}</text>
          
          <view class="product-price-wrapper">
            <text class="product-price">¥{{ formatPrice(product.price) }}</text>
            <text 
              v-if="product.originalPrice && product.originalPrice > product.price"
              class="product-original-price"
            >
              ¥{{ formatPrice(product.originalPrice) }}
            </text>
          </view>
          
          <text v-if="product.sales" class="product-sales">
            已售{{ formatSales(product.sales) }}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { ProductListConfig } from '../../types/dynamic-page';
import { convertStyleToCSS, layoutUtils } from '../../utils/style-helper';
import { theme } from '../../config/theme';

interface Props {
  config: ProductListConfig['config'];
  style?: ProductListConfig['style'];
}

const props = defineProps<Props>();

// 容器样式
const containerStyle = computed(() => {
  const baseStyle = convertStyleToCSS(props.style);
  return {
    ...baseStyle,
    width: '100%',
  };
});

// 内容样式
const contentStyle = computed(() => {
  if (props.config.layout === 'grid') {
    return layoutUtils.createGridStyle(props.config.columns, theme.spacing.md);
  } else {
    return {
      display: 'flex',
      flexDirection: 'column',
      gap: `${theme.spacing.md}px`,
    };
  }
});

// 商品项样式
const itemStyle = computed(() => {
  const baseStyle = {
    backgroundColor: theme.colors.background.card,
    borderRadius: `${theme.borderRadius.lg}px`,
    padding: `${theme.spacing.md}px`,
    boxShadow: theme.shadows.sm,
    transition: 'all 0.2s ease',
    cursor: 'pointer',
  };
  
  if (props.config.layout === 'list') {
    return {
      ...baseStyle,
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'center',
    };
  }
  
  return baseStyle;
});

// 图片尺寸
const imageSize = computed(() => {
  return props.config.layout === 'list' ? 80 : 120;
});

// 格式化价格
const formatPrice = (price: number): string => {
  return price.toFixed(2);
};

// 格式化销量
const formatSales = (sales: number): string => {
  if (sales >= 10000) {
    return `${(sales / 10000).toFixed(1)}万`;
  }
  return sales.toString();
};

// 处理商品点击
const handleProductClick = (product: ProductListConfig['config']['products'][0]) => {
  if (product.link) {
    handleNavigation(product.link);
  }
};

// 处理更多点击
const handleMoreClick = () => {
  if (props.config.moreLink) {
    handleNavigation(props.config.moreLink);
  }
};

// 处理导航
const handleNavigation = (url: string) => {
  if (!url) return;
  
  uni.navigateTo({
    url: url,
    fail: () => {
      uni.switchTab({ 
        url: url,
        fail: () => {
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    }
  });
};
</script>

<style lang="scss" scoped>
.product-list-component {
  width: 100%;
  padding: 0 16px;
}

.product-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 4px;
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  color: v-bind('theme.colors.text.primary');
}

.header-more {
  display: flex;
  align-items: center;
  gap: 4px;
  
  &:active {
    opacity: 0.7;
  }
}

.more-text {
  font-size: 14px;
  color: v-bind('theme.colors.text.secondary');
}

.product-list-content {
  width: 100%;
}

.product-item {
  &:active {
    transform: scale(0.98);
    opacity: 0.9;
  }
}

.product-image-wrapper {
  position: relative;
  margin-bottom: 8px;
}

.product-tags {
  position: absolute;
  top: 4px;
  left: 4px;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.product-tag {
  background-color: v-bind('theme.colors.error');
  color: white;
  font-size: 10px;
  padding: 2px 4px;
  border-radius: 2px;
  line-height: 1;
}

.product-info {
  flex: 1;
}

.product-name {
  font-size: 14px;
  color: v-bind('theme.colors.text.primary');
  line-height: 1.4;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-price-wrapper {
  display: flex;
  align-items: baseline;
  gap: 8px;
  margin-bottom: 4px;
}

.product-price {
  font-size: 16px;
  font-weight: 600;
  color: v-bind('theme.colors.error');
}

.product-original-price {
  font-size: 12px;
  color: v-bind('theme.colors.text.secondary');
  text-decoration: line-through;
}

.product-sales {
  font-size: 12px;
  color: v-bind('theme.colors.text.secondary');
}

// 列表布局特殊样式
.product-list-content:not([style*="grid"]) {
  .product-item {
    flex-direction: row;
    
    .product-image-wrapper {
      margin-right: 12px;
      margin-bottom: 0;
    }
  }
}
</style>
