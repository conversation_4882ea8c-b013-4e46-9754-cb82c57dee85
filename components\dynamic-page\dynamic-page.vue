<template>
  <view class="dynamic-page" :style="pageContainerStyle">
    <view
      v-for="component in sortedComponents"
      :key="component.id"
      class="dynamic-component-wrapper"
    >
      <!-- 公告组件 -->
      <notice-component
        v-if="component.type === 'notice'"
        :config="component.config"
        :style="component.style"
        @component-click="handleComponentClick"
        @component-error="handleComponentError"
      />

      <!-- 图片组件 -->
      <image-component
        v-else-if="component.type === 'image'"
        :config="component.config"
        :style="component.style"
        @component-click="handleComponentClick"
        @component-error="handleComponentError"
      />

      <!-- 轮播组件 -->
      <banner-component
        v-else-if="component.type === 'banner'"
        :config="component.config"
        :style="component.style"
        @component-click="handleComponentClick"
        @component-error="handleComponentError"
      />

      <!-- 九宫格菜单组件 -->
      <grid-menu-component
        v-else-if="component.type === 'grid-menu'"
        :config="component.config"
        :style="component.style"
        @component-click="handleComponentClick"
        @component-error="handleComponentError"
      />

      <!-- 商品列表组件 -->
      <product-list-component
        v-else-if="component.type === 'product-list'"
        :config="component.config"
        :style="component.style"
        @component-click="handleComponentClick"
        @component-error="handleComponentError"
      />

      <!-- 秒杀组件 -->
      <flash-sale-component
        v-else-if="component.type === 'flash-sale'"
        :config="component.config"
        :style="component.style"
        @component-click="handleComponentClick"
        @component-error="handleComponentError"
      />

      <!-- 团购组件 -->
      <group-buy-component
        v-else-if="component.type === 'group-buy'"
        :config="component.config"
        :style="component.style"
        @component-click="handleComponentClick"
        @component-error="handleComponentError"
      />

      <!-- 未知组件类型 -->
      <view v-else class="component-error">
        <view class="error-icon">⚠️</view>
        <text class="error-text">未知组件类型: {{ component.type }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue';
import { PageConfig, ComponentConfig } from '../../types/dynamic-page';
import { pageStyleUtils } from '../../utils/style-helper';

// 导入所有动态组件
import NoticeComponent from '../dynamic-components/notice-component.vue';
import ImageComponent from '../dynamic-components/image-component.vue';
import BannerComponent from '../dynamic-components/banner-component.vue';
import GridMenuComponent from '../dynamic-components/grid-menu-component.vue';
import ProductListComponent from '../dynamic-components/product-list-component.vue';
import FlashSaleComponent from '../dynamic-components/flash-sale-component.vue';
import GroupBuyComponent from '../dynamic-components/group-buy-component.vue';

interface Props {
  pageConfig: PageConfig;
}

interface Emits {
  (e: 'component-click', data: { component: ComponentConfig; action: string; payload?: any }): void;
  (e: 'component-error', data: { component: ComponentConfig; error: Error }): void;
  (e: 'page-ready'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 按sort字段排序的组件列表
const sortedComponents = computed(() => {
  return [...props.pageConfig.components].sort((a, b) => a.sort - b.sort);
});

// 页面容器样式
const pageContainerStyle = computed(() => {
  return pageStyleUtils.getResponsiveContainerStyle(props.pageConfig.pageConfig);
});

// 处理组件点击事件
const handleComponentClick = (data: { action: string; payload?: any }) => {
  // 这里可以添加全局的组件点击处理逻辑
  console.log('Component clicked:', data);

  // 发送事件给父组件
  emit('component-click', {
    component: data.payload?.component,
    action: data.action,
    payload: data.payload,
  });
};

// 处理组件错误
const handleComponentError = (data: { error: Error; component?: ComponentConfig }) => {
  console.error('Component error:', data.error);

  // 发送错误事件给父组件
  emit('component-error', {
    component: data.component,
    error: data.error,
  });

  // 显示错误提示
  uni.showToast({
    title: '组件加载失败',
    icon: 'none',
    duration: 2000,
  });
};

// 页面准备就绪
const handlePageReady = () => {
  emit('page-ready');
};

// 组件挂载后触发页面准备就绪事件
onMounted(() => {
  handlePageReady();
});
</script>

<style lang="scss" scoped>
.dynamic-page {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.dynamic-component-wrapper {
  width: 100%;
  margin-bottom: 8px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

// 全局组件样式
:deep(.dynamic-component) {
  width: 100%;
  box-sizing: border-box;
}

// 响应式适配
@media screen and (max-width: 750px) {
  .dynamic-page {
    padding: 0;
  }
  
  .dynamic-component-wrapper {
    margin-bottom: 6px;
  }
}

// 加载状态样式
.component-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  background-color: #f9f9f9;
  border-radius: 8px;
  margin: 8px 16px;
}

.component-error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100px;
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 8px;
  margin: 8px 16px;
  
  .error-icon {
    font-size: 24px;
    color: #ff4d4f;
    margin-bottom: 8px;
  }
  
  .error-text {
    font-size: 14px;
    color: #ff4d4f;
  }
}
</style>
