/**
 * 播放音频
 * 注意：
 * 1. 返回值不能赋值给ref对象，否则innerAudioContent的方法可能失效
 */

import { MaybeRef, onMounted, Ref, ref, unref, watch } from "vue";
import {
    InnerAudioContextOptions,
    InnerAudioContextHooks,
    InnerAudioContextPublicProps,
} from "../utils/globalAudioContext";

export interface InnerAudioContextReturns {
    duration: Ref<number>;
    paused: Ref<boolean>;
    currentTime: Ref<number>;
    ending: Ref<boolean>;
    getContext: () => UniApp.InnerAudioContext;
    play: () => void;
    pause: () => void;
    seek: (position: number) => void;
    stop: () => void;
    destroy: () => void;
}

const DefaultInnerAudioContextPublicProps = {
    startTime: 0,
    autoplay: true,
    loop: false,
    obeyMuteSwitch: true,
    volume: undefined,
    sessionCategory: "playback",
    playbackRate: 1.0,
};

export default function useAudioContext(
    tempFilePath: MaybeRef<string>,
    options: MaybeRef<InnerAudioContextOptions>
): InnerAudioContextReturns {
    let audioContext: UniApp.InnerAudioContext;
    const duration = ref<number>(0);
    const paused = ref<boolean>(false);
    const currentTime = ref<number>(0);
    const ending = ref<boolean>(true);

    const DefaultInnerAudioContextHooks = {
        onCanplay: () => {
            ending.value = false;
            duration.value = audioContext.duration;
            paused.value = audioContext.paused;
            currentTime.value = audioContext.currentTime;
        },
        onPlay: () => {
            paused.value = audioContext.paused;
        },
        onPause: () => {
            paused.value = audioContext.paused;
        },
        onStop: () => {
            paused.value = audioContext.paused;
            currentTime.value = audioContext.currentTime;
        },
        onEnded: () => {
            ending.value = true;
        },
        onTimeUpdate: () => {
            currentTime.value = audioContext.currentTime;
        },
    };

    function initContext() {
        createContext();
        settingOptions();
    }

    function createContext() {
        if (!audioContext) {
            audioContext = uni.createInnerAudioContext();
        }

        audioContext.src = unref(tempFilePath);
    }

    function settingOptions() {
        if (!audioContext) {
            createContext();
        }

        for (const key of InnerAudioContextPublicProps) {
            audioContext[key] =
                unref(options)?.[key] ??
                DefaultInnerAudioContextPublicProps[key];
        }

        const defaultHooks = Object.keys(DefaultInnerAudioContextHooks);
        for (const hook of InnerAudioContextHooks) {
            const customHook = unref(options)?.[hook];
            if (defaultHooks.includes(hook)) {
                audioContext[hook]?.(() => {
                    DefaultInnerAudioContextHooks[hook]?.(...arguments);
                    customHook?.(...arguments);
                });
            } else if (customHook) {
                audioContext[hook]?.(customHook);
            }
        }
    }

    function getContext() {
        if (!audioContext) {
            initContext();
        }

        return audioContext;
    }

    function play() {
        if (audioContext && audioContext.paused) {
            audioContext.play();
        }
    }

    function pause() {
        if (audioContext && !audioContext.paused) {
            audioContext.pause();
        }
    }

    function stop() {
        if (audioContext && !audioContext.paused) {
            audioContext.stop();
        }
    }

    function seek(position: number) {
        if (audioContext) {
            audioContext.seek(position);
        }
    }

    function destroy() {
        if (audioContext) {
            audioContext.destroy();
            audioContext = undefined;
        }
    }

    watch(
        () => unref(tempFilePath),
        () => {
            createContext();
        }
    );

    watch(
        () => unref(options),
        () => {
            settingOptions();
        }
    );

    onMounted(() => {
        initContext();
    });

    return {
        duration,
        paused,
        currentTime,
        ending,
        getContext,
        play,
        pause,
        seek,
        stop,
        destroy,
    };
}
