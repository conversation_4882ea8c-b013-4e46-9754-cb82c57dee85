<template>
    <view class="content">
        <view class="preview" @click="onImagePreview()">
            <image class="inquiry" :src="state.url" mode="widthFix"></image>
        </view>
   <!--     <view class="name" v-if="state.url">
            <view class="label">图片名称</view>
            <view class="file_name">{{ state.url }}</view>
        </view> -->
        <view class="again" @click="onTakePicture()">
            <image class="again_icon" src="/static/again.png"></image>
            <view class="again_upload">重新上传</view>
        </view>
    </view>
    <view class="btn-contain">
        <view class="btns">
            <view class="cancel" @click="onCancelUpload()">取消</view>
            <view class="submit" @click="handleSubmit()">提交</view>
        </view>
        <loading-page :loading="loading"></loading-page>
    </view>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue';
import { chooseMedia, navigateBack, previewImage, redirectTo, showToast } from '@/utils/uni-promise';
import { onLoad } from '@dcloudio/uni-app';
import { uploadImage } from '@/api/file';
import { batchUploadFileAndAnalysis, quotationInquiryParseInquiry, uploadFileAndAnalysis } from '@/api/quotation';
import { getLatestVersion } from '@/api/product-version';
import { useLoadingFn } from '../../hooks';

interface IQueryParams {
    url?: string;
    fileName?: string;
    urls?: Array<string>;
}

const state = reactive<IQueryParams>({});
const loading = ref(false);
const handleSubmit = useLoadingFn(onSubmit, loading);

onLoad((query: IQueryParams) => {
    if (query && Object.keys(query).length) {
        state.urls = query.url.split(',');
        state.url = state.urls?.[0];
    }
});

const onCancelUpload = () => {
    navigateBack();
};

async function onSubmit() {
    const taskId = await batchUploadFileAndAnalysis(state.urls);
    if (!taskId) {
        return;
    }
    redirectTo({ url: `/pages/inquiry/index?taskId=${taskId}` });
}

const onTakePicture = async () => {
    const res = await chooseMedia({ mediaType: ['image'], sourceType: ['camera', 'album'], sizeType: ['compressed'], camera: 'back', count: 3 });
    if (res.errMsg != 'chooseMedia:ok') {
        showToast({ title: '请拍照或者选择图片', icon: 'none' });
        return;
    }
    state.fileName = '';
    state.urls = res.tempFiles.map((x) => x.tempFilePath);
    state.url = state.urls?.[0];
};

const onImagePreview = () => {
    previewImage({
        current: state.urls.indexOf(state.url),
        urls: state.urls
    });
};
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>