<template>
  <view class="service-product-component" :style="containerStyle">
    <view class="service-wrapper" :style="wrapperStyle">
      <!-- 服务头部 -->
      <view class="service-header" :style="headerStyle">
        <view class="header-content">
          <view class="header-left">
            <view class="service-icon">
              <uv-icon name="service" :color="config.iconColor || theme.colors.primary" size="20" />
            </view>
            <view class="header-text">
              <text class="header-title">{{ config.title || '服务专区' }}</text>
              <text class="header-desc">{{ config.description || '专业服务，品质保障' }}</text>
            </view>
          </view>
          
          <view class="header-right">
            <uv-button
              text="更多服务"
              type="info"
              size="mini"
              plain
              @click="handleViewMore"
            />
          </view>
        </view>
      </view>
      
      <!-- 服务商品列表 -->
      <view class="services-content" :style="contentStyle">
        <view
          v-if="config.displayMode === 'grid'"
          class="services-grid"
          :style="gridStyle"
        >
          <view
            v-for="(service, index) in visibleServices"
            :key="service.id"
            class="service-item"
            :style="serviceItemStyle"
            @click="handleServiceClick(service, index)"
          >
            <!-- 服务图片 -->
            <view class="service-image-wrapper">
              <uv-image
                :src="service.image"
                :width="'100%'"
                :height="'100%'"
                :border-radius="4"
                mode="aspectFill"
                class="service-image"
              />
              
              <!-- 服务标签 -->
              <view v-if="service.tag" class="service-tag" :style="tagStyle">
                <text class="tag-text">{{ service.tag }}</text>
              </view>
            </view>
            
            <!-- 服务信息 -->
            <view class="service-info">
              <text class="service-title">{{ service.title }}</text>
              <text class="service-subtitle">{{ service.subtitle }}</text>
              
              <!-- 服务特色 -->
              <view v-if="service.features && service.features.length > 0" class="service-features">
                <text
                  v-for="feature in service.features.slice(0, 2)"
                  :key="feature"
                  class="feature-tag"
                >{{ feature }}</text>
              </view>
              
              <!-- 价格信息 -->
              <view class="price-container">
                <text class="service-price">¥{{ service.price }}</text>
                <text v-if="service.originalPrice" class="original-price">¥{{ service.originalPrice }}</text>
                <text class="price-unit">{{ service.priceUnit || '起' }}</text>
              </view>
              
              <!-- 服务评价 -->
              <view v-if="config.showRating && service.rating" class="service-rating">
                <uv-rate
                  :value="service.rating"
                  :count="5"
                  :size="12"
                  :disabled="true"
                  :color="config.ratingColor || '#ffa500'"
                />
                <text class="rating-text">{{ service.rating }}</text>
                <text class="review-count">({{ service.reviewCount || 0 }}评价)</text>
              </view>
              
              <!-- 预约按钮 -->
              <uv-button
                text="立即预约"
                :color="config.buttonColor || theme.colors.primary"
                size="mini"
                :custom-style="bookButtonStyle"
                @click.stop="handleBookService(service)"
              />
            </view>
          </view>
        </view>
        
        <!-- 列表布局 -->
        <view v-else class="services-list">
          <view
            v-for="(service, index) in visibleServices"
            :key="service.id"
            class="service-item-list"
            :style="serviceListItemStyle"
            @click="handleServiceClick(service, index)"
          >
            <view class="service-image-wrapper-list">
              <uv-image
                :src="service.image"
                :width="'80px'"
                :height="'80px'"
                :border-radius="4"
                mode="aspectFill"
                class="service-image"
              />
              
              <view v-if="service.tag" class="service-tag" :style="tagStyle">
                <text class="tag-text">{{ service.tag }}</text>
              </view>
            </view>
            
            <view class="service-info-list">
              <view class="service-main-info">
                <text class="service-title">{{ service.title }}</text>
                <text class="service-subtitle">{{ service.subtitle }}</text>
                
                <view v-if="service.features && service.features.length > 0" class="service-features">
                  <text
                    v-for="feature in service.features.slice(0, 3)"
                    :key="feature"
                    class="feature-tag"
                  >{{ feature }}</text>
                </view>
              </view>
              
              <view class="service-action-info">
                <view class="price-container">
                  <text class="service-price">¥{{ service.price }}</text>
                  <text v-if="service.originalPrice" class="original-price">¥{{ service.originalPrice }}</text>
                  <text class="price-unit">{{ service.priceUnit || '起' }}</text>
                </view>
                
                <view v-if="config.showRating && service.rating" class="service-rating">
                  <uv-rate
                    :value="service.rating"
                    :count="5"
                    :size="10"
                    :disabled="true"
                    :color="config.ratingColor || '#ffa500'"
                  />
                  <text class="rating-text">{{ service.rating }}</text>
                </view>
                
                <uv-button
                  text="预约"
                  :color="config.buttonColor || theme.colors.primary"
                  size="mini"
                  :custom-style="bookButtonStyle"
                  @click.stop="handleBookService(service)"
                />
              </view>
            </view>
          </view>
        </view>
        
        <!-- 查看更多 -->
        <view v-if="hasMoreServices" class="more-action">
          <uv-button
            text="查看全部服务"
            type="info"
            size="small"
            plain
            @click="handleViewMore"
          />
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { convertStyleToCSS } from '../../utils/style-helper';
import { theme } from '../../config/theme';

interface ServiceProduct {
  id: string;
  title: string;
  subtitle: string;
  image: string;
  price: number;
  originalPrice?: number;
  priceUnit: string;
  tag?: string;
  features: string[];
  rating?: number;
  reviewCount?: number;
  link: string;
}

interface ServiceProductConfig {
  title: string;
  description: string;
  displayMode: 'grid' | 'list';
  columns: number;
  maxVisibleCount: number;
  services: ServiceProduct[];
  backgroundColor: string;
  headerBackgroundColor: string;
  iconColor: string;
  tagBackgroundColor: string;
  tagTextColor: string;
  buttonColor: string;
  ratingColor: string;
  spacing: number;
  margin: number;
  showRating: boolean;
}

interface Props {
  config: ServiceProductConfig;
  style?: any;
}

const props = defineProps<Props>();

// 容器样式
const containerStyle = computed(() => {
  if (!props.style) {
    return { width: '100%' };
  }
  
  const baseStyle = convertStyleToCSS(props.style);
  
  const processedStyle = {};
  Object.keys(baseStyle).forEach(key => {
    processedStyle[key] = String(baseStyle[key]);
  });
  
  return {
    ...processedStyle,
    width: '100%',
  };
});

// 包装器样式
const wrapperStyle = computed(() => {
  return {
    backgroundColor: props.config.backgroundColor || '#ffffff',
    borderRadius: '12px',
    margin: `${props.config.margin || theme.spacing.sm}px`,
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
    overflow: 'hidden',
  };
});

// 头部样式
const headerStyle = computed(() => {
  return {
    backgroundColor: props.config.headerBackgroundColor || '#f8f9fa',
    padding: `${theme.spacing.md}px`,
    borderBottom: '1px solid #f0f0f0',
  };
});

// 内容样式
const contentStyle = computed(() => {
  return {
    padding: `${theme.spacing.md}px`,
  };
});

// 网格样式
const gridStyle = computed(() => {
  const columns = props.config.columns || 2;
  return {
    display: 'grid',
    gridTemplateColumns: `repeat(${columns}, 1fr)`,
    gap: `${props.config.spacing || theme.spacing.sm}px`,
  };
});

// 服务项样式
const serviceItemStyle = computed(() => {
  return {
    backgroundColor: '#ffffff',
    borderRadius: '8px',
    border: '1px solid #f0f0f0',
    overflow: 'hidden',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
  };
});

// 列表项样式
const serviceListItemStyle = computed(() => {
  return {
    display: 'flex',
    backgroundColor: '#ffffff',
    borderRadius: '8px',
    border: '1px solid #f0f0f0',
    padding: `${theme.spacing.sm}px`,
    marginBottom: `${props.config.spacing || theme.spacing.sm}px`,
    cursor: 'pointer',
    transition: 'all 0.2s ease',
  };
});

// 标签样式
const tagStyle = computed(() => {
  return {
    backgroundColor: props.config.tagBackgroundColor || '#1890ff',
    color: props.config.tagTextColor || '#ffffff',
  };
});

// 预约按钮样式
const bookButtonStyle = computed(() => {
  return {
    backgroundColor: props.config.buttonColor || theme.colors.primary,
    color: '#ffffff',
    borderRadius: '4px',
    fontSize: '12px',
    height: '28px',
    marginTop: '8px',
  };
});

// 可见服务
const visibleServices = computed(() => {
  const maxCount = props.config.maxVisibleCount || 4;
  return props.config.services.slice(0, maxCount);
});

// 是否有更多服务
const hasMoreServices = computed(() => {
  const maxCount = props.config.maxVisibleCount || 4;
  return props.config.services.length > maxCount;
});

// 处理服务点击
const handleServiceClick = (service: ServiceProduct, index: number) => {
  if (service.link) {
    uni.navigateTo({
      url: service.link,
      fail: () => {
        uni.showToast({ title: '页面跳转失败', icon: 'none' });
      }
    });
  }
};

// 处理预约服务
const handleBookService = (service: ServiceProduct) => {
  uni.showToast({
    title: `预约服务: ${service.title}`,
    icon: 'success'
  });
  
  // 可以在这里添加预约逻辑
  uni.$emit('service-book', { service });
};

// 处理查看更多
const handleViewMore = () => {
  uni.navigateTo({
    url: '/pages/service/list',
    fail: () => {
      uni.showToast({ title: '查看更多服务', icon: 'none' });
    }
  });
};
</script>

<style lang="scss" scoped>
.service-product-component {
  width: 100%;
}

.service-wrapper {
  width: 100%;
}

.service-header {
  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .header-left {
    display: flex;
    align-items: center;
    flex: 1;
  }
  
  .service-icon {
    margin-right: 12px;
  }
  
  .header-text {
    flex: 1;
    
    .header-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      display: block;
      margin-bottom: 4px;
    }
    
    .header-desc {
      font-size: 12px;
      color: #666;
      display: block;
    }
  }
  
  .header-right {
    flex-shrink: 0;
  }
}

.services-content {
  width: 100%;
}

.service-item {
  &:active {
    transform: scale(0.98);
    opacity: 0.9;
  }
}

.service-image-wrapper {
  position: relative;
  width: 100%;
  height: 100px;
  overflow: hidden;
}

.service-image-wrapper-list {
  position: relative;
  margin-right: 12px;
  flex-shrink: 0;
}

.service-image {
  width: 100%;
  height: 100%;
}

.service-tag {
  position: absolute;
  top: 4px;
  left: 4px;
  padding: 2px 4px;
  border-radius: 2px;
  
  .tag-text {
    font-size: 10px;
    font-weight: 600;
  }
}

.service-info {
  padding: 8px;
}

.service-info-list {
  flex: 1;
  display: flex;
  justify-content: space-between;
}

.service-main-info {
  flex: 1;
}

.service-action-info {
  flex-shrink: 0;
  text-align: right;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-end;
}

.service-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  display: block;
}

.service-subtitle {
  font-size: 12px;
  color: #666;
  margin-bottom: 6px;
  display: block;
}

.service-features {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-bottom: 6px;
}

.feature-tag {
  background-color: #f0f8ff;
  color: #1890ff;
  font-size: 10px;
  padding: 2px 4px;
  border-radius: 2px;
}

.price-container {
  display: flex;
  align-items: baseline;
  margin-bottom: 6px;
}

.service-price {
  font-size: 16px;
  color: #ff4757;
  font-weight: 600;
  margin-right: 4px;
}

.original-price {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
  margin-right: 4px;
}

.price-unit {
  font-size: 12px;
  color: #666;
}

.service-rating {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 6px;
}

.rating-text {
  font-size: 12px;
  color: #ffa500;
  font-weight: 600;
}

.review-count {
  font-size: 10px;
  color: #999;
}

.service-item-list {
  &:active {
    background-color: #f8f9fa;
  }
  
  &:last-child {
    margin-bottom: 0;
  }
}

.more-action {
  margin-top: 16px;
  text-align: center;
}

// 响应式适配
@media screen and (max-width: 750px) {
  .service-item {
    .service-title {
      font-size: 13px;
    }
    
    .service-price {
      font-size: 14px;
    }
  }
  
  .service-item-list {
    .service-image-wrapper-list {
      margin-right: 8px;
    }
  }
}
</style>
