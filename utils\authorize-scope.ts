import { authorize, showModal, openSetting } from './uni-promise';

export const getisAuthorizePermission = async (permission: string) => {
    const authorizerRes = await authorize({ scope: `scope.${permission}` });
    console.log('authorizerRes', authorizerRes);
};

export const checkAuthorizeScope = async (permission: string, permissionName: string) => {
    const scopePermission = `scope.${permission}`;
    try {
        const authorizerRes = await authorize({ scope: scopePermission });
        return authorizerRes.errMsg === 'authorize:ok';
    } catch ({ errMsg }) {
        console.log(errMsg);
        //提示授权
        const modalRes = await showModal({
            title: `${permissionName}权限授权`,
            content: `${permissionName}授权,拒绝后将无法使用`,
            confirmText: '授权'
        });
        if (modalRes.cancel) {
            uni.showToast({ title: `请授权${permissionName}权限`, icon: 'error' });
            return false;
        }

        const openSettingRes = await openSetting();
        if (!openSettingRes.authSetting[scopePermission]) {
            uni.showToast({ title: `请授权${permissionName}权限`, icon: 'error' });
            return false;
        }
        return openSettingRes[scopePermission];
    }
};
