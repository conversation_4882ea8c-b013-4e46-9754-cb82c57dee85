<template>
    <view class="test-page">
        <view class="test-header">
            <text class="test-title">商品组件居中测试</text>
            <text class="test-desc">测试商品组件在页面padding中的居中效果</text>
        </view>
        
        <view class="test-controls">
            <button @click="toggleColumns" class="control-btn">
                切换列数 (当前: {{ currentColumns }}列)
            </button>
            <button @click="toggleLayout" class="control-btn">
                切换布局 (当前: {{ currentLayout }})
            </button>
        </view>
        
        <!-- 使用动态页面组件渲染商品 -->
        <view class="dynamic-page-container" :style="pageContainerStyle">
            <view class="page-content">
                <view class="dynamic-component-wrapper" :style="getComponentWrapperStyle(productComponent)">
                    <product-component 
                        :config="productComponent.config" 
                        :style="getComponentStyle(productComponent)" 
                    />
                </view>
            </view>
        </view>
        
        <view class="test-info">
            <text class="info-title">测试说明：</text>
            <text class="info-item">1. 商品应该在页面padding内居中显示</text>
            <text class="info-item">2. 商品之间应该有适当的间距</text>
            <text class="info-item">3. 在不同列数下都应该保持居中</text>
            <text class="info-item">4. 移动端适配正常</text>
        </view>
    </view>
</template>

<script setup>
import { ref, computed } from 'vue';
import ProductComponent from '../../components/dynamic-components/product-component.vue';

const currentColumns = ref(2);
const currentLayout = ref('grid');

// 测试用的商品数据
const productComponent = computed(() => ({
    id: 'product-test',
    type: 'product',
    config: {
        displayMode: currentLayout.value,
        backgroundColor: '#ffffff',
        borderRadius: 8,
        showBorder: false,
        borderColor: '#e5e7eb',
        outerMargin: 0,
        innerPadding: 0,
        layout: currentLayout.value,
        columns: currentColumns.value,
        showPrice: true,
        showTitle: true,
        showRating: true,
        products: [
            {
                id: '1',
                title: '测试商品1 - 这是一个很长的商品标题用来测试换行效果',
                image: 'https://m.360buyimg.com/seckillcms/s150x150_jfs/t20280409/276203/1/18347/52095/67f788dcF34d4e028/f5f4b1484638c602.jpg',
                price: 99.99,
                originalPrice: 199.99,
                rating: 4.5,
                link: ''
            },
            {
                id: '2',
                title: '测试商品2',
                price: 199.99,
                originalPrice: 299.99,
                image: 'https://m.360buyimg.com/seckillcms/s150x150_jfs/t1/149444/5/43456/63752/66d6744aFcf132eca/885f81c729aeb1ca.jpg',
                rating: 4.8,
                link: ''
            },
            {
                id: '3',
                title: '测试商品3',
                price: 299.99,
                originalPrice: 399.99,
                image: 'https://m.360buyimg.com/seckillcms/s150x150_jfs/t20280409/276203/1/18347/52095/67f788dcF34d4e028/f5f4b1484638c602.jpg',
                rating: 4.2,
                link: ''
            },
            {
                id: '4',
                title: '测试商品4',
                price: 399.99,
                originalPrice: 499.99,
                image: 'https://m.360buyimg.com/seckillcms/s150x150_jfs/t1/149444/5/43456/63752/66d6744aFcf132eca/885f81c729aeb1ca.jpg',
                rating: 4.7,
                link: ''
            }
        ]
    },
    style: {
        padding: {
            top: 0,
            right: 0,
            bottom: 0,
            left: 0
        },
        margin: {
            top: 0,
            right: 0,
            bottom: 0,
            left: 0
        },
        borderRadius: 0
    },
    background: {
        type: 'solid',
        color: 'transparent'
    },
    sort: 0
}));

// 页面容器样式 - 模拟动态页面的样式
const pageContainerStyle = computed(() => {
    return {
        background: 'linear-gradient(to right, #ff6b6b, #4ecdc4, #45b7d1)',
        paddingTop: '14px',
        paddingRight: '12px',
        paddingBottom: '12px',
        paddingLeft: '14px',
        minHeight: '50vh',
        width: '100%',
        boxSizing: 'border-box',
    };
});

// 获取组件包装器样式
const getComponentWrapperStyle = (component) => {
    const style = {
        marginBottom: '8px',
    };
    
    if (component.background) {
        const bgStyle = getBackgroundStyle(component.background);
        Object.assign(style, bgStyle);
    }
    
    return style;
};

// 获取组件样式
const getComponentStyle = (component) => {
    const style = {};
    
    if (component.style) {
        const { padding, margin, borderRadius } = component.style;
        
        if (padding) {
            style.paddingTop = `${padding.top || 0}px`;
            style.paddingRight = `${padding.right || 0}px`;
            style.paddingBottom = `${padding.bottom || 0}px`;
            style.paddingLeft = `${padding.left || 0}px`;
        }
        
        if (margin) {
            style.marginTop = `${margin.top || 0}px`;
            style.marginRight = `${margin.right || 0}px`;
            style.marginBottom = `${margin.bottom || 0}px`;
            style.marginLeft = `${margin.left || 0}px`;
        }
        
        if (borderRadius) {
            style.borderRadius = `${borderRadius || 0}px`;
        }
    }
    
    return style;
};

// 获取背景样式
const getBackgroundStyle = (background) => {
    if (!background) return { backgroundColor: '#ffffff' };
    
    switch (background.type) {
        case 'solid':
            return { backgroundColor: background.color || '#ffffff' };
        default:
            return { backgroundColor: background.color || '#ffffff' };
    }
};

const toggleColumns = () => {
    currentColumns.value = currentColumns.value === 2 ? 3 : currentColumns.value === 3 ? 1 : 2;
};

const toggleLayout = () => {
    currentLayout.value = currentLayout.value === 'grid' ? 'list' : 'grid';
};
</script>

<style lang="scss" scoped>
.test-page {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100vh;
}

.test-header {
    text-align: center;
    margin-bottom: 20px;
    
    .test-title {
        font-size: 18px;
        font-weight: bold;
        color: #333;
        display: block;
        margin-bottom: 8px;
    }
    
    .test-desc {
        font-size: 14px;
        color: #666;
        display: block;
    }
}

.test-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 20px;
    
    .control-btn {
        padding: 8px 16px;
        background-color: #007aff;
        color: white;
        border: none;
        border-radius: 6px;
        font-size: 12px;
    }
}

.dynamic-page-container {
    border-radius: 12px;
    margin-bottom: 20px;
    overflow: hidden;
}

.test-info {
    background-color: white;
    padding: 15px;
    border-radius: 8px;
    
    .info-title {
        font-size: 16px;
        font-weight: bold;
        color: #333;
        display: block;
        margin-bottom: 10px;
    }
    
    .info-item {
        font-size: 14px;
        color: #666;
        display: block;
        margin-bottom: 5px;
        padding-left: 10px;
    }
}

// 模拟动态页面的样式
.page-content {
    width: 100%;
    position: relative;
}

.dynamic-component-wrapper {
    position: relative;
    width: 100%;
}
</style>
