/**
 * 录音管理
 * 一些使用限制🚫：
 * 1. RecorderManager 不能频繁start / stop
 * 2. RecorderManager 不能频繁pause / resume
 * 3. RecorderManager 要能监测音量，format必须是mp3格式，同时指定frameSize大小，否则无法触发onFrameRecorded事件进行音量判断
 */

import { onHide, onShow } from '@dcloudio/uni-app';
import { computed, Ref, ref, unref, watch } from 'vue';
export interface UseRecorderManagerProps {
    modelValue: boolean;
    duration?: number; // 指定录音的时长，单位 ms ，如果传入了合法的 duration ，在到达指定的 duration 后会自动停止录音，最大值 600000（10 分钟）,默认值 60000（1 分钟）	App、小程序支持
    sampleRate?: number; //	否	采样率，有效值 8000/16000/44100	App、小程序支持
    numberOfChannels?: number; //	Number	否	录音通道数，有效值 1/2	仅小程序支持
    encodeBitRate?: number; //	否	编码码率，有效值见下表格	仅小程序支持
    format?: 'aac' | 'mp3' | 'wav' | 'PCM'; //	否	音频格式，有效值 aac/mp3/wav/PCM。App默认值为mp3，小程序默认值aac	App、小程序支持
    frameSize?: number; //	ms 否	指定帧大小，单位 KB。传入 frameSize 后，每录制指定帧大小的内容后，会回调录制的文件内容，不指定则不会回调。暂仅支持 mp3 格式。	App、百度小程序不支持
    hideTips?: boolean; //	否	隐藏录音图标。	支付宝小程序10.1.85+
    audioSource?: string; //	否	指定录音的音频输入源。	微信小程序详见、支付宝小程序详见、百度小程序详见、快手小程序
    detectDecibel: boolean;
    transformText?: boolean;
    analysisVolumn?: boolean;
    defaultVolumeThreshold?: number; // 判断音量阈值（0, 100]
    maxVolumeThreshold?: number; // 音量最大阈值（0, 32767]
    weightVolumeThreshold?: number; // 音量权重 (0, 100]
    joinEnvironment?: number;
    countdown?: boolean;
    minDuration?: number; // 最小录制时长[0, 600000]
    customTransformText?: (res: any) => Promise<string>;
    customAnalysisSpeaking?: (res: any) => Promise<[boolean, number]>;
}

export interface UseRecorderManagerEmits {
    (e: 'onError', res: any): void;
    (e: 'onStart', res: any): void;
    (e: 'onStop' | 'onTimeout', tempFilePath: string, res: any): void;
    (e: 'onPause', res: any): void;
    (e: 'onResume', res: any): void;
    (e: 'onFrameRecorded', res: any): void;
    (e: 'onInterruptionBegin', res: any): void;
    (e: 'onInterruptionEnd', res: any): void;
    (e: 'onLack'): void;
}

export interface UseRecorderManagerExpose {
    recorderManager: UniApp.RecorderManager;
    recordingCancel: Ref<boolean>;
    recordingError: Ref<boolean>;
    recording: Ref<boolean>;
    stoping: Ref<boolean>;
    pausing: Ref<boolean>;
    isSpeaking: Ref<boolean>;
    isTimeout: Ref<boolean>;
    isLack: Ref<boolean>;
    speakVolume: Ref<number>;
    audioPath: Ref<string>;
    transformText: Ref<string>;
    countdownValue: Ref<number>; // 录音倒计时 单位m
    finalMinDuration: Ref<number>;
    start: (callback?: (res: any) => void) => void;
    stop: (callback?: (tempFilePath: string, res: any) => void) => void;
    pause: (callback?: (res: any) => void) => void;
    resume: (callback?: (res: any) => void) => void;
}

export function useRecorderManager(props: UseRecorderManagerProps, emits: UseRecorderManagerEmits): UseRecorderManagerExpose {
    let callbacks = {
        start: undefined,
        stop: undefined,
        pause: undefined,
        resume: undefined
    };
    let defaultVolumeThreshold = Math.max(1, Math.min(32767, props.defaultVolumeThreshold ?? 1000));
    let coutdownInterval: ReturnType<typeof setInterval>;
    let initialVolumeSum = 0;
    let initialVolumeCount = 0;
    let volumeThreshold = defaultVolumeThreshold;
    let allowMaxVolume = Math.max(1, Math.min(32767, props.maxVolumeThreshold ?? 32767));
    let weightVolume = Math.max(1, Math.min(100, props.weightVolumeThreshold ?? 100));
    const recorderManager: UniApp.RecorderManager = uni.getRecorderManager();

    const recordingCancel = ref(false);
    const recordingError = ref(false);
    const recording = ref(false);
    const pausing = ref(false);
    const stoping = ref(false);
    const isSpeaking = ref(false);
    const isTimeout = ref(false);
    const isLack = ref(false);
    const speakVolume = ref(0);
    const audioPath = ref('');
    const transformText = ref('');
    const countdownValue = ref(0);

    const finaljoinEnvironment = computed<number>(() => props.joinEnvironment ?? 0);
    const finalTransformText = computed<boolean>(() => props.transformText ?? false);
    const finalAnalysisVolumn = computed<boolean>(() => props.analysisVolumn ?? false);
    const finalMinDuration = computed<number>(() => Math.max(0, props.minDuration ?? 0));
    const finalyDuration = computed<number>(() => props.duration ?? 600000);

    const recorderManagerOptions = computed<UniApp.RecorderManagerStartOptions>(() => {
        return {
            duration: unref(finalyDuration) + 400, // 400 设备录音超时差异 - 超时自动停止录制可能不足设置的超时时间，有时还会超出
            sampleRate: props.sampleRate ?? 44100,
            numberOfChannels: props.numberOfChannels ?? 1,
            encodeBitRate: props.encodeBitRate ?? 192000,
            format: props.format ?? 'mp3',
            frameSize: props.frameSize ?? 8,
            hideTips: props.hideTips ?? true,
            audioSource: props.audioSource ?? 'auto',
            detectDecibel: props.detectDecibel ?? false
        };
    });

    watch(
        () => props.modelValue,
        (val) => {
            if (val) {
                // 打开录音
                start();
            } else {
                // 关闭录音
                stop();
            }
        },
        {
            immediate: true
        }
    );

    recorderManager.onError((result: any) => {
        console.error('录音异常', result);
        recordingError.value = true;
        emits('onError', result);
    });

    recorderManager.onStart((result: any) => {
        // console.log("录音开始");
        if (pausing.value) {
            pausing.value = false;
        } else {
            afterStart();
            callbacks.start && callbacks.start(result);
        }
        emits('onStart', result);
    });

    recorderManager.onStop((result: any) => {
        console.log('录音结束', result, result.duration);
        if (coutdownInterval) {
            clearInterval(coutdownInterval);
        }
        recording.value = false;
        stoping.value = false;
        isSpeaking.value = false;
        speakVolume.value = 1;
        const stopFlag = recordingCancel.value || recordingError.value;
        const shouldSave = !stopFlag && result.duration && result.duration >= unref(finalMinDuration) && !!result.fileSize;
        audioPath.value = shouldSave ? result.tempFilePath : '';
        if (unref(finalTransformText) && shouldSave) {
            toTransformText(result);
        }
        if (result.duration < unref(finalMinDuration)) {
            // 录制时长不足
            isLack.value = true;
            emits('onLack');
        }
        if (!stopFlag && result.duration > unref(finalyDuration)) {
            // 录制超时
            isTimeout.value = true;
            emits('onTimeout', audioPath.value, shouldSave ? result : null);
        }
        callbacks.stop && callbacks.stop(audioPath.value, shouldSave ? result : null);
        emits('onStop', audioPath.value, shouldSave ? result : null);
    });

    recorderManager.onPause((result: any) => {
        // console.log("录音暂停");
        if (coutdownInterval) {
            clearInterval(coutdownInterval);
        }
        pausing.value = true;
        callbacks.pause && callbacks.pause(result);
        emits('onPause', result);
    });

    // @ts-ignore
    recorderManager.onResume((result: any) => {
        // console.log("录音继续");
        // pausing.value = false; // 触发onStart
        callbacks.resume && callbacks.resume(result);
        emits('onResume', result);
    });

    recorderManager.onFrameRecorded((result: any) => {
        // console.log("录音中", result, unref(finalAnalysisVolumn));
        if (unref(finalAnalysisVolumn)) {
            toAnalysisSpeaking(result);
        } else {
            isSpeaking.value = true; // 默认
            speakVolume.value = weightVolume; // 默认
        }
        emits('onFrameRecorded', result);
    });

    // @ts-ignore
    recorderManager.onInterruptionBegin((result: any) => {
        // console.log("录音中断");
        emits('onInterruptionBegin', result);
    });

    // @ts-ignore
    recorderManager.onInterruptionEnd((result: any) => {
        emits('onInterruptionEnd', result);
    });

    onShow(() => {
        recordingCancel.value = false;
    });

    onHide(() => {
        recordingCancel.value = true;
        // 直接取消录音
        stop();
    });

    // 开启录音后重置内容
    function afterStart() {
        initialVolumeSum = 0;
        initialVolumeCount = 0;
        volumeThreshold = defaultVolumeThreshold;
        recordingError.value = false;
        recordingCancel.value = false;
        recording.value = true;
        pausing.value = false;
        stoping.value = false;
        isSpeaking.value = false;
        isTimeout.value = false;
        isLack.value = false;
        speakVolume.value = 0;
        audioPath.value = '';
        if (props.countdown) {
            countdownValue.value = countdownValue.value || unref(finalyDuration) / 1000; // - 1; // 倒计时时间
            coutdownInterval = setInterval(() => {
                countdownValue.value = Math.max(0, countdownValue.value - 1);
                if (countdownValue.value <= 0) {
                    clearInterval(coutdownInterval);
                }
            }, 1000); // 1s
        }
    }

    // 分析音量
    function toAnalysisSpeaking(result: any) {
        const analysisFunc = props.customAnalysisSpeaking || defaultAnalysisSpeaking;

        analysisFunc(result)
            .then(([speaking, volumn]) => {
                isSpeaking.value = speaking;
                speakVolume.value = volumn;
            })
            .catch(() => {});
    }

    // 转文字
    function toTransformText(result: any) {
        if (!unref(finalTransformText)) return;
        props
            ?.customTransformText(result)
            .then((text: string) => {
                transformText.value = text;
            })
            .catch(() => {});
    }

    // 默认分析音量策略
    function defaultAnalysisSpeaking(result: any): Promise<[boolean, number]> {
        return new Promise((resolve) => {
            const buffer = result.frameBuffer;
            const data = new Int16Array(buffer);
            let sum = 0;
            for (let i = 0; i < data.length; i++) {
                sum += Math.abs(data[i]);
            }
            const averageVolume = sum / data.length;
            // console.log(">>>平均音量", averageVolume);
            if (initialVolumeCount < unref(finaljoinEnvironment)) {
                initialVolumeSum += averageVolume;
                initialVolumeCount++;
                if (initialVolumeCount === unref(finaljoinEnvironment)) {
                    const averageInitialVolume = initialVolumeSum / 10;
                    // 根据初始音量的平均值设置阈值，例如设置为平均值的 2 倍
                    volumeThreshold = averageInitialVolume * 2;
                }
                resolve([false, 1]);
            } else {
                if (averageVolume > volumeThreshold) {
                    // console.log("有声音");
                    const w = Math.max(1, Math.min(weightVolume, (averageVolume * weightVolume) / allowMaxVolume));
                    resolve([true, w]);
                } else {
                    // console.log("无声音");
                    resolve([false, 1]);
                }
            }
        });
    }

    // 录音管理操作
    function pause(callback?: (res: any) => void) {
        if (!recording.value || stoping.value) return;
        if (pausing.value) return;
        callbacks.pause = callback;
        try {
            recorderManager.pause();
        } catch (error) {
            console.error('暂停录音失败:', error);
        }
    }

    function resume(callback?: (res: any) => void) {
        if (!recording.value || stoping.value) return;
        if (!pausing.value) return;
        callbacks.resume = callback;
        try {
            recorderManager.resume();
        } catch (error) {
            console.error('恢复录音失败:', error);
        }
    }

    function start(callback?: (res: any) => void) {
        if (stoping.value || recording.value) return;
        callbacks.start = callback;
        callbacks.stop = undefined;
        callbacks.pause = undefined;
        callbacks.resume = undefined;
        recorderManager.start(unref(recorderManagerOptions));
    }

    function stop(callback?: (tempFilePath: string, res: any) => void) {
       // recorderManager?.stop();
        if (!recording.value || stoping.value) return;
        callbacks.stop = callback;
        stoping.value = true;
        recorderManager.stop();
    }

    return {
        recorderManager,
        recordingCancel,
        recordingError,
        recording,
        pausing,
        stoping,
        isSpeaking,
        isTimeout,
        isLack,
        speakVolume,
        audioPath,
        transformText,
        countdownValue,
        finalMinDuration,
        pause,
        resume,
        start,
        stop
    };
}
