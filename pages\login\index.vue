<template>
    <view class="content">
        <image class="bg" src="/static/login-bg.png" mode="widthFix"></image>
        <view class="login-contain">
            <view class="login">
                <view class="assistant">玲珑AI报价助手</view>

                <view class="login-phone">
                    <uv-button
                        v-if="allowAuthPhone"
                        :customStyle="{ width: '100%', 'border-radius': '40px', padding: 0, 'background-color': 'transparent', 'border-color': 'transparent' }"
                        openType="getPhoneNumber"
                        @getphonenumber="getPhoneNumber"
                        type="success"
                        size="normal"
                        text="确定授权"
                    >
                        <view class="wechat-login">
                            <!--    <image class="wechat-icon" src="/static/wechat.png"></image> -->
                            <view class="wechat-name">手机号快捷登录</view>
                        </view>
                    </uv-button>
                </view>

                <view v-if="weChatUserInfo.phoneNumber" class="reg" @click="onRedirectReg()">没有账号，立即注册</view>
                <uv-button
                    v-else
                    :customStyle="{ width: '100%', 'border-radius': '40px', padding: 0, 'background-color': 'transparent', 'border-color': 'transparent' }"
                    openType="getPhoneNumber"
                    @getphonenumber="getPhoneNumberForReg"
                    type="success"
                    size="normal"
                    text="确定授权"
                >
                    <view class="reg" @click="onRedirectReg()">没有账号，立即注册</view>
                </uv-button>
                <view class="auth-tip">授权后即可使用完整功能</view>
                <view class="pact">
                    <uv-checkbox-group
                        :size="12"
                        :customStyle="{
                            'font-size': '10px'
                        }"
                    >
                        <uv-checkbox activeColor="#00B678" label="">
                            <text>登录视为您已阅读并同意</text>
                        </uv-checkbox>
                    </uv-checkbox-group>
                    <text class="service" @click="onRedirectTerms()">服务条款</text>
                    <text>和</text>
                    <text class="privacy" @click="onRedirectPrivacy()">隐私政策</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { login, navigateTo, redirectTo, showToast, switchTab } from '@/utils';
import { getWeChatUserOpenId, getWeChatUserPhone } from '@/api/we-chat';
import { IWechatUserInfo } from '@/models';
import { loginWeChat } from '@/api/account';
import { AuthAccessToken } from '@/consts';
import { setStorageValue } from '@/utils/storage';

const allowAuthPhone = ref(false);
const allowReg = ref(false);
let weChatUserInfo: IWechatUserInfo = {};

onMounted(async () => {
    const loginRes = await login();
    if (loginRes.errMsg != 'login:ok') {
        showToast({ title: '微信登录失败，请重试', icon: 'error' });
        return;
    }

    const { data } = await getWeChatUserOpenId(loginRes.code);
    weChatUserInfo = data;
    // 根据unionId 或者 openId 查询不倒用户
    if (!data.allowLogin) {
        allowAuthPhone.value = true;
        return;
    }
    handleWeChatLogin();
});

/** 微信获取授权手机号 */
const getPhoneNumber = async (e: any) => {
    if (e.errMsg != 'getPhoneNumber:ok') {
        showToast({ title: '请授权手机号码', icon: 'error' });
        return;
    }

    const { data } = await getWeChatUserPhone({ code: e.code, unionId: weChatUserInfo.unionId, openId: weChatUserInfo.openId });
    weChatUserInfo.phoneNumber = data.phoneNumber;
    if (!data.allowLogin) {
        allowReg.value = true;
        showToast({ title: '手机号未注册', icon: 'none' });
        setTimeout(() => onRedirectReg(), 2000);
        return;
    }
    handleWeChatLogin();
};

const getPhoneNumberForReg = async (e: any) => {
    if (!weChatUserInfo?.phoneNumber) {
        await getPhoneNumber(e);
        return;
    }
    onRedirectReg();
};

const handleWeChatLogin = async () => {
    const authRes = await loginWeChat({ unionId: weChatUserInfo.unionId, openId: weChatUserInfo.openId });

    if (authRes.code != 0) {
        showToast({ title: '登录失败，请重新登录' });
        return;
    }

    // 存 登录凭据
    setStorageValue(AuthAccessToken, authRes.data);
    switchTab({ url: '/pages/quote/index' });
};

const onRedirectReg = () => {
    if (!weChatUserInfo.unionId || !weChatUserInfo.openId || !weChatUserInfo.phoneNumber) {
        return;
    }

    redirectTo({ url: `/pages/register/index?unionId=${weChatUserInfo.unionId}&openId=${weChatUserInfo.openId}&phone=${weChatUserInfo.phoneNumber}` });
};

const onRedirectTerms = () => {
    navigateTo({ url: '/pages/subpackage/service/terms/index' });
};

const onRedirectPrivacy = () => {
    navigateTo({ url: '/pages/subpackage/service/privacy/index' });
};
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>