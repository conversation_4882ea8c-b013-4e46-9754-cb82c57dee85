/**
 * 将枚举转换为包含 {label: 描述, value: 枚举值} 的数组
 * @param enumObj - 枚举对象
 * @param mapDesc - 描述映射
 */
export function enumToArray<E extends { [key: number]: string }, T extends number>(enumObj: E, mapDesc: Record<T, string>): Array<{ name: string; value: string | number }> {
    return Object.keys(enumObj)
        .filter((key) => !isNaN(Number(key))) // 过滤掉非数值键
        .map((key) => {
            const value = key;
            return {
                name: mapDesc[value],
                value: value
            };
        });
}
