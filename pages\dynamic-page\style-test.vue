<template>
    <view class="test-page">
        <view class="test-header">
            <text class="test-title">样式处理测试</text>
            <text class="test-desc">测试 convertStyleToCSS 工具函数的正确使用</text>
        </view>
        
        <view class="test-section">
            <text class="section-title">测试组件样式配置：</text>
            <view class="config-display">
                <text class="config-text">{{ JSON.stringify(testComponent.style, null, 2) }}</text>
            </view>
        </view>
        
        <view class="test-section">
            <text class="section-title">转换后的CSS样式：</text>
            <view class="config-display">
                <text class="config-text">{{ JSON.stringify(convertedStyle, null, 2) }}</text>
            </view>
        </view>
        
        <view class="test-section">
            <text class="section-title">实际应用效果：</text>
            <view class="demo-container">
                <!-- 使用转换后的样式 -->
                <view class="demo-component" :style="convertedStyle">
                    <text class="demo-text">这是一个测试组件</text>
                    <text class="demo-desc">应用了转换后的padding、margin和borderRadius样式</text>
                </view>
            </view>
        </view>
        
        <view class="test-actions">
            <button @click="updateTestStyle" class="test-btn">更新测试样式</button>
            <button @click="resetStyle" class="test-btn">重置样式</button>
        </view>
        
        <view class="test-info">
            <text class="info-title">测试说明：</text>
            <text class="info-item">1. convertStyleToCSS 应该将配置对象转换为CSS样式对象</text>
            <text class="info-item">2. padding/margin 应该转换为 paddingTop, paddingRight 等属性</text>
            <text class="info-item">3. borderRadius 应该转换为具体的圆角属性</text>
            <text class="info-item">4. 所有值都应该带有 'px' 单位</text>
        </view>
    </view>
</template>

<script setup>
import { ref, computed } from 'vue';
import { convertStyleToCSS } from '../../utils/style-helper';

// 测试用的组件样式配置
const testComponent = ref({
    style: {
        padding: {
            top: 16,
            right: 12,
            bottom: 16,
            left: 12
        },
        margin: {
            top: 8,
            right: 0,
            bottom: 8,
            left: 0
        },
        borderRadius: {
            topLeft: 8,
            topRight: 8,
            bottomLeft: 8,
            bottomRight: 8
        }
    }
});

// 使用 convertStyleToCSS 转换样式
const convertedStyle = computed(() => {
    return convertStyleToCSS(testComponent.value.style);
});

// 更新测试样式
const updateTestStyle = () => {
    testComponent.value.style = {
        padding: {
            top: Math.floor(Math.random() * 20) + 10,
            right: Math.floor(Math.random() * 20) + 10,
            bottom: Math.floor(Math.random() * 20) + 10,
            left: Math.floor(Math.random() * 20) + 10
        },
        margin: {
            top: Math.floor(Math.random() * 16) + 4,
            right: Math.floor(Math.random() * 16) + 4,
            bottom: Math.floor(Math.random() * 16) + 4,
            left: Math.floor(Math.random() * 16) + 4
        },
        borderRadius: {
            topLeft: Math.floor(Math.random() * 20),
            topRight: Math.floor(Math.random() * 20),
            bottomLeft: Math.floor(Math.random() * 20),
            bottomRight: Math.floor(Math.random() * 20)
        }
    };
    
    uni.showToast({
        title: '样式已更新',
        icon: 'success'
    });
};

// 重置样式
const resetStyle = () => {
    testComponent.value.style = {
        padding: {
            top: 16,
            right: 12,
            bottom: 16,
            left: 12
        },
        margin: {
            top: 8,
            right: 0,
            bottom: 8,
            left: 0
        },
        borderRadius: {
            topLeft: 8,
            topRight: 8,
            bottomLeft: 8,
            bottomRight: 8
        }
    };
    
    uni.showToast({
        title: '样式已重置',
        icon: 'success'
    });
};
</script>

<style lang="scss" scoped>
.test-page {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100vh;
}

.test-header {
    text-align: center;
    margin-bottom: 30px;
    
    .test-title {
        font-size: 20px;
        font-weight: bold;
        color: #333;
        display: block;
        margin-bottom: 8px;
    }
    
    .test-desc {
        font-size: 14px;
        color: #666;
        display: block;
    }
}

.test-section {
    background-color: white;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    
    .section-title {
        font-size: 16px;
        font-weight: bold;
        color: #333;
        display: block;
        margin-bottom: 10px;
    }
}

.config-display {
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
    
    .config-text {
        font-family: 'Courier New', monospace;
        font-size: 12px;
        color: #495057;
        white-space: pre-wrap;
        display: block;
    }
}

.demo-container {
    padding: 20px;
    background-color: #e9ecef;
    border-radius: 8px;
    border: 2px dashed #6c757d;
}

.demo-component {
    background-color: #007bff;
    color: white;
    min-height: 80px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    
    .demo-text {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 8px;
        display: block;
    }
    
    .demo-desc {
        font-size: 12px;
        opacity: 0.8;
        text-align: center;
        display: block;
    }
}

.test-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 30px;
    
    .test-btn {
        padding: 10px 20px;
        background-color: #007aff;
        color: white;
        border: none;
        border-radius: 6px;
        font-size: 14px;
    }
}

.test-info {
    background-color: white;
    padding: 15px;
    border-radius: 8px;
    
    .info-title {
        font-size: 16px;
        font-weight: bold;
        color: #333;
        display: block;
        margin-bottom: 10px;
    }
    
    .info-item {
        font-size: 14px;
        color: #666;
        display: block;
        margin-bottom: 5px;
        padding-left: 10px;
    }
}
</style>
