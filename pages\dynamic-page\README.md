# 动态页面重构说明

## 重构概述

根据 `config.js` 中的最新 JSON 配置，完全重构了动态页面渲染逻辑，不再兼容旧的配置格式。

## 主要变更

### 1. 配置结构更新

新的配置结构基于 `config.js` 中的 `defaultConfig`：

```javascript
{
  "pageConfig": {
    "background": {
      "type": "gradient",
      "color": "#ffffff",
      "gradientType": "linear",
      "gradientDirection": "to right",
      "gradientColors": ["#ff6b6b", "#4ecdc4", "#45b7d1"]
    },
    "padding": {
      "top": 14,
      "right": 12,
      "bottom": 12,
      "left": 14
    },
    "previewMode": "mobile"
  },
  "components": [
    // 组件配置数组
  ]
}
```

### 2. 组件渲染优化

- **移除了旧的导航栏配置**：不再支持自定义导航栏
- **简化了组件包装器**：使用新的样式处理方式
- **统一了组件样式处理**：通过 `getComponentStyle()` 和 `getComponentWrapperStyle()` 方法

### 3. 支持的组件类型

当前支持以下组件类型：
- `search` - 搜索组件
- `carousel` - 轮播图组件
- `notice` - 公告组件
- `image` - 图片组件
- `grid-menu` - 九宫格菜单组件
- `coupon` - 优惠券组件
- `seckill` - 秒杀组件
- `product` - 商品组件
- `divider` - 分割线组件
- `article` - 文章组件
- `group-buy` - 团购组件
- `chain-buy` - 接龙购买组件
- `service-product` - 服务商品组件

### 4. 样式处理改进

#### 页面背景样式
- 支持纯色背景 (`solid`)
- 支持渐变背景 (`gradient`)
- 支持线性渐变和径向渐变

#### 组件样式
- 统一的 padding/margin 处理
- 支持组件级别的背景配置
- 响应式设计优化

### 5. 移除的功能

- 自定义导航栏配置
- 旧版本的组件兼容性
- 部分不再使用的组件类型

## 使用方法

### 基本使用

```vue
<template>
  <dynamic-page />
</template>

<script setup>
// 页面会自动使用 config.js 中的 defaultConfig
</script>
```

### 传递自定义配置

```javascript
// 跳转时传递配置
const config = {
  pageConfig: { /* 页面配置 */ },
  components: [ /* 组件配置 */ ]
};

uni.navigateTo({
  url: `/pages/dynamic-page/index?config=${encodeURIComponent(JSON.stringify(config))}`
});
```

## 测试

使用 `test.vue` 页面进行功能测试：

```bash
# 在小程序开发工具中访问
pages/dynamic-page/test
```

测试页面提供：
- 配置信息查看
- 组件列表展示
- 页面跳转测试

## 注意事项

1. **不兼容旧配置**：新版本完全基于最新的 JSON 配置结构
2. **组件依赖**：确保所有引用的组件文件存在
3. **样式适配**：针对移动端进行了优化
4. **性能优化**：移除了不必要的计算和渲染逻辑

## 开发建议

1. 使用 `config.js` 中的配置作为标准格式
2. 新增组件时遵循现有的配置结构
3. 测试时使用提供的测试页面
4. 关注控制台输出的调试信息

## 文件结构

```
pages/dynamic-page/
├── index.vue          # 主要的动态页面组件
├── config.js          # 最新的JSON配置
├── test.vue           # 测试页面
└── README.md          # 本说明文档
```
