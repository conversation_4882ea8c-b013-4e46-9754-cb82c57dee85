<template>
  <view class="groupbuy-component" :style="containerStyle">
    <view class="groupbuy-wrapper" :style="wrapperStyle">
      <!-- 团购头部 -->
      <view class="groupbuy-header" :style="headerStyle">
        <view class="header-content">
          <view class="header-left">
            <view class="badge" :style="badgeStyle">
              <text class="badge-text">{{ config.badgeText || '团购' }}</text>
            </view>
            <view class="header-text">
              <text class="header-title">{{ config.title || '团购专区' }}</text>
              <text class="header-desc">{{ config.description || '邀请好友一起团购' }}</text>
            </view>
          </view>
          
          <view class="header-right">
            <uv-button
              text="更多"
              type="info"
              size="mini"
              plain
              @click="handleViewMore"
            />
          </view>
        </view>
      </view>
      
      <!-- 团购商品列表 -->
      <scroll-view scroll-x :show-scrollbar="false" class="products-scroll">
        <view class="products-list">
          <view
            v-for="(product, index) in config.products"
            :key="product.id"
            class="product-item"
            :style="productItemStyle"
            @click="handleProductClick(product, index)"
          >
            <!-- 商品图片 -->
            <view class="product-image-wrapper">
              <uv-image
                :src="product.image"
                :width="'100%'"
                :height="'100%'"
                :border-radius="4"
                mode="aspectFill"
                class="product-image"
              />
              
              <!-- 团购标签 -->
              <view class="groupbuy-badge">
                <text class="badge-text">{{ product.groupSize }}人团</text>
              </view>
            </view>
            
            <!-- 商品信息 -->
            <view class="product-info">
              <text class="product-title">{{ product.title }}</text>
              
              <!-- 价格信息 -->
              <view class="price-container">
                <view class="group-price-info">
                  <text class="group-price">¥{{ product.groupPrice }}</text>
                  <text class="group-label">{{ product.groupSize }}人价</text>
                </view>
                <text class="original-price">单买¥{{ product.originalPrice }}</text>
              </view>
              
              <!-- 团购进度 -->
              <view class="group-progress">
                <view class="progress-info">
                  <text class="progress-text">已成团{{ product.successGroups }}个</text>
                  <text class="participants-text">{{ product.currentParticipants }}人参与</text>
                </view>
                <uv-line-progress
                  :percentage="calculateGroupProgress(product.currentParticipants, product.targetParticipants)"
                  :active-color="config.progressColor || '#52c41a'"
                  :inactive-color="'#f0f0f0'"
                  :height="4"
                  :show-text="false"
                />
              </view>
              
              <!-- 参团按钮 -->
              <uv-button
                :text="getButtonText(product)"
                :color="getButtonColor(product)"
                size="mini"
                :custom-style="buyButtonStyle"
                @click.stop="handleJoinGroup(product)"
              />
            </view>
          </view>
        </view>
      </scroll-view>
      
      <!-- 团购规则说明 -->
      <view v-if="config.showRules" class="rules-section">
        <text class="rules-title">团购规则</text>
        <view class="rules-list">
          <text class="rule-item">• 邀请好友参团，人数满{{ config.minGroupSize || 2 }}人即可享受团购价</text>
          <text class="rule-item">• 团购时间限制{{ config.timeLimit || 24 }}小时，超时自动退款</text>
          <text class="rule-item">• 支持微信、支付宝等多种支付方式</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { convertStyleToCSS } from '../../utils/style-helper';
import { theme } from '../../config/theme';

interface GroupBuyProduct {
  id: string;
  title: string;
  image: string;
  groupPrice: number;
  originalPrice: number;
  groupSize: number;
  successGroups: number;
  currentParticipants: number;
  targetParticipants: number;
  status: 'active' | 'success' | 'failed';
  endTime: string;
  link: string;
}

interface GroupBuyConfig {
  title: string;
  description: string;
  badgeText: string;
  headerAlign: 'left' | 'center' | 'right';
  products: GroupBuyProduct[];
  backgroundColor: string;
  headerBackgroundColor: string;
  badgeBackgroundColor: string;
  badgeTextColor: string;
  progressColor: string;
  buttonColor: string;
  spacing: number;
  margin: number;
  showRules: boolean;
  minGroupSize: number;
  timeLimit: number;
}

interface Props {
  config: GroupBuyConfig;
  style?: any;
}

const props = defineProps<Props>();

// 容器样式
const containerStyle = computed(() => {
  if (!props.style) {
    return { width: '100%' };
  }
  
  const baseStyle = convertStyleToCSS(props.style);
  
  const processedStyle = {};
  Object.keys(baseStyle).forEach(key => {
    processedStyle[key] = String(baseStyle[key]);
  });
  
  return {
    ...processedStyle,
    width: '100%',
  };
});

// 包装器样式
const wrapperStyle = computed(() => {
  return {
    backgroundColor: props.config.backgroundColor || '#ffffff',
    borderRadius: '12px',
    margin: `${props.config.margin || theme.spacing.sm}px`,
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
    overflow: 'hidden',
  };
});

// 头部样式
const headerStyle = computed(() => {
  return {
    backgroundColor: props.config.headerBackgroundColor || '#f8f9fa',
    padding: `${theme.spacing.md}px`,
    textAlign: props.config.headerAlign || 'left',
  };
});

// 徽章样式
const badgeStyle = computed(() => {
  return {
    backgroundColor: props.config.badgeBackgroundColor || '#52c41a',
    color: props.config.badgeTextColor || '#ffffff',
  };
});

// 商品项样式
const productItemStyle = computed(() => {
  return {
    width: '140px',
    marginRight: `${props.config.spacing || theme.spacing.sm}px`,
    backgroundColor: '#ffffff',
    borderRadius: '8px',
    border: '1px solid #f0f0f0',
    overflow: 'hidden',
  };
});

// 购买按钮样式
const buyButtonStyle = computed(() => {
  return {
    borderRadius: '4px',
    fontSize: '12px',
    height: '28px',
    marginTop: '8px',
  };
});

// 计算团购进度
const calculateGroupProgress = (current: number, target: number) => {
  return target > 0 ? Math.round((current / target) * 100) : 0;
};

// 获取按钮文字
const getButtonText = (product: GroupBuyProduct) => {
  switch (product.status) {
    case 'success':
      return '立即参团';
    case 'failed':
      return '已结束';
    default:
      return `发起${product.groupSize}人团`;
  }
};

// 获取按钮颜色
const getButtonColor = (product: GroupBuyProduct) => {
  switch (product.status) {
    case 'success':
      return '#52c41a';
    case 'failed':
      return '#d9d9d9';
    default:
      return props.config.buttonColor || '#ff4757';
  }
};

// 处理商品点击
const handleProductClick = (product: GroupBuyProduct, index: number) => {
  if (product.link) {
    uni.navigateTo({
      url: product.link,
      fail: () => {
        uni.showToast({ title: '页面跳转失败', icon: 'none' });
      }
    });
  }
};

// 处理参团
const handleJoinGroup = (product: GroupBuyProduct) => {
  if (product.status === 'failed') {
    uni.showToast({
      title: '团购已结束',
      icon: 'none'
    });
    return;
  }
  
  uni.showToast({
    title: `参与团购: ${product.title}`,
    icon: 'success'
  });
  
  // 可以在这里添加参团逻辑
  uni.$emit('groupbuy-join', { product });
};

// 处理查看更多
const handleViewMore = () => {
  uni.navigateTo({
    url: '/pages/groupbuy/list',
    fail: () => {
      uni.showToast({ title: '查看更多团购商品', icon: 'none' });
    }
  });
};
</script>

<style lang="scss" scoped>
.groupbuy-component {
  width: 100%;
}

.groupbuy-wrapper {
  width: 100%;
}

.groupbuy-header {
  border-bottom: 1px solid #f0f0f0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.badge {
  padding: 4px 8px;
  border-radius: 12px;
  margin-right: 12px;
}

.badge-text {
  font-size: 12px;
  font-weight: 600;
}

.header-text {
  flex: 1;
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 4px;
}

.header-desc {
  font-size: 12px;
  color: #666;
  display: block;
}

.header-right {
  flex-shrink: 0;
}

.products-scroll {
  padding: 16px;
  white-space: nowrap;
}

.products-list {
  display: flex;
  flex-direction: row;
  padding-right: 16px;
}

.product-item {
  flex-shrink: 0;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:active {
    transform: scale(0.98);
    opacity: 0.9;
  }
}

.product-image-wrapper {
  position: relative;
  width: 100%;
  height: 100px;
  overflow: hidden;
}

.product-image {
  width: 100%;
  height: 100%;
}

.groupbuy-badge {
  position: absolute;
  top: 4px;
  left: 4px;
  background-color: #52c41a;
  border-radius: 2px;
  padding: 2px 4px;
}

.product-info {
  padding: 8px;
}

.product-title {
  font-size: 12px;
  color: #333;
  font-weight: 500;
  margin-bottom: 6px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.3;
}

.price-container {
  margin-bottom: 8px;
}

.group-price-info {
  display: flex;
  align-items: baseline;
  margin-bottom: 2px;
}

.group-price {
  font-size: 16px;
  color: #52c41a;
  font-weight: 600;
  margin-right: 4px;
}

.group-label {
  font-size: 10px;
  color: #52c41a;
  background-color: #f6ffed;
  padding: 1px 4px;
  border-radius: 2px;
}

.original-price {
  font-size: 10px;
  color: #999;
  text-decoration: line-through;
}

.group-progress {
  margin-bottom: 8px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.progress-text,
.participants-text {
  font-size: 10px;
  color: #666;
}

.rules-section {
  padding: 16px;
  background-color: #fafafa;
  border-top: 1px solid #f0f0f0;
}

.rules-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  display: block;
}

.rules-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.rule-item {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

// 响应式适配
@media screen and (max-width: 750px) {
  .product-item {
    width: 120px;
  }
  
  .product-image-wrapper {
    height: 80px;
  }
  
  .header-title {
    font-size: 16px;
  }
}
</style>
