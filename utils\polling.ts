// utils/polling.ts

type PollingOptions<T> = {
  /** 要轮询执行的方法 */
  fn: () => Promise<T>;
  /** 轮询间隔(毫秒)，默认 3000 */
  interval?: number;
  /** 最大重试次数，默认 3 */
  maxRetry?: number;
  /** 是否立即执行，默认 false */
  immediate?: boolean;
  /** 错误回调 */
  onError?: (error: Error) => void;
  /** 成功回调 */
  onSuccess?: (data: T) => void;
};

export class Polling<T = any> {
  private timer: number | null = null;
  private retryCount = 0;
  private isActive = false;
  
  private options: Required<Omit<PollingOptions<T>, 'onError' | 'onSuccess'>> & {
    onError?: (error: Error) => void;
    onSuccess?: (data: T) => void;
  };

  constructor(options: PollingOptions<T>) {
    if (typeof options.fn !== 'function') {
      throw new Error('Polling: fn must be a function');
    }

    this.options = {
      interval: 3000,
      maxRetry: 3,
      immediate: false,
      ...options,
    };
  }

  /**
   * 开始轮询
   */
  start(): void {
    if (this.isActive) return;

    this.isActive = true;
    this.retryCount = 0;

    if (this.options.immediate) {
      this.execute();
    } else {
      this.scheduleNext();
    }
  }

  /**
   * 停止轮询
   */
  stop(): void {
    this.isActive = false;
    if (this.timer !== null) {
      clearTimeout(this.timer);
      this.timer = null;
    }
  }

  /**
   * 执行轮询方法
   */
  private async execute(): Promise<void> {
    if (!this.isActive) return;

    try {
      const result = await this.options.fn();
      this.retryCount = 0; // 成功后重置重试次数
      this.options.onSuccess?.(result);
      this.scheduleNext();
    } catch (error) {
      this.retryCount++;

      if (this.retryCount >= this.options.maxRetry) {
        this.stop();
        this.options.onError?.(
          new Error(`Polling: Max retry (${this.options.maxRetry}) exceeded`)
        );
      } else {
        this.scheduleNext();
      }

      if (error instanceof Error) {
        this.options.onError?.(error);
      } else {
        this.options.onError?.(new Error(String(error)));
      }
    }
  }

  /**
   * 安排下一次执行
   */
  private scheduleNext(): void {
    if (!this.isActive) return;

    this.timer = setTimeout(() => {
      this.execute();
    }, this.options.interval) as unknown as number; // 微信小程序环境下的类型转换
  }

  /**
   * 更新配置
   * @param newOptions 新配置
   */
  updateOptions(newOptions: Partial<PollingOptions<T>>): void {
    this.options = {
      ...this.options,
      ...newOptions,
    };
  }

  /**
   * 获取当前是否活跃状态
   */
  get active(): boolean {
    return this.isActive;
  }
}

/**
 * 创建轮询实例
 * @param options 配置选项
 * @returns 轮询实例
 */
export function createPolling<T = any>(options: PollingOptions<T>): Polling<T> {
  return new Polling<T>(options);
}