.container {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    height: 100%;
    justify-content: flex-end;
}

.content {
    display: flex;
    flex-direction: column;
    padding: 10px;
    //flex: 1;
    overflow: auto;
    .search {
        background: #ffffff;
        padding: 10px;
        .action {
            margin-top: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            .desc {
                display: flex;
                align-items: center;
                gap: 4px;
                font-size: 12px;
                color: #606266;
                .info_icon {
                    width: 12px;
                    height: 12px;
                }
                .keyword {
                    color: #00b678;
                }
            }
            .search-action{
                display: flex;
                gap: 4px;
                .voice-btn,.search-btn{
                    padding: 5px 12px;
                    background: #00b678;
                    border-radius: 4px;
                    font-size: 12px;
                    color: #ffffff;
                }
                
            }
        }
    }
    .info {
        display: flex;
        background: #ffffff;
        box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.06);
        padding-left: 22px;
        flex-direction: column;
        border-radius: 10px;
        margin: 10px 0;
        .item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #dcdfe6;
            .label {
                font-size: 16px;
                font-weight: 600;
                color: #303133;
                position: relative;
                &.required::before {
                    content: '*';
                    color: #f56c6c;
                    left: -10px;
                    position: absolute;
                }
            }
            .value {
                padding-right: 12px;
                flex: 1;
            }
        }
    }
    .discount {
        background: #ffffff;
        box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.06);
        border-radius: 10px;
        padding: 16px 20px;
        .label {
            font-size: 16px;
            color: #303133;
            margin-bottom: 10px;
        }
        .input-discount {
            display: flex;
            gap: 10px;
        }
    }
}

.action_box {
    width: 100%;
    background: linear-gradient(180deg, #d7f6f4 0%, #fbfcfc 13%);
    box-shadow: 0px 2.5px 12.5px 0px rgba(21, 105, 137, 0.08);
    .unit-price_box {
        padding: 20px 20px 30px;
        .total {
            display: flex;
            gap: 45px;
            justify-content: center;
            border-bottom: 1px solid #dcdfe6;
            padding-bottom: 20px;
            .item {
                display: flex;
                flex-direction: column;
                align-items: center;
                .label {
                    font-size: 14px;
                    font-weight: 500;
                    color: #606266;
                }
                .unit-price_item {
                    color: #f56c6c;
                    display: flex;
                    margin-top: 10px;
                    align-items: baseline;
                    .price-icon {
                        font-size: 14px;
                        font-weight: 500;
                    }
                    .price {
                        font-weight: 500;
                        font-size: 22px;
                    }
                }
            }
        }
    }
    .action {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 10px;
        .cancel {
            padding: 5px 16px;
            color: #303133;
            border: 1px solid #dcdfe6;
        }
        .left-btn,
        .right-btn {
            display: flex;
            gap: 10px;
            .delete {
                padding: 5px 16px;
            }
            .delete {
                color: #ffffff;
                background-color: #f56c6c;
            }
        }
        .btn {
            border-radius: 4px;
            font-size: 14px;
        }
        .submit {
            color: #ffffff;
            padding: 5px 46px;
            background: #00b678;
        }
        .right-btn {
            .submit {
                padding: 5px 16px;
            }
        }
    }
}
