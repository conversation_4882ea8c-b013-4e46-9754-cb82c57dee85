<template>
  <view class="theme-switcher">
    <!-- 主题切换按钮 -->
    <view class="theme-trigger" @click="showThemePanel = !showThemePanel">
      <view class="current-theme-preview">
        <view 
          class="color-dot primary" 
          :style="{ backgroundColor: currentColors.primary }"
        ></view>
        <view 
          class="color-dot secondary" 
          :style="{ backgroundColor: currentColors.secondary }"
        ></view>
        <view 
          class="color-dot accent" 
          :style="{ backgroundColor: currentColors.accent }"
        ></view>
      </view>
      <text class="theme-text">主题</text>
    </view>
    
    <!-- 主题选择面板 -->
    <view v-if="showThemePanel" class="theme-panel" :style="panelStyle">
      <view class="panel-header">
        <text class="panel-title">选择主题</text>
        <view class="close-btn" @click="showThemePanel = false">
          <text class="close-icon">×</text>
        </view>
      </view>
      
      <view class="theme-list">
        <view 
          v-for="themeItem in themeList" 
          :key="themeItem.key"
          class="theme-item"
          :class="{ active: currentTheme === themeItem.key }"
          @click="handleThemeChange(themeItem.key)"
        >
          <!-- 主题预览 -->
          <view class="theme-preview">
            <view class="preview-colors">
              <view 
                class="preview-color primary"
                :style="{ backgroundColor: themeItem.colors.primary }"
              ></view>
              <view 
                class="preview-color secondary"
                :style="{ backgroundColor: themeItem.colors.secondary }"
              ></view>
              <view 
                class="preview-color accent"
                :style="{ backgroundColor: themeItem.colors.accent }"
              ></view>
            </view>
            <view class="preview-neutral">
              <view 
                v-for="(color, shade) in themeItem.colors.neutral" 
                :key="shade"
                class="neutral-bar"
                :style="{ backgroundColor: color }"
              ></view>
            </view>
          </view>
          
          <!-- 主题信息 -->
          <view class="theme-info">
            <text class="theme-name">{{ themeItem.name }}</text>
            <view class="color-labels">
              <text class="color-label">主色</text>
              <text class="color-label">辅助</text>
              <text class="color-label">强调</text>
            </view>
          </view>
          
          <!-- 选中标识 -->
          <view v-if="currentTheme === themeItem.key" class="selected-icon">
            <text class="check-icon">✓</text>
          </view>
        </view>
      </view>
      
      <!-- 自定义主题按钮 -->
      <view class="custom-theme-section">
        <view class="custom-theme-btn" @click="handleCustomTheme">
          <text class="custom-text">自定义主题</text>
        </view>
      </view>
    </view>
    
    <!-- 遮罩层 -->
    <view 
      v-if="showThemePanel" 
      class="theme-overlay" 
      @click="showThemePanel = false"
    ></view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';
import { 
  currentTheme, 
  currentThemeColors, 
  themeList, 
  switchTheme,
  dynamicTheme 
} from '../../config/theme-system';

const showThemePanel = ref(false);

// 当前主题颜色
const currentColors = computed(() => currentThemeColors.value);

// 面板样式
const panelStyle = computed(() => {
  const theme = dynamicTheme.value;
  return {
    backgroundColor: theme.colors.background.primary,
    borderColor: theme.colors.border.medium,
    boxShadow: theme.shadows.lg,
  };
});

// 处理主题切换
const handleThemeChange = (themeKey) => {
  switchTheme(themeKey);
  showThemePanel.value = false;
  
  uni.showToast({
    title: `已切换到${themeList.find(t => t.key === themeKey)?.name}`,
    icon: 'success',
    duration: 1500
  });
  
  // 触发主题变更事件
  uni.$emit('theme-changed', {
    theme: themeKey,
    colors: currentThemeColors.value
  });
};

// 处理自定义主题
const handleCustomTheme = () => {
  showThemePanel.value = false;
  uni.showToast({
    title: '自定义主题功能开发中',
    icon: 'none'
  });
};
</script>

<style lang="scss" scoped>
.theme-switcher {
  position: relative;
}

.theme-trigger {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  border: 1px solid #e5e7eb;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:active {
    transform: scale(0.95);
  }
}

.current-theme-preview {
  display: flex;
  margin-right: 8px;
  gap: 2px;
}

.color-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.theme-text {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}

.theme-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 998;
}

.theme-panel {
  position: absolute;
  top: 100%;
  right: 0;
  width: 320px;
  margin-top: 8px;
  border-radius: 12px;
  border: 1px solid;
  z-index: 999;
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.panel-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.close-btn {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f3f4f6;
  cursor: pointer;
}

.close-icon {
  font-size: 18px;
  color: #6b7280;
}

.theme-list {
  max-height: 400px;
  overflow-y: auto;
}

.theme-item {
  position: relative;
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
  cursor: pointer;
  transition: background-color 0.2s ease;
  
  &:hover {
    background-color: #f9fafb;
  }
  
  &.active {
    background-color: #eff6ff;
  }
  
  &:last-child {
    border-bottom: none;
  }
}

.theme-preview {
  margin-right: 12px;
}

.preview-colors {
  display: flex;
  gap: 4px;
  margin-bottom: 4px;
}

.preview-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.preview-neutral {
  display: flex;
  gap: 1px;
  height: 4px;
}

.neutral-bar {
  flex: 1;
  border-radius: 1px;
}

.theme-info {
  flex: 1;
}

.theme-name {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  display: block;
  margin-bottom: 4px;
}

.color-labels {
  display: flex;
  gap: 8px;
}

.color-label {
  font-size: 10px;
  color: #9ca3af;
}

.selected-icon {
  width: 20px;
  height: 20px;
  background-color: #3b82f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.check-icon {
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.custom-theme-section {
  padding: 12px 16px;
  border-top: 1px solid #e5e7eb;
}

.custom-theme-btn {
  width: 100%;
  padding: 8px;
  text-align: center;
  background-color: #f3f4f6;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  
  &:hover {
    background-color: #e5e7eb;
  }
}

.custom-text {
  font-size: 14px;
  color: #6b7280;
}

// 移动端适配
@media screen and (max-width: 750px) {
  .theme-panel {
    width: 280px;
  }
  
  .theme-item {
    padding: 10px 12px;
  }
  
  .preview-color {
    width: 14px;
    height: 14px;
  }
}
</style>
