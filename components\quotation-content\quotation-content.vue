<template>
    <view class="content">
        <view class="form">
            <view class="item">
                <view class="label">询价单位：</view>
                <view class="value organization" @click="onSelectOrganization()">
                    <text v-if="!selectInquiryOrganization?.id">请选择</text>
                    <text class="name" v-else>{{ selectInquiryOrganization.name }}</text>
                    <image class="arraw" src="/static/arraw.png"></image>
                </view>
            </view>
            <view class="item">
                <view class="label">联系人：</view>
                <view class="value">
                    <uv-input v-model="form.contactPerson" placeholder="请输入" inputAlign="right" border="none" />
                </view>
            </view>
            <view class="item">
                <view class="label">联系方式：</view>
                <view class="value">
                    <uv-input v-model="form.contactInfo" placeholder="请输入" inputAlign="right" border="none" />
                </view>
            </view>
            <view class="item">
                <view class="label">铜价(元/吨)：</view>
                <view class="value">
                    <uv-input v-model="form.copperPrice" type="digit" placeholder="请输入" inputAlign="right" border="none" />
                </view>
            </view>
            <view class="item">
                <view class="label">主题 ：</view>
                <view class="value">
                    <uv-input v-model="form.subject" placeholder="请输入" inputAlign="right" border="none" />
                </view>
            </view>
            <view class="item">
                <view class="label">参考：</view>
                <view class="value">
                    <uv-input v-model="form.reference" placeholder="请输入" inputAlign="right" border="none" />
                </view>
            </view>
            <view class="item">
                <view class="label">备注：</view>
                <view class="value">
                    <uv-input v-model="form.remark" placeholder="请输入" inputAlign="right" border="none" />
                </view>
            </view>
        </view>
        <view class="action">
            <view class="cancel btn" @click="onCancel()">取消</view>
            <view class="confirm btn" @click="onConfirm()">确认生成</view>
        </view>
        <uv-picker ref="inquiryOrganizationOptions" :columns="columns" keyName="name" @confirm="onConfirmSelect" confirmColor="#00b678"></uv-picker>
    </view>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue';
import { IInquiryOrganization, IQuotationInquiryExport } from '@/models';
import { queryInquiryOrganizationList } from '@/api/inquiry-organization';

const emits = defineEmits<{
    (e: 'onCancel'): void;
    (e: 'onConfirm', val: IQuotationInquiryExport): void;
}>();

const form = reactive<IQuotationInquiryExport>({});
const inquiryOrganizationOptions = ref();
const columns = ref<Array<Array<IInquiryOrganization>>>();
const selectInquiryOrganization = ref<IInquiryOrganization>();

onMounted(async () => {
    const { data } = await queryInquiryOrganizationList();
    columns.value = [data];
});

const onSelectOrganization = () => {
    inquiryOrganizationOptions.value.open();
};

const onConfirmSelect = (e) => {
    selectInquiryOrganization.value = e.value[0];
    form.inquiryCompanyId = selectInquiryOrganization.value.id;
};

const onCancel = () => {
    emits('onCancel');
    emptyFormValue();
};

const onConfirm = () => {
    emits('onConfirm', form);
    emptyFormValue();
};

const emptyFormValue = () => {
    Object.assign(form, {
        inquiryCompanyId: undefined,
        contactPerson: undefined,
        contactInfo: undefined,
        copperPrice: undefined,
        subject: undefined,
        reference: undefined,
        remark: undefined
    });
    selectInquiryOrganization.value = {};
};
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>
