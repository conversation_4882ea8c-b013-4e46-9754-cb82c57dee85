<template>
    <view
        class="wsd-voice-input-icon"
        :style="strokeStyles"
    >
        <view
            v-for="stroke in strokes"
            :key="stroke.id"
            class="wsd-voice-input-icon__stroke"
            :class="[strokeRound && 'is-round', starting && 'is-starting', awaiting && 'is-awaiting']"
            :style="getStrokeStyle(stroke)"
            :animation="stroke.animationCfg"
        ></view>
    </view>
</template>

<script setup lang="ts">
import { computed, ref, unref } from "vue";
import type { CSSProperties } from "vue";

defineOptions({
    name: "wsd-voice-input-icon",
});

export interface VoiceInputIconProps {
    width?: string | number;
    height?: string | number;
    strokeWidth: string | number;
    strokeHeight: string | number;
    strokeColor: string;
    strokeCount?: number; // [8, 500]
    strokeRound?: boolean;
    initialScale?: number; // (0, 10]
    minScale?: number; // (0, 10]
    maxScale?: number; // (0, 10]
    awaitScale?: number; // (0, 10]
    awaitDelay?: number;
    awaitDuration?: number; // ms
    startDuration?: number; // ms
    weight?: number;
}

export interface StrokeItem {
    id: string;
    scale: number;
    delay: number;
    duration: number;
    currentScale: number;
    animationCfg?: UniApp.Animation;
    timing?: "ease-in" | "ease-out" | "linear" | "ease-in-out";
    animation?: "voice-icon-await-ani" | "voice-icon-start-ani";
    direction?: "normal" | "alternate";
}

let awaitInterval: ReturnType<typeof setInterval> = null;
let startInterval: ReturnType<typeof setInterval> = null;

const props = withDefaults(defineProps<VoiceInputIconProps>(), {
    strokeColor: "rgba(50, 50, 50, 1)",
    strokeWidth: 2,
    strokeHeight: 10,
    strokeCount: 16,
    strokeRound: true,
    initialScale: 1,
    minScale: 0.2,
    maxScale: 4,
    awaitScale: 1.8,
    awaitDuration: 3600, // ms
    awaitDelay: 120, // ms
    startDuration: 240, // ms
    weight: 100, // (1, 100]
});

const strokes = ref<StrokeItem[]>([]);
const starting = ref<boolean>(false);
const awaiting = ref<boolean>(false);
const lastWeight = ref<number>(props.weight);

const strokeStyles = computed<CSSProperties>(() => {
    return {
        "--stroke-wrapper-width": !props.width
            ? "auto"
            : typeof props.width === "string"
            ? props.width
            : `${props.width}px`,
        "--stroke-wrapper-height": !props.height
            ? "auto"
            : typeof props.height === "string"
            ? props.height
            : `${props.height}px`,
        "--stroke-width":
            typeof props.strokeWidth === "string"
                ? props.strokeWidth
                : `${props.strokeWidth}px`,
        "--stroke-height":
            typeof props.strokeHeight === "string"
                ? props.strokeHeight
                : `${props.strokeHeight}px`,
        "--stroke-color": `${props.strokeColor}`,
        "--stroke-scale": props.initialScale,
    };
});

const defaultAwaitScale = computed<number>(
    () => (props.maxScale - props.minScale) / 2 + props.minScale
);

const finalAwaitScale = computed<number>(
    () => props.awaitScale || unref(defaultAwaitScale)
);

const middleIndex = computed<number>(() => props.strokeCount / 2);

function getIdPrefix(): number {
    return new Date().getTime();
}

function getStrokeStyle(stroke: StrokeItem): CSSProperties {
    if (starting.value || !awaiting.value) return {};
    return {
        "--stroke-scale-now": stroke.scale,
        "--stroke-ani-name": stroke.animation,
        "--stroke-ani-direction": stroke.direction,
        "--stroke-ani-timing-function": stroke.timing,
        "--stroke-ani-delay": `${stroke.delay / 1000}s`,
        "--stroke-ani-duration": `${stroke.duration / 1000}s`,
    };
}

function initSrokes() {
    const prefix = getIdPrefix();
    strokes.value = [];
    for (let i = 0; i <= props.strokeCount; i++) {
        strokes.value.push({
            id: `${prefix}-${i}`,
            scale: props.initialScale,
            currentScale: props.initialScale,
            delay: 0,
            duration: 0,
            animationCfg: undefined,
            timing: "linear",
            animation: "voice-icon-await-ani",
            direction: "normal",
        });
    }
}

function stopRecording() {
    if (startInterval) {
        clearInterval(startInterval);
    }
    if (!awaiting.value && !starting.value) return;
    awaiting.value = false;
    starting.value = false;
    initSrokes();
}

// 等待录音
function awaitRecording() {
    if (startInterval) {
        clearInterval(startInterval);
    }

    if (awaiting.value) return;

    if (!strokes.value.length) {
        initSrokes();
    }

    starting.value = false;
    awaiting.value = true;

    const prefix = getIdPrefix();
    strokes.value = strokes.value.map((item: StrokeItem, index: number) => {
        const step = Math.floor(Math.abs(unref(middleIndex) - index));
        return {
            id: `${prefix}-${index}`,
            animationCfg: undefined,
            scale: unref(finalAwaitScale),
            currentScale: unref(finalAwaitScale),
            delay: step * props.awaitDelay + 50,
            duration: props.awaitDuration,
            timing: "linear",
            animation: "voice-icon-await-ani",
            direction: "normal",
        };
    });
}

// 开始录音
function startRecording() {
    if (awaitInterval) {
        clearTimeout(awaitInterval);
    }

    if (starting.value) return;

    if (!strokes.value.length) {
        initSrokes();
    }

    starting.value = true;
    awaiting.value = false;

    lastWeight.value = props.weight;
    const prefix = getIdPrefix();
    strokes.value = strokes.value.map((item: StrokeItem, index: number) => {
        // const scale = getRandomScale(index, props.weight);
        return {
            id: `${prefix}-${index}`,
            animationCfg: undefined,
            scale: props.initialScale,
            currentScale: props.initialScale,
            delay: 0,
            duration: props.startDuration / 2,
            // timing: "linear",
            // animation: "voice-icon-await-ani",
            // direction: "normal",
        };
    });

    startAnimationStart(true);
}

function startAnimationStart(init = false) {
    const interval = (changed: boolean) => {
        strokes.value = strokes.value.map((item: StrokeItem, index: number) => {
            if (changed) {
                item.scale = getRandomScale(index, lastWeight.value);
            }
            const reset = item.currentScale !== props.initialScale;
            item.currentScale = reset ? props.initialScale : item.scale;
            item.animationCfg = getStartAnimationStep(
                index,
                item.currentScale,
                reset ? "ease-out" : "ease-in"
            );
            return item;
        });
    };

    if (init) {
        interval(true);
    }

    startInterval = setInterval(() => {
        let changed = lastWeight.value !== props.weight;
        if (changed) {
            lastWeight.value = props.weight;
        } else {
            changed = Math.random() > 0.75;
        }
        interval(changed);
    }, props.startDuration / 2);
}

function getRandomScale(i: number, w: number) {
    const mid = strokes.value.length / 2;
    const step = Math.floor(Math.abs(mid - i));
    const weight = Math.max(1, Math.min(w, 100));
    const weightRatio = 1 - (100 - weight) / 100; // (0, 1]
    const parter = 10 / (5 * weightRatio);
    const scaleOffset = Math.max(0.07, 0.4 * weightRatio);
    const stepScale = Math.max(
        props.minScale,
        Math.min(props.maxScale * 1.5 * weightRatio, props.maxScale)
    );
    const distanceOffset = Math.max(0.7, Math.min(3, 3 * weightRatio));

    let scale = Math.random();

    if (step < mid / parter) {
        scale = scale < scaleOffset ? scale + scaleOffset : scale;
    }

    scale *= Math.min(1, distanceOffset * (1 - step / mid)) * stepScale;

    scale = Math.max(props.minScale, Math.min(props.maxScale, scale));

    return scale;
}

function getStartAnimationStep(
    index: number,
    scale: number,
    timingFunction: UniApp.CreateAnimationOptions["timingFunction"] = "linear"
): UniApp.Animation {
    const animation = uni.createAnimation({
        transformOrigin: "50% 50%",
        duration: props.startDuration / 2,
        timingFunction: timingFunction,
        delay: Math.random() * 20,
    });

    // animation.scaleY(props.initialScale).step({duration: 0});
    animation.scaleY(scale).step();

    return animation.export();
}

defineExpose({
    stopRecording,
    startRecording,
    awaitRecording,
});
</script>

<style scoped lang="scss">
.wsd-voice-input-icon {
    position: relative;
    box-sizing: border-box;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: var(--stroke-wrapper-width);
    height: var(--stroke-wrapper-height);

    &__stroke {
        position: relative;
        box-sizing: border-box;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        transform-origin: center center;
        transform: scaleY(var(--stroke-scale));
        background: var(--stroke-color);
        width: var(--stroke-width);
        height: var(--stroke-height);

        &.is-round {
          border-radius: calc(var(--stroke-width) / 2);
        }

        &.is-awaiting {
            animation-name: voice-icon-await-ani;
            animation-duration: var(--stroke-ani-duration);
            animation-delay: var(--stroke-ani-delay);
            animation-timing-function: var(--stroke-ani-timing-function);
            animation-direction: var(--stroke-ani-direction);
            animation-iteration-count: infinite;
        }

        &.is-starting {
            animation-name: none !important;
        }

        @keyframes voice-icon-await-ani {
            0% {
                transform: scaleY(var(--stroke-scale));
            }

            15% {
                transform: scaleY(var(--stroke-scale-now));
            }

            30% {
                transform: scaleY(var(--stroke-scale));
            }

            100% {
                transform: scaleY(var(--stroke-scale));
            }
        }

        @keyframes voice-icon-start-ani {
            0% {
                transform: scaleY(var(--stroke-scale));
            }

            100% {
                transform: scaleY(var(--stroke-scale-now));
            }
        }
    }

    .wsd-voice-input-icon__stroke + .wsd-voice-input-icon__stroke {
        margin-left: var(--stroke-width);
    }
}
</style>
