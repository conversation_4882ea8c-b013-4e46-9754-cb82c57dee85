<template>
  <view class="poster-component" :style="containerStyle">
    <view class="poster-wrapper" :style="wrapperStyle">
      <!-- 海报图片 -->
      <view class="poster-image-container" :style="imageContainerStyle">
        <uv-image
          :src="config.imageUrl"
          :width="'100%'"
          :height="'100%'"
          :border-radius="config.borderRadius || 0"
          :mode="config.imageMode || 'aspectFill'"
          :show-loading="true"
          :show-error="true"
          :fade="true"
          class="poster-image"
          @click="handlePosterClick"
        />
        
        <!-- 海报覆盖层内容 -->
        <view v-if="hasOverlayContent" class="poster-overlay" :style="overlayStyle">
          <!-- 标题区域 -->
          <view v-if="config.title || config.subtitle" class="overlay-header">
            <text v-if="config.title" class="overlay-title" :style="titleStyle">{{ config.title }}</text>
            <text v-if="config.subtitle" class="overlay-subtitle" :style="subtitleStyle">{{ config.subtitle }}</text>
          </view>
          
          <!-- 内容区域 -->
          <view v-if="config.content" class="overlay-content">
            <text class="overlay-text" :style="contentStyle">{{ config.content }}</text>
          </view>
          
          <!-- 按钮区域 -->
          <view v-if="config.showButton" class="overlay-actions">
            <uv-button
              :text="config.buttonText || '立即查看'"
              :color="config.buttonColor || theme.colors.primary"
              :size="config.buttonSize || 'small'"
              :custom-style="buttonStyle"
              @click.stop="handleButtonClick"
            />
          </view>
          
          <!-- 角标 -->
          <view v-if="config.badge" class="poster-badge" :style="badgeStyle">
            <text class="badge-text">{{ config.badge }}</text>
          </view>
        </view>
        
        <!-- 播放按钮（视频海报） -->
        <view v-if="config.type === 'video'" class="play-button" @click.stop="handlePlayVideo">
          <uv-icon name="play-circle-fill" color="#ffffff" size="48" />
        </view>
      </view>
      
      <!-- 海报底部信息 -->
      <view v-if="config.showBottomInfo" class="poster-bottom" :style="bottomStyle">
        <view class="bottom-content">
          <view class="bottom-left">
            <text v-if="config.title" class="bottom-title">{{ config.title }}</text>
            <text v-if="config.description" class="bottom-desc">{{ config.description }}</text>
          </view>
          
          <view v-if="config.showShareButton" class="bottom-right">
            <uv-button
              text="分享"
              type="info"
              size="mini"
              plain
              @click="handleShare"
            />
          </view>
        </view>
        
        <!-- 标签列表 -->
        <view v-if="config.tags && config.tags.length > 0" class="tags-container">
          <text
            v-for="tag in config.tags"
            :key="tag"
            class="tag-item"
            :style="tagStyle"
          >#{{ tag }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { convertStyleToCSS } from '../../utils/style-helper';
import { theme } from '../../config/theme';

interface PosterConfig {
  type: 'image' | 'video';
  imageUrl: string;
  videoUrl?: string;
  title?: string;
  subtitle?: string;
  content?: string;
  description?: string;
  badge?: string;
  tags?: string[];
  width: number | string;
  height: number | string;
  borderRadius: number;
  imageMode: string;
  overlayPosition: 'top' | 'center' | 'bottom';
  overlayBackground: string;
  titleColor: string;
  titleSize: number;
  subtitleColor: string;
  subtitleSize: number;
  contentColor: string;
  contentSize: number;
  badgeBackgroundColor: string;
  badgeTextColor: string;
  badgePosition: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  showButton: boolean;
  buttonText: string;
  buttonColor: string;
  buttonSize: string;
  showBottomInfo: boolean;
  showShareButton: boolean;
  backgroundColor: string;
  clickable: boolean;
  clickType: string;
  url: string;
  message: string;
  margin: number;
}

interface Props {
  config: PosterConfig;
  style?: any;
}

const props = defineProps<Props>();

// 容器样式
const containerStyle = computed(() => {
  if (!props.style) {
    return { width: '100%' };
  }
  
  const baseStyle = convertStyleToCSS(props.style);
  
  const processedStyle = {};
  Object.keys(baseStyle).forEach(key => {
    processedStyle[key] = String(baseStyle[key]);
  });
  
  return {
    ...processedStyle,
    width: '100%',
  };
});

// 包装器样式
const wrapperStyle = computed(() => {
  return {
    backgroundColor: props.config.backgroundColor || 'transparent',
    borderRadius: `${props.config.borderRadius || 0}px`,
    margin: `${props.config.margin || theme.spacing.sm}px`,
    overflow: 'hidden',
  };
});

// 图片容器样式
const imageContainerStyle = computed(() => {
  let width = props.config.width;
  let height = props.config.height;
  
  // 处理百分比和字符串
  if (typeof width === 'string') {
    width = width.includes('%') ? width : `${width}px`;
  } else {
    width = `${width}px`;
  }
  
  if (typeof height === 'string') {
    height = height.includes('%') ? height : `${height}px`;
  } else {
    height = `${height}px`;
  }
  
  return {
    position: 'relative',
    width: width,
    height: height,
    borderRadius: `${props.config.borderRadius || 0}px`,
    overflow: 'hidden',
    cursor: props.config.clickable ? 'pointer' : 'default',
  };
});

// 是否有覆盖层内容
const hasOverlayContent = computed(() => {
  return props.config.title || props.config.subtitle || props.config.content || props.config.showButton || props.config.badge;
});

// 覆盖层样式
const overlayStyle = computed(() => {
  const position = props.config.overlayPosition || 'bottom';
  let justifyContent = 'flex-end';
  
  switch (position) {
    case 'top':
      justifyContent = 'flex-start';
      break;
    case 'center':
      justifyContent = 'center';
      break;
    case 'bottom':
    default:
      justifyContent = 'flex-end';
      break;
  }
  
  return {
    background: props.config.overlayBackground || 'linear-gradient(transparent, rgba(0, 0, 0, 0.6))',
    justifyContent,
  };
});

// 标题样式
const titleStyle = computed(() => {
  return {
    color: props.config.titleColor || '#ffffff',
    fontSize: `${props.config.titleSize || 18}px`,
    fontWeight: '600',
  };
});

// 副标题样式
const subtitleStyle = computed(() => {
  return {
    color: props.config.subtitleColor || '#ffffff',
    fontSize: `${props.config.subtitleSize || 14}px`,
  };
});

// 内容样式
const contentStyle = computed(() => {
  return {
    color: props.config.contentColor || '#ffffff',
    fontSize: `${props.config.contentSize || 12}px`,
  };
});

// 徽章样式
const badgeStyle = computed(() => {
  const position = props.config.badgePosition || 'top-right';
  let positionStyle = {};
  
  switch (position) {
    case 'top-left':
      positionStyle = { top: '8px', left: '8px' };
      break;
    case 'top-right':
      positionStyle = { top: '8px', right: '8px' };
      break;
    case 'bottom-left':
      positionStyle = { bottom: '8px', left: '8px' };
      break;
    case 'bottom-right':
      positionStyle = { bottom: '8px', right: '8px' };
      break;
  }
  
  return {
    ...positionStyle,
    backgroundColor: props.config.badgeBackgroundColor || '#ff4757',
    color: props.config.badgeTextColor || '#ffffff',
  };
});

// 按钮样式
const buttonStyle = computed(() => {
  return {
    backgroundColor: props.config.buttonColor || theme.colors.primary,
    color: '#ffffff',
    borderRadius: '20px',
  };
});

// 底部样式
const bottomStyle = computed(() => {
  return {
    backgroundColor: props.config.backgroundColor || '#ffffff',
    padding: `${theme.spacing.sm}px`,
  };
});

// 标签样式
const tagStyle = computed(() => {
  return {
    backgroundColor: '#f0f8ff',
    color: '#1890ff',
  };
});

// 处理海报点击
const handlePosterClick = () => {
  if (!props.config.clickable) return;
  
  if (props.config.clickType === 'navigate' && props.config.url) {
    uni.navigateTo({
      url: props.config.url,
      fail: () => {
        uni.showToast({ title: '页面跳转失败', icon: 'none' });
      }
    });
  } else if (props.config.clickType === 'message' && props.config.message) {
    uni.showToast({ title: props.config.message, icon: 'none' });
  }
};

// 处理按钮点击
const handleButtonClick = () => {
  handlePosterClick();
};

// 处理播放视频
const handlePlayVideo = () => {
  if (props.config.videoUrl) {
    uni.navigateTo({
      url: `/pages/video/player?url=${encodeURIComponent(props.config.videoUrl)}`,
      fail: () => {
        uni.showToast({ title: '播放视频', icon: 'none' });
      }
    });
  }
};

// 处理分享
const handleShare = () => {
  uni.showShareMenu({
    title: props.config.title || '精彩内容分享',
    path: `/pages/poster/detail?id=${props.config.imageUrl}`,
    fail: () => {
      uni.showToast({ title: '分享功能暂未开放', icon: 'none' });
    }
  });
};
</script>

<style lang="scss" scoped>
.poster-component {
  width: 100%;
}

.poster-wrapper {
  width: 100%;
}

.poster-image-container {
  position: relative;
  
  &:active {
    opacity: 0.9;
  }
}

.poster-image {
  width: 100%;
  height: 100%;
}

.poster-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  padding: 16px;
  z-index: 2;
}

.overlay-header {
  margin-bottom: 8px;
}

.overlay-title {
  display: block;
  margin-bottom: 4px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.overlay-subtitle {
  display: block;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.overlay-content {
  flex: 1;
  margin-bottom: 8px;
}

.overlay-text {
  line-height: 1.4;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.overlay-actions {
  align-self: flex-start;
}

.poster-badge {
  position: absolute;
  padding: 4px 8px;
  border-radius: 4px;
  z-index: 3;
}

.badge-text {
  font-size: 12px;
  font-weight: 600;
}

.play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 3;
  cursor: pointer;
  
  &:active {
    opacity: 0.8;
  }
}

.poster-bottom {
  border-top: 1px solid #f0f0f0;
}

.bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.bottom-left {
  flex: 1;
}

.bottom-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
  display: block;
}

.bottom-desc {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  display: block;
}

.bottom-right {
  flex-shrink: 0;
  margin-left: 12px;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.tag-item {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
}

// 响应式适配
@media screen and (max-width: 750px) {
  .poster-overlay {
    padding: 12px;
  }
  
  .overlay-title {
    font-size: 16px;
  }
  
  .overlay-subtitle {
    font-size: 12px;
  }
  
  .bottom-title {
    font-size: 14px;
  }
}
</style>
