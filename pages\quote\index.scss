.content {
    height: 100%;
    background: linear-gradient(180deg, #bbeae2 0%, #b2e2ed 43%, #d8e7ed 63%, #d5f0ed 82%, #f6f9fa 100%);
    //  display: flex;
    // flex-direction: column;
    .header {
        display: flex;
        align-items: flex-end;
        margin-left: 22px;
        .title {
            font-size: 18px;
            font-weight: 500;
            letter-spacing: 0.36px;
            color: #000000;
            padding-bottom: 10px;
            font-weight: 600;
        }
    }
    .body {
        flex: 1;
    }
    .action {
        padding: 0 16px;
        display: flex;
        gap: 9px;
        height: 176px;

        .name {
            font-size: 15px;
            font-weight: 600;
            color: #066693;
            margin-left: 15px;
        }
        .item-bg {
            padding: 20px 0 10px;
            border-radius: 12px;
            background: linear-gradient(153deg, #def8ff 25%, #f0fdff 48%);
            box-shadow: 0px 2.5px 12.5px 0px rgba(21, 105, 137, 0.08);
        }

        .redirect {
            width: 16px;
            height: 16px;
            margin-left: 10px;
        }

        .item {
            flex: 1;
            .icon-box {
                width: 100%;
                height: 77px;
                position: relative;
                margin-top: 32px;
                overflow: hidden;
            }
            .camera {
                width: 89px;
                height: 77px;
                position: absolute;
                right: -12px;
            }
        }
        .chat-voice {
            display: flex;
            flex-direction: column;
            gap: 8px;
            overflow: hidden;
            flex: 1;
            .item-bg {
                padding-bottom: 0;
            }
            .chat-action,
            .voice-action {
                flex: 1;
                display: flex;
                flex-direction: column;
                .redirect {
                    position: absolute;
                    bottom: 10px;
                }
            }
            .chat-action-chat-file {
                width: 100%;
                flex: 1;
                position: relative;
                overflow: hidden;
                .chat-file {
                    width: 40px;
                    height: 41px;
                    position: absolute;
                    right: 0;
                }
            }
            .chat-action-microphone {
                width: 100%;
                flex: 1;
                position: relative;
                overflow: hidden;
                .microphone {
                    width: 42px;
                    height: 64px;
                    position: absolute;
                    right: 0;
                }
            }
        }
    }
    .quote {
        padding: 0 16px 16px;
        margin-top: 20px;
        flex: 1;
        display: flex;
        flex-direction: column;
        .recent-inquiries {
            width: 86px;
            height: 16px;
            margin-bottom: 10px;
        }
        .scroll-view {
            flex: 1;
        }
        .record {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 10px;
            .item {
                border-radius: 6px;
                padding: 10px;
                background: #ffffff;
                display: flex;
                align-items: center;
                justify-content: space-between;
                .company {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    .company_icon {
                        width: 28px;
                        height: 28px;
                    }
                    .company_info {
                        display: flex;
                        flex-direction: column;
                        justify-content: space-between;
                        .name {
                            font-size: 14px;
                            color: #303133;
                            margin-bottom: 2px;
                        }
                        .date {
                            font-size: 12px;
                            color: #a8abb2;
                        }
                    }
                }
                .price {
                    display: flex;
                    font-weight: 500;
                    color: #f56c6c;
                    align-items: center;
                    .price_icon {
                        font-size: 12px;
                    }
                    .total {
                        font-size: 16px;
                    }
                }
            }
        }
    }
}
.voice_container {
    background-color: #ffffff;
    height: 50vh;
    .voice {
        padding:50px 20px 20px;
    }
    .again-voice {
        font-size: 15px;
        color: #00b678;
        padding: 10px;
        margin-top: 10px;
        display: flex;
        justify-content: center;
        text-align: center;
    }
    .action_box {
        display: flex;
        margin-top: 20px;
        gap: 8px;
        .confirm,
        .cancel {
            font-size: 14px;
            border-radius: 4px;
            flex: 1;
            padding: 5px 0;
            text-align: center;
        }
        .cancel {
            color: #303133;
            border: 1px solid #dcdfe6;
        }
        .confirm {
            color: #ffffff;
            background: #00b678;
        }
    }
}
