import { uploadFile, get } from '@/utils/http';
import type { IFile, Response } from '@/models';

/**  上传文件 */
export const uploadImage = (file: string): Promise<Response<IFile>> => {
    return uploadFile('admin-api/infra/file/upload', file);
};

/**  上传语音 */
export const uploadVoiceAnalysis = (file: string): Promise<Response<string>> => {
    return uploadFile('admin-api/business/quotationInquiry/parseProductByVoice', file);
};

/** 批量上传 */
export const batchUploadImage = (files: Array<string>) => {
    if (!Array.isArray(files) || files.length === 0) {
        return [];
    }
    return Promise.all(files.map((item) => uploadImage(item)));
};

/** 批量上传 */
export const batchUploadImageGetFiles = async (files: Array<string>): Promise<Array<IFile>> => {
    if (!Array.isArray(files) || files.length === 0) {
        return [];
    }
    const uploadRes: Array<Response<IFile>> = await Promise.all(files.map((item) => uploadImage(item)));
    if (!Array.isArray(uploadRes) || uploadRes.length === 0) {
        return [];
    }
    return uploadRes.map((x) => x.data);
};

export const getShareFile = (fileId: string): Promise<Response<string>> => {
    return get(`admin-api/infra/file/shareFile/${fileId}`);
};

export const batchGetShareFile = async (fileIds: Array<string>): Promise<Array<string>> => {
    if (!Array.isArray(fileIds) || fileIds.length === 0) {
        return [];
    }

    const fileIdRes: Array<Response<string>> = await Promise.all(fileIds.map((id) => getShareFile(id)));
    return fileIdRes.map((x) => x.data);
};
