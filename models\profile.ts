import { ITenant } from './tenant';

export interface IProfile {
    username?: string;
    nickname?: string;
    remark?: string;
    deptId?: number;
    postIds?: Array<number>;
    email?: string;
    mobile?: string;
    sex?: number;
    avatar?: string;
    id?: string;
    status?: number;
    loginIp?: string;
    loginDate?: string;
    createTime?: string;
    dept?: IDept;
    posts?: Array<IPost>;
    socialUsers?: Array<ISocialUsers>;
    tenantInfo?: ITenant;
    avatarInfo?: IAvatarInfo;

    /** true： 超级管理员 */
    isSuperAdmin?: boolean;
}
export interface IRole {
    id: string;
    name: string;
}

export interface IDept {
    id: number;
    name: string;
}

export interface IPost {
    id: number;
    name: string;
}

export interface ISocialUsers {
    type: number;
    openid: string;
}
export interface IAvatarInfo {
    id?: string;
    name?: string;
    url?: string;
}
