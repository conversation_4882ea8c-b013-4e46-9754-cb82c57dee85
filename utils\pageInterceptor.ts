// utils/pageInterceptor.ts
export function setupShareInterceptor() {
  // #ifdef MP-WEIXIN || MP-ALIPAY || MP-TOUTIAO
  const originalPage = Page;
  
  Page = (options) => {
    // 强制注入分享配置
    options.onShareAppMessage = options.onShareAppMessage || function() {
      return {
        title: '默认分享标题',
        path: `/${this.route}`,
        imageUrl: '/static/share.png'
      }
    };
    
    // 微信朋友圈
    // #ifdef MP-WEIXIN
    options.onShareTimeline = options.onShareTimeline || function() {
      return {
        title: '朋友圈默认标题',
        query: '',
        imageUrl: '/static/share.png'
      }
    };
    // #endif
    
    return originalPage(options);
  };
  // #endif
}