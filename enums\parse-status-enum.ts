/** 解析状态 */
export enum ParseStatusEnum {
  /** 等待解析 */
  WAITING_PARSE = 0,

  /** 解析中 */
  PARSING = 1,

  /** 解析完成 */
  PARSED = 3,

  /** 解析中止 */
  PARSING_STOP = 2
}

export const ParseStatusEnumMapDesc: Record<ParseStatusEnum, string> = {
  [ParseStatusEnum.WAITING_PARSE]: "等待解析",
  [ParseStatusEnum.PARSING]: "解析中",
  [ParseStatusEnum.PARSED]: "解析完成",
  [ParseStatusEnum.PARSING_STOP]: "解析中止"
};

export const ParseStatusEnumMapColor: Record<ParseStatusEnum, string> = {
  [ParseStatusEnum.WAITING_PARSE]: "info",
  [ParseStatusEnum.PARSING]: "warning",
  [ParseStatusEnum.PARSED]: "success",
  [ParseStatusEnum.PARSING_STOP]: "warning"
};

/** 解析状态 */
export enum TaskParseStatusEnum {
  /** 创建 */
  CREATED = 0,

  /** 预处理 */
  PRE_PROCESS = 1,

  /** 等待解析 */
  WAITING_PARSE = 2,

  /**  解析中 */
  PARSING = 3,

  /** 解析完成 */
  PARSED = 4,

  /**  解析中止 */
  PARSING_STOP = 5
}

export const TaskParseStatusEnumMapDesc: Record<TaskParseStatusEnum, string> = {
  [TaskParseStatusEnum.CREATED]: "创建中",
  [TaskParseStatusEnum.PRE_PROCESS]: "预处理",
  [TaskParseStatusEnum.WAITING_PARSE]: "等待解析",
  [TaskParseStatusEnum.PARSING]: "解析中",
  [TaskParseStatusEnum.PARSED]: "解析完成",
  [TaskParseStatusEnum.PARSING_STOP]: "解析中止"
};

export const TaskParseStatusEnumMapColor: Record<TaskParseStatusEnum, string> = {
  [TaskParseStatusEnum.CREATED]: "info",
  [TaskParseStatusEnum.PRE_PROCESS]: "warning",
  [TaskParseStatusEnum.WAITING_PARSE]: "info",
  [TaskParseStatusEnum.PARSING]: "warning",
  [TaskParseStatusEnum.PARSED]: "success",
  [TaskParseStatusEnum.PARSING_STOP]: "danger"
};
