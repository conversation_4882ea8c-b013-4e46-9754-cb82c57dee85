<template>
  <view class="product-tab-component" :style="containerStyle">
    <view class="product-tab-wrapper" :style="wrapperStyle">
      <!-- 标签页头部 -->
      <view class="tab-header">
        <scroll-view scroll-x :show-scrollbar="false" class="tab-scroll">
          <view class="tab-list">
            <view
              v-for="(category, index) in config.categories"
              :key="category.id"
              class="tab-item"
              :class="{ 'active': activeTabIndex === index }"
              :style="getTabItemStyle(index)"
              @click="handleTabClick(index)"
            >
              <text class="tab-text">{{ category.name }}</text>
              <view v-if="activeTabIndex === index" class="tab-indicator" :style="indicatorStyle"></view>
            </view>
          </view>
        </scroll-view>
      </view>
      
      <!-- 商品内容区域 -->
      <view class="tab-content" :style="contentStyle">
        <view
          v-if="config.displayMode === 'vertical'"
          class="vertical-layout"
        >
          <!-- 垂直布局：左侧标签，右侧商品 -->
          <view class="vertical-tabs">
            <view
              v-for="(category, index) in config.categories"
              :key="category.id"
              class="vertical-tab-item"
              :class="{ 'active': activeTabIndex === index }"
              @click="handleTabClick(index)"
            >
              <text class="vertical-tab-text">{{ category.name }}</text>
            </view>
          </view>
          
          <view class="vertical-products">
            <view class="products-grid" :style="productsGridStyle">
              <view
                v-for="product in visibleProducts"
                :key="product.id"
                class="product-item"
                :style="productItemStyle"
                @click="handleProductClick(product)"
              >
                <view class="product-image-wrapper">
                  <uv-image
                    :src="product.image"
                    :width="'100%'"
                    :height="'100%'"
                    :border-radius="4"
                    mode="aspectFill"
                    class="product-image"
                  />
                </view>
                
                <view class="product-info">
                  <text class="product-title">{{ product.title }}</text>
                  <view class="product-price">
                    <text class="current-price">¥{{ product.price }}</text>
                    <text v-if="product.originalPrice" class="original-price">¥{{ product.originalPrice }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 水平布局：上方标签，下方商品 -->
        <view v-else class="horizontal-layout">
          <view class="products-grid" :style="productsGridStyle">
            <view
              v-for="product in visibleProducts"
              :key="product.id"
              class="product-item"
              :style="productItemStyle"
              @click="handleProductClick(product)"
            >
              <view class="product-image-wrapper">
                <uv-image
                  :src="product.image"
                  :width="'100%'"
                  :height="'100%'"
                  :border-radius="4"
                  mode="aspectFill"
                  class="product-image"
                />
              </view>
              
              <view class="product-info">
                <text class="product-title">{{ product.title }}</text>
                <view class="product-price">
                  <text class="current-price">¥{{ product.price }}</text>
                  <text v-if="product.originalPrice" class="original-price">¥{{ product.originalPrice }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 查看更多按钮 -->
        <view v-if="hasMoreProducts" class="more-action">
          <uv-button
            text="查看更多商品"
            type="info"
            size="small"
            plain
            @click="handleViewMore"
          />
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { convertStyleToCSS } from '../../utils/style-helper';
import { theme } from '../../config/theme';

interface Product {
  id: string;
  title: string;
  price: number;
  originalPrice?: number;
  image: string;
  categoryId: string;
}

interface Category {
  id: string;
  name: string;
  products: Product[];
}

interface ProductTabConfig {
  displayMode: 'horizontal' | 'vertical';
  maxVisibleProducts: number;
  productSpacing: number;
  categories: Category[];
  backgroundColor: string;
  tabBackgroundColor: string;
  activeTabColor: string;
  inactiveTabColor: string;
  tabTextColor: string;
  activeTabTextColor: string;
  indicatorColor: string;
  borderRadius: number;
  showBorder: boolean;
  borderColor: string;
  margin: number;
  productColumns: number;
}

interface Props {
  config: ProductTabConfig;
  style?: any;
}

const props = defineProps<Props>();

// 当前活跃标签索引
const activeTabIndex = ref(0);

// 容器样式
const containerStyle = computed(() => {
  if (!props.style) {
    return { width: '100%' };
  }
  
  const baseStyle = convertStyleToCSS(props.style);
  
  const processedStyle = {};
  Object.keys(baseStyle).forEach(key => {
    processedStyle[key] = String(baseStyle[key]);
  });
  
  return {
    ...processedStyle,
    width: '100%',
  };
});

// 包装器样式
const wrapperStyle = computed(() => {
  return {
    backgroundColor: props.config.backgroundColor || '#ffffff',
    borderRadius: `${props.config.borderRadius || theme.borderRadius.md}px`,
    border: props.config.showBorder ? `1px solid ${props.config.borderColor || theme.colors.border.light}` : 'none',
    margin: `${props.config.margin || theme.spacing.sm}px`,
    overflow: 'hidden',
  };
});

// 内容样式
const contentStyle = computed(() => {
  return {
    padding: `${theme.spacing.md}px`,
  };
});

// 标签项样式
const getTabItemStyle = (index: number) => {
  const isActive = activeTabIndex.value === index;
  return {
    backgroundColor: isActive 
      ? (props.config.activeTabColor || theme.colors.primary) 
      : (props.config.tabBackgroundColor || 'transparent'),
    color: isActive 
      ? (props.config.activeTabTextColor || '#ffffff') 
      : (props.config.tabTextColor || theme.colors.text.primary),
  };
};

// 指示器样式
const indicatorStyle = computed(() => {
  return {
    backgroundColor: props.config.indicatorColor || theme.colors.primary,
  };
});

// 商品网格样式
const productsGridStyle = computed(() => {
  const columns = props.config.productColumns || 2;
  return {
    display: 'grid',
    gridTemplateColumns: `repeat(${columns}, 1fr)`,
    gap: `${props.config.productSpacing || theme.spacing.sm}px`,
  };
});

// 商品项样式
const productItemStyle = computed(() => {
  return {
    backgroundColor: '#ffffff',
    borderRadius: `${theme.borderRadius.sm}px`,
    border: `1px solid ${theme.colors.border.light}`,
    overflow: 'hidden',
    cursor: 'pointer',
  };
});

// 当前可见商品
const visibleProducts = computed(() => {
  if (!props.config.categories[activeTabIndex.value]) return [];
  
  const currentCategory = props.config.categories[activeTabIndex.value];
  const maxCount = props.config.maxVisibleProducts || 6;
  return currentCategory.products.slice(0, maxCount);
});

// 是否有更多商品
const hasMoreProducts = computed(() => {
  if (!props.config.categories[activeTabIndex.value]) return false;
  
  const currentCategory = props.config.categories[activeTabIndex.value];
  const maxCount = props.config.maxVisibleProducts || 6;
  return currentCategory.products.length > maxCount;
});

// 处理标签点击
const handleTabClick = (index: number) => {
  activeTabIndex.value = index;
};

// 处理商品点击
const handleProductClick = (product: Product) => {
  uni.navigateTo({
    url: `/pages/product/detail?id=${product.id}`,
    fail: () => {
      uni.showToast({
        title: `查看商品: ${product.title}`,
        icon: 'none'
      });
    }
  });
};

// 处理查看更多
const handleViewMore = () => {
  const currentCategory = props.config.categories[activeTabIndex.value];
  uni.navigateTo({
    url: `/pages/product/list?categoryId=${currentCategory.id}`,
    fail: () => {
      uni.showToast({
        title: `查看${currentCategory.name}更多商品`,
        icon: 'none'
      });
    }
  });
};
</script>

<style lang="scss" scoped>
.product-tab-component {
  width: 100%;
}

.product-tab-wrapper {
  width: 100%;
}

.tab-header {
  border-bottom: 1px solid #f0f0f0;
}

.tab-scroll {
  width: 100%;
  white-space: nowrap;
}

.tab-list {
  display: flex;
  flex-direction: row;
}

.tab-item {
  position: relative;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
  
  &:active {
    opacity: 0.8;
  }
  
  &.active {
    font-weight: 600;
  }
}

.tab-text {
  font-size: 14px;
  white-space: nowrap;
}

.tab-indicator {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  border-radius: 1px;
}

.tab-content {
  width: 100%;
}

.vertical-layout {
  display: flex;
  flex-direction: row;
  gap: 16px;
}

.vertical-tabs {
  flex-shrink: 0;
  width: 80px;
  border-right: 1px solid #f0f0f0;
}

.vertical-tab-item {
  padding: 12px 8px;
  text-align: center;
  cursor: pointer;
  border-radius: 4px;
  margin-bottom: 4px;
  transition: all 0.2s ease;
  
  &:active {
    opacity: 0.8;
  }
  
  &.active {
    background-color: #f0f8ff;
    color: #1890ff;
    font-weight: 600;
  }
}

.vertical-tab-text {
  font-size: 12px;
  line-height: 1.3;
}

.vertical-products {
  flex: 1;
}

.horizontal-layout {
  width: 100%;
}

.products-grid {
  width: 100%;
}

.product-item {
  transition: all 0.2s ease;
  
  &:active {
    transform: scale(0.98);
    opacity: 0.9;
  }
}

.product-image-wrapper {
  width: 100%;
  height: 100px;
  overflow: hidden;
}

.product-image {
  width: 100%;
  height: 100%;
}

.product-info {
  padding: 8px;
}

.product-title {
  font-size: 12px;
  color: #333;
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.3;
}

.product-price {
  display: flex;
  align-items: center;
  gap: 4px;
}

.current-price {
  font-size: 14px;
  color: #ff4757;
  font-weight: 600;
}

.original-price {
  font-size: 10px;
  color: #999;
  text-decoration: line-through;
}

.more-action {
  margin-top: 16px;
  text-align: center;
}

// 响应式适配
@media screen and (max-width: 750px) {
  .vertical-tabs {
    width: 60px;
  }
  
  .vertical-tab-text {
    font-size: 11px;
  }
  
  .product-title {
    font-size: 11px;
  }
  
  .current-price {
    font-size: 12px;
  }
}
</style>
