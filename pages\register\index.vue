<template>
    <view class="content">
        <view class="form">
            <view class="item">
                <view class="label required">手机号：</view>
                <view class="value">
                    <uv-input v-model="form.phone" placeholder="请输入手机号" inputAlign="right" border="none" />
                </view>
            </view>
            <view class="item">
                <view class="label required">姓名：</view>
                <view class="value">
                    <uv-input v-model="form.username" placeholder="请输入姓名" inputAlign="right" border="none" />
                </view>
            </view>
            <view class="item">
                <view class="label">邮箱：</view>
                <view class="value">
                    <uv-input v-model="form.email" placeholder="请输入邮箱" inputAlign="right" border="none" />
                </view>
            </view>
            <view class="item">
                <view class="label required">所属企业：</view>
                <view class="value">
                    <uv-input v-model="form.enterprise" placeholder="请输入所属企业" inputAlign="right" border="none" />
                </view>
            </view>
            <view class="item">
                <view class="label required">所在部门 ：</view>
                <view class="value">
                    <uv-input v-model="form.department" placeholder="请输入所在部门" inputAlign="right" border="none" />
                </view>
            </view>
            <view class="item">
                <view class="label required">岗位：</view>
                <view class="value">
                    <uv-input v-model="form.position" placeholder="请输入岗位" inputAlign="right" border="none" />
                </view>
            </view>
        </view>
        <view class="action">
            <view class="tip">注册审核通过后，会收到微信消息提醒</view>
            <view v-if="showRegBtn" class="reg-action" @click="onSubmitReg()">注册</view>
        </view>
        <loading-page :loading="loading"></loading-page>
    </view>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue';
import { IAccountRegister } from '@/models';
import { showToast, switchTab } from '@/utils';
import { createRegister, getRegisterInfo, loginWeChat } from '@/api/account';
import { onLoad } from '@dcloudio/uni-app';
import { setStorageValue } from '@/utils/storage';
import { AuthAccessToken, emailRegex, phoneReg } from '@/consts';
import { useLoadingFn } from '@/hooks';
import { useSubscribeMessageHook } from '@/hooks/use-subscribe-message';

interface IQueryParams {
    unionId: string;
    openId: string;
    phone: string;
}

const loading = ref();
const showRegBtn = ref(false);

const form = reactive<IAccountRegister>({
    username: undefined,
    phone: undefined,
    enterprise: undefined,
    email: undefined,
    department: undefined,
    position: undefined,
    wechatMaOpenId: undefined,
    wechatUnionId: undefined,
    registerSource: 'MA'
});

const handleSubmitReg = useLoadingFn(onSubmitReg, loading);
const subscribeMessageHook = useSubscribeMessageHook();

onLoad(async (query: IQueryParams) => {
    if (query && Object.keys(query).length) {
        form.phone = query.phone;
        form.wechatUnionId = query.unionId;
        form.wechatMaOpenId = query.openId;
        const { data } = await getRegisterInfo({ wechatUnionId: form.wechatUnionId, wechatMaOpenId: form.wechatMaOpenId });
        if (data && Object.keys(data).length) {
            Object.assign(form, data);
            showRegBtn.value = false;
        } else {
            showRegBtn.value = true;
        }
    }
});

async function onSubmitReg() {
    if (!form.phone?.trim()) {
        showToast({ title: '手机号码不能为空', icon: 'none' });
        return;
    }

    if (!phoneReg.test(form.phone)) {
        showToast({ title: '手机号码格式不正确', icon: 'none' });
        return;
    }

    if (!form.username?.trim()) {
        showToast({ title: '姓名不能为空', icon: 'none' });
        return;
    }

    if (form.email?.trim() && !emailRegex.test(form.email.trim())) {
        showToast({ title: '邮箱格式不正确', icon: 'none' });
        return;
    }

    if (!form.enterprise?.trim()) {
        showToast({ title: '公司名称不能为空', icon: 'none' });
        return;
    }

    if (!form.department?.trim()) {
        showToast({ title: '所在部门不能为空', icon: 'none' });
        return;
    }

    if (!form.position?.trim()) {
        showToast({ title: '岗位不能为空', icon: 'none' });
        return;
    }
    await createRegister(form);
    showRegBtn.value = false;
    subscribeMessageHook.RegisterAuditResultSubscribeMessage();
    showToast({ title: '注册成功,请等待审核', icon: 'none' });
}
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>
