<template>
  <view class="article-component" :style="containerStyle">
    <view class="article-wrapper" :style="wrapperStyle">
      <!-- 文章头部 -->
      <view v-if="config.showHeader" class="article-header" :style="headerStyle">
        <view class="header-content">
          <view class="header-left">
            <view class="article-icon">
              <uv-icon name="file-text" :color="config.iconColor || theme.colors.primary" size="20" />
            </view>
            <view class="header-text">
              <text class="header-title">{{ config.title || '精选文章' }}</text>
              <text class="header-desc">{{ config.description || '优质内容推荐' }}</text>
            </view>
          </view>
          
          <view class="header-right">
            <uv-button
              text="更多文章"
              type="info"
              size="mini"
              plain
              @click="handleViewMore"
            />
          </view>
        </view>
      </view>
      
      <!-- 文章列表 -->
      <view class="articles-content" :style="contentStyle">
        <view
          v-if="config.displayMode === 'list'"
          class="articles-list"
        >
          <view
            v-for="(article, index) in visibleArticles"
            :key="article.id"
            class="article-item"
            :style="articleItemStyle"
            @click="handleArticleClick(article, index)"
          >
            <!-- 文章图片 -->
            <view v-if="article.image" class="article-image-wrapper">
              <uv-image
                :src="article.image"
                :width="'100px'"
                :height="'80px'"
                :border-radius="4"
                mode="aspectFill"
                class="article-image"
              />
            </view>
            
            <!-- 文章信息 -->
            <view class="article-info" :class="{ 'no-image': !article.image }">
              <text class="article-title">{{ article.title }}</text>
              <text v-if="article.summary" class="article-summary">{{ article.summary }}</text>
              
              <!-- 文章标签 -->
              <view v-if="article.tags && article.tags.length > 0" class="article-tags">
                <text
                  v-for="tag in article.tags.slice(0, 3)"
                  :key="tag"
                  class="tag-item"
                  :style="tagStyle"
                >#{{ tag }}</text>
              </view>
              
              <!-- 文章元信息 -->
              <view class="article-meta">
                <text class="meta-item">{{ article.author || '佚名' }}</text>
                <text class="meta-separator">·</text>
                <text class="meta-item">{{ formatDate(article.publishTime) }}</text>
                <text v-if="article.readCount" class="meta-separator">·</text>
                <text v-if="article.readCount" class="meta-item">{{ article.readCount }}阅读</text>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 卡片布局 -->
        <view v-else class="articles-grid" :style="gridStyle">
          <view
            v-for="(article, index) in visibleArticles"
            :key="article.id"
            class="article-card"
            :style="articleCardStyle"
            @click="handleArticleClick(article, index)"
          >
            <!-- 文章图片 -->
            <view v-if="article.image" class="card-image-wrapper">
              <uv-image
                :src="article.image"
                :width="'100%'"
                :height="'120px'"
                :border-radius="4"
                mode="aspectFill"
                class="card-image"
              />
              
              <!-- 文章分类标签 -->
              <view v-if="article.category" class="category-badge" :style="categoryBadgeStyle">
                <text class="category-text">{{ article.category }}</text>
              </view>
            </view>
            
            <!-- 卡片内容 -->
            <view class="card-content">
              <text class="card-title">{{ article.title }}</text>
              <text v-if="article.summary" class="card-summary">{{ article.summary }}</text>
              
              <!-- 卡片底部信息 -->
              <view class="card-footer">
                <view class="card-meta">
                  <text class="meta-author">{{ article.author || '佚名' }}</text>
                  <text class="meta-date">{{ formatDate(article.publishTime) }}</text>
                </view>
                
                <view v-if="article.readCount || article.likeCount" class="card-stats">
                  <view v-if="article.readCount" class="stat-item">
                    <uv-icon name="eye" color="#999" size="12" />
                    <text class="stat-text">{{ article.readCount }}</text>
                  </view>
                  <view v-if="article.likeCount" class="stat-item">
                    <uv-icon name="heart" color="#999" size="12" />
                    <text class="stat-text">{{ article.likeCount }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 查看更多 -->
        <view v-if="hasMoreArticles" class="more-action">
          <uv-button
            text="查看更多文章"
            type="info"
            size="small"
            plain
            @click="handleViewMore"
          />
        </view>
        
        <!-- 空状态 -->
        <view v-if="config.articles.length === 0" class="empty-state">
          <uv-icon name="file-text" color="#ccc" size="48" />
          <text class="empty-text">暂无文章内容</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { convertStyleToCSS } from '../../utils/style-helper';
import { theme } from '../../config/theme';

interface Article {
  id: string;
  title: string;
  summary?: string;
  image?: string;
  author?: string;
  category?: string;
  tags?: string[];
  publishTime: string;
  readCount?: number;
  likeCount?: number;
  link: string;
}

interface ArticleConfig {
  title: string;
  description: string;
  displayMode: 'list' | 'grid';
  columns: number;
  maxVisibleCount: number;
  articles: Article[];
  showHeader: boolean;
  backgroundColor: string;
  headerBackgroundColor: string;
  iconColor: string;
  tagBackgroundColor: string;
  tagTextColor: string;
  categoryBackgroundColor: string;
  categoryTextColor: string;
  spacing: number;
  margin: number;
}

interface Props {
  config: ArticleConfig;
  style?: any;
}

const props = defineProps<Props>();

// 容器样式
const containerStyle = computed(() => {
  if (!props.style) {
    return { width: '100%' };
  }
  
  const baseStyle = convertStyleToCSS(props.style);
  
  const processedStyle = {};
  Object.keys(baseStyle).forEach(key => {
    processedStyle[key] = String(baseStyle[key]);
  });
  
  return {
    ...processedStyle,
    width: '100%',
  };
});

// 包装器样式
const wrapperStyle = computed(() => {
  return {
    backgroundColor: props.config.backgroundColor || '#ffffff',
    borderRadius: '12px',
    margin: `${props.config.margin || theme.spacing.sm}px`,
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
    overflow: 'hidden',
  };
});

// 头部样式
const headerStyle = computed(() => {
  return {
    backgroundColor: props.config.headerBackgroundColor || '#f8f9fa',
    padding: `${theme.spacing.md}px`,
    borderBottom: '1px solid #f0f0f0',
  };
});

// 内容样式
const contentStyle = computed(() => {
  return {
    padding: `${theme.spacing.md}px`,
  };
});

// 网格样式
const gridStyle = computed(() => {
  const columns = props.config.columns || 2;
  return {
    display: 'grid',
    gridTemplateColumns: `repeat(${columns}, 1fr)`,
    gap: `${props.config.spacing || theme.spacing.sm}px`,
  };
});

// 文章项样式
const articleItemStyle = computed(() => {
  return {
    display: 'flex',
    backgroundColor: '#ffffff',
    borderRadius: '8px',
    border: '1px solid #f0f0f0',
    padding: `${theme.spacing.sm}px`,
    marginBottom: `${props.config.spacing || theme.spacing.sm}px`,
    cursor: 'pointer',
    transition: 'all 0.2s ease',
  };
});

// 文章卡片样式
const articleCardStyle = computed(() => {
  return {
    backgroundColor: '#ffffff',
    borderRadius: '8px',
    border: '1px solid #f0f0f0',
    overflow: 'hidden',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
  };
});

// 标签样式
const tagStyle = computed(() => {
  return {
    backgroundColor: props.config.tagBackgroundColor || '#f0f8ff',
    color: props.config.tagTextColor || '#1890ff',
  };
});

// 分类徽章样式
const categoryBadgeStyle = computed(() => {
  return {
    backgroundColor: props.config.categoryBackgroundColor || '#1890ff',
    color: props.config.categoryTextColor || '#ffffff',
  };
});

// 可见文章
const visibleArticles = computed(() => {
  const maxCount = props.config.maxVisibleCount || 5;
  return props.config.articles.slice(0, maxCount);
});

// 是否有更多文章
const hasMoreArticles = computed(() => {
  const maxCount = props.config.maxVisibleCount || 5;
  return props.config.articles.length > maxCount;
});

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  
  if (days === 0) {
    return '今天';
  } else if (days === 1) {
    return '昨天';
  } else if (days < 7) {
    return `${days}天前`;
  } else {
    return date.toLocaleDateString();
  }
};

// 处理文章点击
const handleArticleClick = (article: Article, index: number) => {
  if (article.link) {
    uni.navigateTo({
      url: article.link,
      fail: () => {
        uni.showToast({ title: '页面跳转失败', icon: 'none' });
      }
    });
  }
};

// 处理查看更多
const handleViewMore = () => {
  uni.navigateTo({
    url: '/pages/article/list',
    fail: () => {
      uni.showToast({ title: '查看更多文章', icon: 'none' });
    }
  });
};
</script>

<style lang="scss" scoped>
.article-component {
  width: 100%;
}

.article-wrapper {
  width: 100%;
}

.article-header {
  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .header-left {
    display: flex;
    align-items: center;
    flex: 1;
  }
  
  .article-icon {
    margin-right: 12px;
  }
  
  .header-text {
    flex: 1;
    
    .header-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      display: block;
      margin-bottom: 4px;
    }
    
    .header-desc {
      font-size: 12px;
      color: #666;
      display: block;
    }
  }
  
  .header-right {
    flex-shrink: 0;
  }
}

.articles-content {
  width: 100%;
}

.article-item {
  &:active {
    background-color: #f8f9fa;
  }
  
  &:last-child {
    margin-bottom: 0;
  }
}

.article-image-wrapper {
  margin-right: 12px;
  flex-shrink: 0;
}

.article-info {
  flex: 1;
  
  &.no-image {
    margin-left: 0;
  }
}

.article-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 6px;
  display: block;
  line-height: 1.4;
}

.article-summary {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  display: block;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.article-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-bottom: 8px;
}

.tag-item {
  font-size: 10px;
  padding: 2px 4px;
  border-radius: 2px;
}

.article-meta {
  display: flex;
  align-items: center;
  gap: 4px;
}

.meta-item {
  font-size: 10px;
  color: #999;
}

.meta-separator {
  font-size: 10px;
  color: #ccc;
}

.article-card {
  &:active {
    transform: scale(0.98);
    opacity: 0.9;
  }
}

.card-image-wrapper {
  position: relative;
}

.card-image {
  width: 100%;
}

.category-badge {
  position: absolute;
  top: 8px;
  left: 8px;
  padding: 2px 6px;
  border-radius: 4px;
}

.category-text {
  font-size: 10px;
  font-weight: 600;
}

.card-content {
  padding: 12px;
}

.card-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 6px;
  display: block;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card-summary {
  font-size: 12px;
  color: #666;
  margin-bottom: 12px;
  display: block;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-meta {
  flex: 1;
}

.meta-author {
  font-size: 10px;
  color: #666;
  margin-right: 8px;
}

.meta-date {
  font-size: 10px;
  color: #999;
}

.card-stats {
  display: flex;
  gap: 8px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 2px;
}

.stat-text {
  font-size: 10px;
  color: #999;
}

.more-action {
  margin-top: 16px;
  text-align: center;
}

.empty-state {
  padding: 40px 20px;
  text-align: center;
}

.empty-text {
  font-size: 14px;
  color: #999;
  margin-top: 12px;
  display: block;
}

// 响应式适配
@media screen and (max-width: 750px) {
  .article-item {
    .article-title {
      font-size: 13px;
    }
    
    .article-summary {
      font-size: 11px;
    }
  }
  
  .card-title {
    font-size: 13px;
  }
  
  .card-summary {
    font-size: 11px;
  }
}
</style>
