<template>
  <view class="group-buy-component" :style="containerStyle">
    <!-- 团购头部 -->
    <view class="group-buy-header" :style="headerStyle">
      <text class="title">{{ config.title }}</text>
      <text class="subtitle">{{ config.subtitle }}</text>
    </view>
    
    <!-- 团购商品列表 -->
    <view class="group-buy-products">
      <view 
        class="product-item"
        v-for="product in config.products"
        :key="product.id"
        @click="handleProductClick(product)"
      >
        <!-- 商品图片 -->
        <view class="product-image-wrapper">
          <uv-image
            :src="product.image"
            width="100"
            height="100"
            :borderRadius="theme.borderRadius.md"
            mode="aspectFill"
            :showLoading="true"
            :showError="true"
          />
        </view>
        
        <!-- 商品信息 -->
        <view class="product-info">
          <text class="product-name">{{ product.name }}</text>
          
          <view class="price-wrapper">
            <view class="group-price-section">
              <text class="group-price">¥{{ formatPrice(product.groupPrice) }}</text>
              <text class="price-label">团购价</text>
            </view>
            <text class="original-price">原价¥{{ formatPrice(product.originalPrice) }}</text>
          </view>
          
          <!-- 团购进度 -->
          <view class="group-progress">
            <view class="progress-info">
              <text class="progress-text">{{ product.currentCount }}/{{ product.groupSize }}人团</text>
              <text class="progress-status" :class="getProgressStatusClass(product)">
                {{ getProgressStatusText(product) }}
              </text>
            </view>
            
            <view class="progress-bar">
              <view 
                class="progress-fill" 
                :style="{ width: getProgressPercentage(product) + '%' }"
              />
            </view>
          </view>
          
          <!-- 参团按钮 -->
          <view class="action-wrapper">
            <uv-button 
              :type="getButtonType(product)"
              size="small"
              :text="getButtonText(product)"
              :disabled="isButtonDisabled(product)"
              @click.stop="handleJoinGroup(product)"
            />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { GroupBuyConfig } from '../../types/dynamic-page';
import { convertStyleToCSS, colorUtils } from '../../utils/style-helper';
import { theme } from '../../config/theme';

interface Props {
  config: GroupBuyConfig['config'];
  style?: GroupBuyConfig['style'];
}

const props = defineProps<Props>();

// 容器样式
const containerStyle = computed(() => {
  const baseStyle = convertStyleToCSS(props.style);
  return {
    ...baseStyle,
    width: '100%',
  };
});

// 头部样式
const headerStyle = computed(() => {
  return {
    backgroundColor: colorUtils.getThemeColorOrDefault(
      props.config.backgroundColor, 
      theme.colors.primary
    ),
    padding: `${theme.spacing.md}px`,
    color: theme.colors.text.inverse,
  };
});

// 格式化价格
const formatPrice = (price: number): string => {
  return price.toFixed(2);
};

// 获取进度百分比
const getProgressPercentage = (product: GroupBuyConfig['config']['products'][0]): number => {
  return Math.min((product.currentCount / product.groupSize) * 100, 100);
};

// 获取进度状态文本
const getProgressStatusText = (product: GroupBuyConfig['config']['products'][0]): string => {
  if (product.currentCount >= product.groupSize) {
    return '已成团';
  } else if (product.currentCount > 0) {
    return `还差${product.groupSize - product.currentCount}人`;
  } else {
    return '等待开团';
  }
};

// 获取进度状态样式类
const getProgressStatusClass = (product: GroupBuyConfig['config']['products'][0]): string => {
  if (product.currentCount >= product.groupSize) {
    return 'status-success';
  } else if (product.currentCount > 0) {
    return 'status-warning';
  } else {
    return 'status-default';
  }
};

// 获取按钮类型
const getButtonType = (product: GroupBuyConfig['config']['products'][0]): string => {
  if (product.currentCount >= product.groupSize) {
    return 'success';
  } else if (product.currentCount > 0) {
    return 'warning';
  } else {
    return 'primary';
  }
};

// 获取按钮文本
const getButtonText = (product: GroupBuyConfig['config']['products'][0]): string => {
  if (product.currentCount >= product.groupSize) {
    return '立即购买';
  } else if (product.currentCount > 0) {
    return '参与团购';
  } else {
    return '发起团购';
  }
};

// 判断按钮是否禁用
const isButtonDisabled = (product: GroupBuyConfig['config']['products'][0]): boolean => {
  return false; // 根据业务需求决定
};

// 处理商品点击
const handleProductClick = (product: GroupBuyConfig['config']['products'][0]) => {
  if (product.link) {
    uni.navigateTo({
      url: product.link,
      fail: () => {
        uni.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  }
};

// 处理参团操作
const handleJoinGroup = (product: GroupBuyConfig['config']['products'][0]) => {
  // 这里可以添加参团逻辑
  uni.showModal({
    title: '参与团购',
    content: `确定要参与${product.name}的团购吗？`,
    success: (res) => {
      if (res.confirm) {
        // 执行参团操作
        if (product.link) {
          uni.navigateTo({
            url: product.link + '?action=join-group',
            fail: () => {
              uni.showToast({
                title: '页面跳转失败',
                icon: 'none'
              });
            }
          });
        } else {
          uni.showToast({
            title: '参团成功！',
            icon: 'success'
          });
        }
      }
    }
  });
};
</script>

<style lang="scss" scoped>
.group-buy-component {
  width: 100%;
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  margin: 0 16px;
}

.group-buy-header {
  text-align: center;
}

.title {
  font-size: 18px;
  font-weight: 600;
  display: block;
  margin-bottom: 4px;
}

.subtitle {
  font-size: 14px;
  opacity: 0.9;
}

.group-buy-products {
  padding: 16px;
}

.product-item {
  display: flex;
  padding: 16px;
  border: 1px solid v-bind('theme.colors.border.light');
  border-radius: 8px;
  margin-bottom: 12px;
  background-color: white;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  &:active {
    background-color: #f9f9f9;
  }
}

.product-image-wrapper {
  margin-right: 12px;
  flex-shrink: 0;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.product-name {
  font-size: 16px;
  color: v-bind('theme.colors.text.primary');
  font-weight: 500;
  line-height: 1.4;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.price-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.group-price-section {
  display: flex;
  align-items: baseline;
  gap: 6px;
}

.group-price {
  font-size: 20px;
  font-weight: 600;
  color: v-bind('theme.colors.error');
}

.price-label {
  font-size: 12px;
  color: v-bind('theme.colors.error');
  background-color: rgba(239, 68, 68, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
}

.original-price {
  font-size: 14px;
  color: v-bind('theme.colors.text.secondary');
  text-decoration: line-through;
}

.group-progress {
  margin-bottom: 12px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.progress-text {
  font-size: 14px;
  color: v-bind('theme.colors.text.primary');
  font-weight: 500;
}

.progress-status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  
  &.status-success {
    color: v-bind('theme.colors.success');
    background-color: rgba(16, 185, 129, 0.1);
  }
  
  &.status-warning {
    color: v-bind('theme.colors.warning');
    background-color: rgba(245, 158, 11, 0.1);
  }
  
  &.status-default {
    color: v-bind('theme.colors.text.secondary');
    background-color: rgba(156, 163, 175, 0.1);
  }
}

.progress-bar {
  width: 100%;
  height: 6px;
  background-color: v-bind('theme.colors.border.light');
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: v-bind('theme.colors.primary');
  border-radius: 3px;
  transition: width 0.3s ease;
}

.action-wrapper {
  margin-top: auto;
  display: flex;
  justify-content: flex-end;
}
</style>
