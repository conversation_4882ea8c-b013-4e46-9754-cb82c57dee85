<template>
    <view
        class="wsd-voice-icon"
        :style="iconStyle"
    >
        <view class="wsd-voice-icon__span"></view>
    </view>
</template>

<script setup lang="ts">
import {
    computed,
    onBeforeUnmount,
    onMounted,
    ref,
    unref,
    type CSSProperties,
} from "vue";
defineOptions({
    name: "wsd-voice-icon",
});

export interface VoiceIconProps {
    strokeWidth?: number;
    strokeGutter?: number;
    strokeColor?: string;
    size?: number;
    angle?: number;
    animation?: boolean;
    animationSpeed?: number; // ms动画速度
    autoAnimation?: boolean;
}

const props = withDefaults(defineProps<VoiceIconProps>(), {
    size: 8,
    strokeWidth: 3,
    strokeColor: "rgba(143, 143, 143, 1)",
    angle: 75,
    animation: false,
    animationSpeed: 250,
    autoAnimation: false,
});

let animationInterval: ReturnType<typeof setInterval>;
const animationStep = ref(2);
const finalStrokeGutter = computed<number>(() =>
    Math.max(1, props.strokeGutter ?? props.strokeWidth)
);
const finalAngle = computed<number>(() => (props.angle / 180) * Math.PI);

const iconSize = computed<number>(() => {
    return props.size + 2 * props.strokeWidth + 2 * unref(finalStrokeGutter);
});
const iconInnerSize = computed<number>(() => {
    return 2 * unref(iconSize);
});
const iconOutterSize = computed<number>(() => {
    const r = Math.ceil(unref(iconInnerSize) / 2);
    const y = Math.ceil(Math.tan(unref(finalAngle) / 2) * r);
    return 2 * y;
});

const clipPath = computed<string>(() => {
    return getClipPath(unref(iconInnerSize), unref(finalAngle));
});

const iconBackgorund = computed<string>(() => {
    let steps = [];
    const stepLen = [
        props.size,
        props.size + unref(finalStrokeGutter),
        props.size + unref(finalStrokeGutter) + props.strokeWidth,
        props.size + 2 * unref(finalStrokeGutter) + props.strokeWidth,
        props.size + 2 * unref(finalStrokeGutter) + 2 * props.strokeWidth,
    ];

    if (animationStep.value === 2) {
        steps = [
            `${props.strokeColor} ${stepLen[0]}px`,
            `transparent ${stepLen[0]}px`,
            `transparent ${stepLen[1]}px`,
            `${props.strokeColor} ${stepLen[1]}px`,
            `${props.strokeColor} ${stepLen[2]}px`,
            `transparent ${stepLen[2]}px`,
            `transparent ${stepLen[3]}px`,
            `${props.strokeColor} ${stepLen[3]}px`,
            `${props.strokeColor} ${stepLen[4]}px`,
        ];
    } else if (animationStep.value === 1) {
        steps = [
            `${props.strokeColor} ${stepLen[0]}px`,
            `transparent ${stepLen[0]}px`,
            `transparent ${stepLen[1]}px`,
            `${props.strokeColor} ${stepLen[1]}px`,
            `${props.strokeColor} ${stepLen[2]}px`,
            `transparent ${stepLen[2]}px`,
            `transparent ${stepLen[3]}px`,
            `transparent ${stepLen[3]}px`,
            `transparent ${stepLen[4]}px`,
        ];
    } else if (animationStep.value === 0) {
        steps = [
            `${props.strokeColor} ${stepLen[0]}px`,
            `transparent ${stepLen[0]}px`,
            `transparent ${stepLen[1]}px`,
            `transparent ${stepLen[1]}px`,
            `transparent ${stepLen[2]}px`,
            `transparent ${stepLen[2]}px`,
            `transparent ${stepLen[3]}px`,
            `transparent ${stepLen[3]}px`,
            `transparent ${stepLen[4]}px`,
        ];
    }
    return `radial-gradient(circle at center, ${steps.join(",")})`;
});

const iconStyle = computed<CSSProperties>(() => {
    return {
        "--voice-icon-stroke-color": props.strokeColor,
        "--voice-icon-stroke-width": `${props.strokeWidth}px`,
        "--voice-icon-size": `${unref(iconSize)}px`,
        "--voice-icon-inner-size": `${unref(iconInnerSize)}px`,
        "--voice-icon-outter-size": `${unref(iconOutterSize)}px`,
        "--voice-icon-clip-path": unref(clipPath),
        "--voice-icon-background": unref(iconBackgorund),
    };
});

function getClipPath(d: number, angle: number): string {
    if (!d || !angle) return "";
    const r = Math.ceil(d / 2);
    const y = unref(iconOutterSize) / 2;
    const p1 = [`${r}px`, `${r}px`];
    const p2 = [`${d}px`, `${r - y}px`];
    const p3 = [`${d}px`, `${r}px`];
    const p4 = [`${d}px`, `${r + y}px`];

    return `polygon(${p1.join(" ")},${p2.join(" ")},${p3.join(" ")},${p4.join(
        " "
    )})`;
}

function startAnimation() {
    if (!props.animation) {
        endAnimation();
    } else {
        if (!animationInterval) {
            animationInterval = setInterval(() => {
                animationStep.value = (animationStep.value + 1) % 3;
            }, props.animationSpeed);
        }
    }
}

function endAnimation() {
    if (animationInterval) {
        clearInterval(animationInterval);
    }
    animationInterval = undefined;
    animationStep.value = 2;
}

onMounted(() => {
    if (props.animation && props.autoAnimation) {
        startAnimation();
    }
});

onBeforeUnmount(() => {
    endAnimation();
});

defineExpose({
    startAnimation,
    endAnimation,
});
</script>

<style scoped lang="scss">
.wsd-voice-icon {
    position: relative;
    box-sizing: border-box;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    transform-origin: center;
    width: var(--voice-icon-outter-size);
    height: var(--voice-icon-outter-size);

    &__span {
        position: relative;
        width: var(--voice-icon-size);
        height: var(--voice-icon-outter-size);
        box-sizing: border-box;
        border: none;
        overflow: hidden;

        &::before {
            position: absolute;
            display: inline-block;
            box-sizing: border-box;
            border: none;
            content: "";
            top: calc(
                -1 * calc(calc(
                                var(--voice-icon-inner-size) -
                                    var(--voice-icon-outter-size)
                            ) / 2)
            );
            left: calc(-1 * var(--voice-icon-size));
            width: var(--voice-icon-inner-size);
            height: var(--voice-icon-inner-size);
            border-radius: var(--voice-icon-inner-size);
            clip-path: var(--voice-icon-clip-path);
            background: var(--voice-icon-background);
        }
    }
}
</style>
