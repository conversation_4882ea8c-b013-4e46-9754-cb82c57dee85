<template>
    <view class="container">
        <view class="content">
            <view class="search">
                <uv-textarea v-model="state.pastedContent" :height="40" placeholder="请输入"></uv-textarea>
                <view class="action">
                    <view class="desc">
                        <image class="info_icon" src="/static/info.png"></image>
                        <view>输入</view>
                        <view class="keyword">型号-电压-规格</view>
                        <view>检索基础库</view>
                    </view>
                    <view class="search-action">
                        <view class="voice-btn" @click="handleShowVoice()">语音录入</view>
                        <view class="search-btn" @click="handleSearchQuotationInquiry()">检索</view>
                    </view>
                </view>
            </view>
            <view class="info">
                <view class="item">
                    <view class="label required">型号：</view>
                    <view class="value">
                        <uv-input v-model="state.quotationInquiryForm.modelName" placeholder="请输入" inputAlign="right" border="none" />
                    </view>
                </view>
                <view class="item">
                    <view class="label">电压等级：</view>
                    <view class="value">
                        <uv-input v-model="state.quotationInquiryForm.voltageLevel" placeholder="请输入" inputAlign="right" border="none" />
                    </view>
                </view>
                <view class="item">
                    <view class="label">规格：</view>
                    <view class="value">
                        <uv-input v-model="state.quotationInquiryForm.specification" placeholder="请输入" inputAlign="right" border="none" />
                    </view>
                </view>
                <view class="item">
                    <view class="label">单位：</view>
                    <view class="value">
                        <uv-input v-model="state.quotationInquiryForm.unit" placeholder="请输入" inputAlign="right" border="none" />
                    </view>
                </view>
                <view class="item">
                    <view class="label required">数量：</view>
                    <view class="value">
                        <uv-input type="digit" v-model="state.quotationInquiryForm.quantity" placeholder="请输入" inputAlign="right" border="none" />
                    </view>
                </view>
                <view class="item">
                    <view class="label">折扣前单价：</view>
                    <view class="value">
                        <uv-input type="digit" v-model="state.quotationInquiryForm.discountBeforeUnitPrice" placeholder="请输入" inputAlign="right" border="none" />
                    </view>
                </view>
            </view>
            <view class="discount">
                <view class="label">折扣(%):</view>
                <view class="input-discount">
                    <uv-input placeholder="折扣1" v-model="state.quotationInquiryForm.discount1" border="surround" :formatter="onFormatter"></uv-input>
                    <uv-input placeholder="折扣2" v-model="state.quotationInquiryForm.discount2" border="surround" :formatter="onFormatter"></uv-input>
                    <uv-input placeholder="折扣3" v-model="state.quotationInquiryForm.discount3" border="surround" :formatter="onFormatter"></uv-input>
                    <uv-input placeholder="折扣4" v-model="state.quotationInquiryForm.discount4" border="surround" :formatter="onFormatter"></uv-input>
                </view>
            </view>
        </view>
        <view class="action_box">
            <view class="unit-price_box">
                <view class="total">
                    <view class="item">
                        <view class="label">含税单价</view>
                        <view class="unit-price_item">
                            <view class="price-icon" v-if="state.quotationInquiryForm.taxUnitPrice">￥</view>
                            <view class="price">{{ state.quotationInquiryForm.taxUnitPrice || '--' }}</view>
                        </view>
                    </view>
                    <view class="item">
                        <view class="label">含税合计</view>
                        <view class="unit-price_item">
                            <view class="price-icon" v-if="state.quotationInquiryForm.taxTotalAmount">￥</view>
                            <view class="price">{{ state.quotationInquiryForm.taxTotalAmount || '--' }}</view>
                        </view>
                    </view>
                </view>
                <view class="action">
                    <template v-if="props.model === 'edit'">
                        <view class="left-btn">
                            <view class="cancel btn" @click="onCancel()">取消</view>
                            <view class="delete btn" @click="onDelete()">删除</view>
                        </view>
                        <view class="submit btn" @click="handleSaveEdit()">提交</view>
                    </template>
                    <template v-else>
                        <view class="cancel btn" @click="onCancel()">取消</view>
                        <view class="right-btn">
                            <view class="submit btn" @click="onSingleAdd()">添加</view>
                            <view class="submit btn" @click="onContinueAddInquiry()">继续添加</view>
                        </view>
                    </template>
                </view>
            </view>
            <loading-page :loading="loading"></loading-page>
        </view>

        <uv-popup ref="jumpingSpectrumPopup" bgColor="none" :round="8" closeable :closeOnClickOverlay="false" :safeAreaInsetBottom="false" mode="bottom">
            <JumpingSpectrum :height="40" @onCancel="handleOnCancelVoice()" @onConfirmVoice="handleOnConfirmVoice" />
        </uv-popup>
    </view>
</template>

<script setup lang="ts">
import { nextTick, reactive, ref, watch } from 'vue';
import { IQuotationInquiryDetail } from '@/models';
import { showModal, showToast } from '@/utils';
import { addQuotationInquiryItem, deleteTaskItemById, quotationInquiryByParseProduct, quotationInquiryEditItem } from '@/api/quotation';
import { useLoadingFn } from '../../hooks';
import JumpingSpectrum from '../jumping-spectrum/jumping-spectrum.vue';
import { checkAuthorizeScope } from '../../utils/authorize-scope';

const emits = defineEmits<{
    (e: 'onCancel'): void;
    (e: 'onDelete', value: string): void;
    (e: 'onSaveSuccess', value: string): void;
    (e: 'onSaveAddSuccess'): void;
}>();

let isContinueAdd: boolean = false;
const props = withDefaults(
    defineProps<{
        quotationInquiryDetail: IQuotationInquiryDetail;
        model: 'add' | 'edit';
        versionId?: string;
    }>(),
    {
        quotationInquiryDetail: () => ({}),
        model: 'edit'
    }
);

const loading = ref(false);
const jumpingSpectrumPopup = ref();
const jumpingSpectrum = ref();

const handleSaveEdit = useLoadingFn(onSubmitEdit, loading);
const handleOnAddInquiry = useLoadingFn(onAddInquiry, loading);
const handleSearchQuotationInquiry = useLoadingFn(onSearchQuotationInquiry, loading);

const state = reactive<{
    pastedContent: string;
    quotationInquiryForm: IQuotationInquiryDetail;
}>({
    pastedContent: '',
    quotationInquiryForm: {}
});

watch(
    () => props.quotationInquiryDetail,
    (quotationInquiryDetail) => {
        if (!quotationInquiryDetail || Object.keys(quotationInquiryDetail).length === 0) {
            state.quotationInquiryForm = {};
            return;
        }
        Object.assign(state.quotationInquiryForm, quotationInquiryDetail);
    }
);

const onCancel = () => {
    state.pastedContent = '';
    if (isContinueAdd) {
        emits('onSaveAddSuccess');
    } else {
        emits('onCancel');
    }
};

const onDelete = async () => {
    const confirmRes = await showModal({ title: '确认删除', content: '删除成功后，数据将不能恢复' });
    if (!confirmRes.confirm) {
        return;
    }
    state.pastedContent = '';
    await deleteTaskItemById(props.quotationInquiryDetail.id);
    emits('onDelete', props.quotationInquiryDetail.id);
};

async function onSubmitEdit() {
    if (!state.quotationInquiryForm.modelName) {
        showToast({ title: '型号不能为空', icon: 'none' });
        return;
    }
    state.pastedContent = '';
    await quotationInquiryEditItem(state.quotationInquiryForm);
    emits('onSaveSuccess', props.quotationInquiryDetail.id);
}

async function onContinueAddInquiry() {
    if (!state.quotationInquiryForm.modelName) {
        showToast({ title: '型号不能为空', icon: 'none' });
        return;
    }

    if (isNaN(parseFloat(`${state.quotationInquiryForm.quantity}`))) {
        showToast({ title: '数量不能为空', icon: 'none' });
        return;
    }

    isContinueAdd = true;
    await handleOnAddInquiry();
    state.quotationInquiryForm = { taskId: state.quotationInquiryForm.taskId };
    state.pastedContent = '';
}

async function onSingleAdd() {
    if (!state.quotationInquiryForm.modelName) {
        showToast({ title: '型号不能为空', icon: 'none' });
        return;
    }
    await handleOnAddInquiry();
    state.pastedContent = '';
    emits('onSaveAddSuccess');
}

async function onAddInquiry() {
    await addQuotationInquiryItem(state.quotationInquiryForm);
    showToast({ title: '新增成功', icon: 'success' });
}

async function onSearchQuotationInquiry() {
    if (!state.pastedContent?.trim()) {
        showToast({ title: '型号-电压-规格 不能为空', icon: 'none' });
        return;
    }
    const { data } = await quotationInquiryByParseProduct({
        pastedContent: state.pastedContent,
        versionId: props.versionId
    });
    Object.assign(state.quotationInquiryForm, data);
}

async function handleShowVoice() {
    // 检查语音权限
    const recordPermission = await checkAuthorizeScope('record', '麦克风');
    if (!recordPermission) {
        return;
    }
    jumpingSpectrumPopup.value?.open();
    nextTick(() => {
        jumpingSpectrum.value?.startJumping();
    });
}

function handleOnCancelVoice() {
    jumpingSpectrum.value?.stopJumping();
    jumpingSpectrumPopup.value?.close();
}

function handleOnConfirmVoice(value: string) {
    jumpingSpectrum.value?.stopJumping();
    jumpingSpectrumPopup.value?.close();
    state.pastedContent = value;
    handleSearchQuotationInquiry();
}

function onFormatter(value: string) {
    return checkDiscountForNumber(value);
}

function checkDiscountForNumber(value: string) {
    let newValue = '';
    let hasDot = false;
    let hasMinus = false;

    if (typeof value === 'object') {
        return null;
    }

    // 逐个字符检查
    for (let i = 0; i < value.length; i++) {
        const char = value[i];

        // 允许数字
        if (char >= '0' && char <= '9') {
            newValue += char;
        }
        // 允许小数点且只能有一个
        else if (char === '.' && !hasDot) {
            newValue += char;
            hasDot = true;
        }
        // 允许负号且只能在开头
        else if (char === '-' && i === 0 && !hasMinus) {
            newValue += char;
            hasMinus = true;
        }
    }

    if (newValue === '.') return '0.'; // 自动补全为0.
    if (newValue === '-.') return '-0.'; // 自动补全为-0.

    // 避免以0开头的多位整数（可选，不影响小数部分）
    if (!hasDot) {
        if (newValue.length > 1 && newValue.startsWith('0') && !newValue.startsWith('-')) {
            newValue = newValue.substring(1);
        }
        if (newValue.length > 2 && newValue.startsWith('-0')) {
            newValue = '-' + newValue.substring(2);
        }
    }

    // 确保小数点后至少有一位数字（可选）
    if (hasDot && newValue.endsWith('.')) {
        // 可以选择自动补全为.0或者保留小数点等待输入
        // newValue += '0'; // 自动补全
    }

    return newValue === '' ? null : newValue;
}
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>
