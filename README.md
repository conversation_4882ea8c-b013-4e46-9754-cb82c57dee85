# 动态页面渲染系统

基于JSON配置的动态页面渲染系统，专为uni-app框架设计。

## 🚀 功能特性

- **JSON驱动**: 通过JSON配置动态生成页面内容
- **Vue 3 Composition API**: 使用最新的 setup 语法
- **组件化设计**: 支持多种商城组件类型
- **灵活尺寸**: 图片组件支持百分比宽度（如 "100%"）
- **页面级配置**: 支持背景、导航栏、安全区域等配置
- **uni-app兼容**: 完全适配uni-app框架

## 📱 支持的组件类型

- `search` - 搜索组件（支持搜索框、按钮、图标）
- `carousel` - 轮播图组件（支持自动播放、指示器、箭头）
- `notice` - 公告组件
- `image` - 图片组件
- `banner` - 轮播图组件
- `grid-menu` - 九宫格菜单（支持图片、点击类型）
- `coupon` - 优惠券组件（支持水平/垂直布局）
- `seckill` - 秒杀组件（支持倒计时、进度条）
- `product-tab` - 商品标签页组件（支持分类切换、水平/垂直布局）
- `groupbuy` - 团购组件（支持团购进度、参团功能）
- `chainbuy` - 接龙购买组件（支持接龙列表、参与者展示）
- `service-product` - 服务商品组件（支持预约、评价展示）
- `poster` - 海报组件（支持图片/视频、覆盖层内容）
- `article` - 文章组件（支持列表/卡片布局、标签分类）
- `product` - 商品组件（支持网格/列表布局）
- `product-list` - 商品列表
- `divider` - 分割线组件（支持文字、样式）
- `flash-sale` - 秒杀组件
- `group-buy` - 团购组件

## 🎯 JSON配置格式

```json
{
  "pageConfig": {
    "background": {
      "type": "solid",
      "color": "#f5f5f5"
    },
    "padding": {
      "top": 0,
      "right": 0,
      "bottom": 0,
      "left": 0
    },
    "previewMode": "mobile"
  },
  "components": [
    {
      "id": "component-id",
      "type": "notice",
      "config": {
        "content": "公告内容",
        "backgroundColor": "#fef3c7",
        "textColor": "#92400e"
      },
      "style": {
        "padding": { "top": 0, "right": 0, "bottom": 0, "left": 0 },
        "margin": { "top": 0, "right": 0, "bottom": 0, "left": 0 },
        "borderRadius": { "topLeft": 8, "topRight": 8, "bottomLeft": 8, "bottomRight": 8 }
      },
      "sort": 0
    },
    {
      "id": "image-example",
      "type": "image",
      "config": {
        "url": "图片地址",
        "link": "",
        "alt": "图片描述",
        "width": "100%",  // 支持数字或字符串（如 "100%", "50%"）
        "height": 200,
        "borderRadius": 8
      },
      "style": {
        "padding": { "top": 0, "right": 0, "bottom": 0, "left": 0 },
        "margin": { "top": 0, "right": 0, "bottom": 0, "left": 0 },
        "borderRadius": { "topLeft": 8, "topRight": 8, "bottomLeft": 8, "bottomRight": 8 }
      },
      "sort": 1
    },
    {
      "id": "grid-menu-example",
      "type": "grid-menu",
      "config": {
        "items": [
          {
            "id": "menu1",
            "name": "商城",
            "image": "图片地址",
            "clickType": "navigate",
            "url": "/shop",
            "sort": 1
          }
        ],
        "columns": 4,
        "backgroundColor": "#ffffff",
        "itemBackgroundColor": "#f8fafc",
        "textColor": "#374151",
        "fontSize": 14,
        "itemPadding": 16,
        "itemMargin": 8,
        "itemBorderRadius": 8,
        "showBorder": false,
        "borderColor": "#e5e7eb"
      },
      "sort": 2
    },
    {
      "id": "product-example",
      "type": "product",
      "config": {
        "layout": "grid",
        "columns": 2,
        "showPrice": true,
        "showTitle": true,
        "showRating": true,
        "products": [
          {
            "id": "product1",
            "title": "商品名称",
            "price": 99.99,
            "originalPrice": 129.99,
            "image": "商品图片地址",
            "rating": 4.5,
            "link": "/product/detail"
          }
        ]
      },
      "sort": 3
    }
  ],
  "metadata": {
    "version": "1.0.0",
    "timestamp": 1751862486409,
    "exportedAt": "2025-07-07T04:28:06.409Z"
  }
}
```

## 🛠️ 使用方法

### 1. 直接访问动态页面
```
/pages/dynamic-page/index
```

### 2. 通过URL参数传递配置
```
/pages/dynamic-page/index?config=<编码后的JSON字符串>
```

### 3. 在代码中使用
```javascript
// 在页面的onLoad方法中
onLoad(options) {
  if (options.config) {
    try {
      const config = JSON.parse(decodeURIComponent(options.config));
      this.pageConfig = config;
    } catch (error) {
      console.error('解析配置失败:', error);
    }
  }
}
```

## 📋 页面配置说明

### pageConfig (页面级配置)
- `background`: 页面背景设置
- `padding`: 页面内边距
- `previewMode`: 预览模式 ("mobile" | "desktop")
- `navigation`: 导航栏配置 (可选)
- `safeArea`: 安全区域配置 (可选)
- `scroll`: 滚动配置 (可选)

### components (组件配置)
- `id`: 组件唯一标识
- `type`: 组件类型
- `config`: 组件特定配置
- `style`: 组件样式配置
- `sort`: 排序顺序

### metadata (元数据)
- `version`: 配置版本
- `timestamp`: 时间戳
- `exportedAt`: 导出时间

## 🎨 图片组件宽度支持

图片组件的 `width` 属性支持多种格式：

```json
{
  "width": 100,        // 数字：固定像素宽度
  "width": "100%",     // 字符串：百分比宽度，占满容器
  "width": "50%",      // 字符串：50% 宽度
  "width": "200px"     // 字符串：带单位的固定宽度
}
```

## 🎨 组件样式配置

每个组件都支持统一的样式配置：

```json
{
  "style": {
    "padding": {
      "top": 0,
      "right": 16,
      "bottom": 0,
      "left": 16
    },
    "margin": {
      "top": 8,
      "right": 0,
      "bottom": 8,
      "left": 0
    },
    "borderRadius": {
      "topLeft": 8,
      "topRight": 8,
      "bottomLeft": 8,
      "bottomRight": 8
    }
  }
}
```

## 📂 项目结构

```
├── pages/
│   ├── index/index.vue              # 首页
│   └── dynamic-page/index.vue       # 动态页面渲染
├── components/
│   └── dynamic-components/          # 动态组件
│       ├── notice-component.vue     # 公告组件
│       ├── image-component.vue      # 图片组件
│       ├── banner-component.vue     # 轮播组件
│       ├── grid-menu-component.vue  # 九宫格菜单
│       ├── product-list-component.vue # 商品列表
│       ├── flash-sale-component.vue # 秒杀组件
│       └── group-buy-component.vue  # 团购组件
├── types/
│   └── dynamic-page.ts             # 类型定义
├── utils/
│   └── style-helper.ts             # 样式工具
└── config/
    └── theme.ts                    # 主题配置
```

## 🔧 开发说明

1. 系统使用 `v-if/v-else-if` 方式渲染组件，兼容uni-app
2. 所有样式配置都会转换为内联样式
3. 组件按 `sort` 字段排序渲染
4. 支持页面级样式配置和组件级样式配置

## 📝 注意事项

- 确保JSON配置格式正确
- 组件ID必须唯一
- sort字段用于控制组件渲染顺序
- 样式配置支持数值类型，会自动添加px单位
