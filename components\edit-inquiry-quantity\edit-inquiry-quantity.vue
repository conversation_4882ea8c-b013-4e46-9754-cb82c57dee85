<template>
    <view class="content">
        <view class="title">修改数量</view>
        <view class="edit">
            <uv-input
                type="digit"
                v-model="state.quotationInquiryForm.quantity"
                placeholder="请输入"
                :customStyle="{
                    height: '25px'
                }"
            />
            <view
                class="unit"
                v-if="state.quotationInquiryForm.unit"
             
            >
                {{ state.quotationInquiryForm.unit }}
            </view>
        </view>
        <view class="action">
            <view class="cancel" @click="onCancel()">取消</view>
            <view class="confirm" @click="handleSaveEdit()">确定</view>
        </view>
        <loading-page :loading="loading"></loading-page>
    </view>
</template>

<script setup lang="ts">
import { reactive, ref, watch } from 'vue';
import { IQuotationInquiryDetail } from '@/models';
import { quotationInquiryEditItem } from '@/api/quotation';
import { useLoadingFn } from '@/hooks';

const emits = defineEmits<{
    (e: 'onCancel'): void;
    (e: 'onDelete', value: string): void;
    (e: 'onSaveSuccess', value: string): void;
}>();

const loading = ref(false);

const handleSaveEdit = useLoadingFn(onSubmitEdit, loading);
const state = reactive<{
    quotationInquiryForm: IQuotationInquiryDetail;
}>({
    quotationInquiryForm: {}
});

const props = withDefaults(
    defineProps<{
        quotationInquiryDetail: IQuotationInquiryDetail;
    }>(),
    {
        quotationInquiryDetail: () => ({})
    }
);

watch(
    () => props.quotationInquiryDetail,
    (quotationInquiryDetail) => {
        if (!quotationInquiryDetail || Object.keys(quotationInquiryDetail).length === 0) {
            state.quotationInquiryForm = {};
            return;
        }
        Object.assign(state.quotationInquiryForm, quotationInquiryDetail);
    }
);

const onCancel = () => {
    emits('onCancel');
};

async function onSubmitEdit() {
    await quotationInquiryEditItem(state.quotationInquiryForm);
    emits('onSaveSuccess', props.quotationInquiryDetail.id);
}
</script>

<style scoped lang="scss">
.content {
    display: flex;
    flex-direction: column;
    border-radius: 8px;
    background: #ffffff;
    padding: 20px;
    .title {
        font-size: 18px;
        font-weight: 600;
        text-align: center;
        color: #303133;
        margin-bottom: 20px;
    }
    .edit {
        display: flex;
        align-items: center;
        gap: 8px;
        height: 39px;
        .unit {
            border-radius: 4px;
            border: 1px solid #ebeef5;
            padding: 0 20px;
            background: #f7f8fa;
             line-height: 37px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
    }
    .action {
        display: flex;
        margin-top: 20px;
        gap: 8px;
        .confirm,
        .cancel {
            font-size: 14px;
            border-radius: 4px;
            flex: 1;
            padding: 5px 0;
            text-align: center;
        }
        .cancel {
            color: #303133;
            border: 1px solid #dcdfe6;
        }
        .confirm {
            color: #ffffff;
            background: #00b678;
        }
    }
}
</style>
