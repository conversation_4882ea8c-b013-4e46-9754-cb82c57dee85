import { MatchStatusEnum, ParseStatusEnum } from "@/enums";

/*** 询价单 */
export interface IQuotationInquiryDetail {
  /**
   * 原始数据
   */
  columnRawContent?: string;
  /**
   * 折扣1
   */
  discount1?: number;
  /**
   * 折扣2
   */
  discount2?: number;
  /**
   * 折扣3
   */
  discount3?: number;
  /**
   * 折扣4
   */
  discount4?: number;
  /**
   * excel-原始行号
   */
  excelRowIndex?: number;
  /**
   * excel-sheet号
   */
  excelSheet?: number;
  /**
   * ID
   */
  id?: string;

  /**
   * 匹配状态枚举
   */
  matchStatus?: MatchStatusEnum;
  /**
   * 材料成本单价
   */
  materialCostPrice?: number;
  /**
   * 材料成本合计
   */
  materialCostTotal?: number;
  /**
   * 型号
   */
  modelName?: string;
  /**
   * 数量
   */
  quantity?: number;
  /**
   * 排序
   */
  sort?: number;
  /**
   * 规格
   */
  specification?: string;
  /**
   * 询报价任务id
   */
  taskId?: string;
  /**
   * 含税合计
   */
  taxTotalAmount?: number;
  /**
   * 含税单价
   */
  taxUnitPrice?: number;

  /**
   * 含税单价 编辑之前
   */
  discountBeforeUnitPrice?: number;
  /**
   * 单位
   */
  unit?: string;
  /**
   * 电压等级
   */
  voltageLevel?: string;

  inputMethod?: number;

  /** 解析状态 */

  parseStatus?: ParseStatusEnum;

  /**
   * 匹配价格 isMatchPrice   true 正常显示下面这个字段, false 带颜色 priceMatchRecord
   */
  isMatchPrice?: boolean;

  /** 价格匹配记录 */
  priceMatchRecord?: string;
  
  /**  */
  discountBeforeTotalAmount?:number;
}
