import { getWeChatUserOpenId } from '@/api/we-chat';
import { login, reLaunch } from './uni-promise';
import { loginWeChat } from '@/api/account';
import { AuthAccessToken } from '../consts';
import { setStorageValue } from './storage';

export const loginByWeChat = async () => {
    const { errMsg, code } = await login();
    if (errMsg !== 'login:ok') {
        reLaunch({ url: '/pages/login/index' });
        return;
    }
    const { data } = await getWeChatUserOpenId(code);
    if (!data?.openId || !data?.unionId || !data.allowLogin) {
        reLaunch({ url: '/pages/login/index' });
        return;
    }
    const { data: auth } = await loginWeChat({ unionId: data.unionId, openId: data.openId });

    if (!auth || Object.keys(auth).length === 0) {
        reLaunch({ url: '/pages/login/index' });
        return;
    }
    setStorageValue(AuthAccessToken, auth);
};
