.content {
    display: flex;
    flex-direction: column;
    .header {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 30px 40px;
        background: #ffffff;
        .avatar_box {
            .avatar {
                position: relative;
                border-radius: 100%;
                width: 64px;
                height: 64px;
                background: #ffffff;
                display: flex;
                justify-content: center;
                align-items: center;
                overflow: visible;
                background-color: none;
                padding: 0;
                line-height: inherit;
                .img {
                    width: 64px;
                    height: 64px;
                    border-radius: 100%;
                }
                .default_avatar {
                    width: 64px;
                    height: 64px;
                }
                .camera {
                    width: 20px;
                    height: 20px;
                    position: absolute;
                    right: 0;
                    bottom: 0;
                }
                &::after {
                    content: none;
                }
            }
        }
        .nick-name {
            font-size: 22px;
            font-weight: 500;
            color: #303133;
        }
    }
    .body {
        background: #ffffff;
        margin-top: 10px;
        padding: 0 16px;
        display: flex;
        flex-direction: column;
        .item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid #dcdfe6;
            padding: 15px 0;
            .label {
                display: flex;
                align-items: center;
                gap: 16px;
                .icon {
                    width: 18px;
                    height: 18px;
                }
                .name {
                    font-size: 16px;
                    color: #303133;
                }
            }
            .value {
                flex: 1;
                display: flex;
                justify-content: flex-end;
            }
            &.remark {
                align-items: flex-start;
            }
        }
    }
}
