<template>
    <view class="test-page">
        <view class="test-header">
            <text class="test-title">动态页面重构测试</text>
            <text class="test-desc">基于最新JSON配置的组件渲染测试</text>
        </view>
        
        <view class="test-actions">
            <button @click="loadDefaultConfig" class="test-btn">加载默认配置</button>
            <button @click="testNavigation" class="test-btn">测试页面跳转</button>
        </view>
        
        <view class="config-info">
            <text class="config-title">当前配置信息：</text>
            <view class="config-details">
                <text class="config-item">组件数量: {{ componentCount }}</text>
                <text class="config-item">页面背景: {{ backgroundType }}</text>
                <text class="config-item">预览模式: {{ previewMode }}</text>
            </view>
        </view>
        
        <view class="component-list">
            <text class="list-title">组件列表：</text>
            <view 
                v-for="(component, index) in sortedComponents" 
                :key="component.id" 
                class="component-item"
            >
                <text class="component-type">{{ index + 1 }}. {{ component.type }}</text>
                <text class="component-id">ID: {{ component.id }}</text>
            </view>
        </view>
    </view>
</template>

<script setup>
import { ref, computed } from 'vue';
import { defaultConfig } from './config';

const pageConfig = ref(defaultConfig);

const componentCount = computed(() => {
    return pageConfig.value.components.length;
});

const backgroundType = computed(() => {
    return pageConfig.value.pageConfig.background.type;
});

const previewMode = computed(() => {
    return pageConfig.value.pageConfig.previewMode;
});

const sortedComponents = computed(() => {
    return [...pageConfig.value.components].sort((a, b) => a.sort - b.sort);
});

const loadDefaultConfig = () => {
    pageConfig.value = defaultConfig;
    uni.showToast({
        title: '配置已重新加载',
        icon: 'success'
    });
};

const testNavigation = () => {
    const configStr = encodeURIComponent(JSON.stringify(pageConfig.value));
    uni.navigateTo({
        url: `/pages/dynamic-page/index?config=${configStr}`,
        success: () => {
            console.log('跳转成功');
        },
        fail: (err) => {
            console.error('跳转失败:', err);
            uni.showToast({
                title: '跳转失败',
                icon: 'error'
            });
        }
    });
};
</script>

<style lang="scss" scoped>
.test-page {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100vh;
}

.test-header {
    text-align: center;
    margin-bottom: 30px;
    
    .test-title {
        font-size: 20px;
        font-weight: bold;
        color: #333;
        display: block;
        margin-bottom: 8px;
    }
    
    .test-desc {
        font-size: 14px;
        color: #666;
        display: block;
    }
}

.test-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 30px;
    
    .test-btn {
        padding: 10px 20px;
        background-color: #007aff;
        color: white;
        border: none;
        border-radius: 6px;
        font-size: 14px;
    }
}

.config-info {
    background-color: white;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    
    .config-title {
        font-size: 16px;
        font-weight: bold;
        color: #333;
        display: block;
        margin-bottom: 10px;
    }
    
    .config-details {
        .config-item {
            font-size: 14px;
            color: #666;
            display: block;
            margin-bottom: 5px;
        }
    }
}

.component-list {
    background-color: white;
    padding: 15px;
    border-radius: 8px;
    
    .list-title {
        font-size: 16px;
        font-weight: bold;
        color: #333;
        display: block;
        margin-bottom: 15px;
    }
    
    .component-item {
        padding: 10px;
        border-bottom: 1px solid #f0f0f0;
        
        &:last-child {
            border-bottom: none;
        }
        
        .component-type {
            font-size: 14px;
            color: #333;
            font-weight: 500;
            display: block;
            margin-bottom: 4px;
        }
        
        .component-id {
            font-size: 12px;
            color: #999;
            display: block;
        }
    }
}
</style>
