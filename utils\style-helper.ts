/**
 * 样式辅助工具函数
 */

import { BaseStyle, PageBackground, PagePadding, PageLevelConfig, PageSafeArea, PageScroll } from '../types/dynamic-page';
import { theme } from '../config/theme';

/**
 * 获取安全的样式值
 */
export const getSafeStyle = (style?: BaseStyle): BaseStyle => {
  const defaultStyle: BaseStyle = {
    padding: { top: 0, right: 0, bottom: 0, left: 0 },
    margin: { top: 0, right: 0, bottom: 0, left: 0 },
    borderRadius: { topLeft: 0, topRight: 0, bottomLeft: 0, bottomRight: 0 }
  };

  if (!style) return defaultStyle;

  return {
    padding: {
      top: style.padding?.top ?? defaultStyle.padding.top,
      right: style.padding?.right ?? defaultStyle.padding.right,
      bottom: style.padding?.bottom ?? defaultStyle.padding.bottom,
      left: style.padding?.left ?? defaultStyle.padding.left,
    },
    margin: {
      top: style.margin?.top ?? defaultStyle.margin.top,
      right: style.margin?.right ?? defaultStyle.margin.right,
      bottom: style.margin?.bottom ?? defaultStyle.margin.bottom,
      left: style.margin?.left ?? defaultStyle.margin.left,
    },
    borderRadius: {
      topLeft: style.borderRadius?.topLeft ?? defaultStyle.borderRadius.topLeft,
      topRight: style.borderRadius?.topRight ?? defaultStyle.borderRadius.topRight,
      bottomLeft: style.borderRadius?.bottomLeft ?? defaultStyle.borderRadius.bottomLeft,
      bottomRight: style.borderRadius?.bottomRight ?? defaultStyle.borderRadius.bottomRight,
    }
  };
};

/**
 * 将配置样式转换为CSS样式对象
 */
export const convertStyleToCSS = (style?: BaseStyle): Record<string, string> => {
  const safeStyle = getSafeStyle(style);
  const { padding, margin, borderRadius } = safeStyle;

  return {
    paddingTop: `${padding.top}px`,
    paddingRight: `${padding.right}px`,
    paddingBottom: `${padding.bottom}px`,
    paddingLeft: `${padding.left}px`,
    marginTop: `${margin.top}px`,
    marginRight: `${margin.right}px`,
    marginBottom: `${margin.bottom}px`,
    marginLeft: `${margin.left}px`,
    borderTopLeftRadius: `${borderRadius.topLeft}px`,
    borderTopRightRadius: `${borderRadius.topRight}px`,
    borderBottomLeftRadius: `${borderRadius.bottomLeft}px`,
    borderBottomRightRadius: `${borderRadius.bottomRight}px`,
  };
};

/**
 * 生成内联样式字符串
 */
export const generateInlineStyle = (styleObj: Record<string, string | number>): string => {
  return Object.entries(styleObj)
    .map(([key, value]) => {
      const cssKey = key.replace(/([A-Z])/g, '-$1').toLowerCase();
      return `${cssKey}: ${value}`;
    })
    .join('; ');
};

/**
 * 合并样式对象
 */
export const mergeStyles = (...styles: Array<Record<string, any>>): Record<string, any> => {
  return Object.assign({}, ...styles);
};

/**
 * 获取响应式字体大小
 */
export const getResponsiveFontSize = (size: number): string => {
  // 基于设计稿375px宽度进行适配
  return `${size}px`;
};

/**
 * 获取响应式间距
 */
export const getResponsiveSpacing = (spacing: number): string => {
  return `${spacing}px`;
};

/**
 * 颜色工具函数
 */
export const colorUtils = {
  /**
   * 判断是否为有效的颜色值
   */
  isValidColor: (color: string): boolean => {
    const colorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$|^rgb\(|^rgba\(|^hsl\(|^hsla\(/;
    return colorRegex.test(color);
  },

  /**
   * 获取颜色的透明度版本
   */
  getColorWithOpacity: (color: string, opacity: number): string => {
    if (color.startsWith('#')) {
      const hex = color.slice(1);
      const r = parseInt(hex.slice(0, 2), 16);
      const g = parseInt(hex.slice(2, 4), 16);
      const b = parseInt(hex.slice(4, 6), 16);
      return `rgba(${r}, ${g}, ${b}, ${opacity})`;
    }
    return color;
  },

  /**
   * 获取主题颜色或默认颜色
   */
  getThemeColorOrDefault: (color: string, defaultColor: string = theme.colors.primary): string => {
    return colorUtils.isValidColor(color) ? color : defaultColor;
  },
};

/**
 * 尺寸工具函数
 */
export const sizeUtils = {
  /**
   * 转换尺寸单位
   */
  convertSize: (size: number | string, unit: 'px' | 'rpx' | '%' = 'px'): string => {
    if (typeof size === 'string') return size;
    return `${size}${unit}`;
  },

  /**
   * 获取安全的尺寸值
   */
  getSafeSize: (size: number, min: number = 0, max: number = 9999): number => {
    return Math.max(min, Math.min(max, size));
  },
};

/**
 * 动画工具函数
 */
export const animationUtils = {
  /**
   * 生成CSS动画样式
   */
  createAnimation: (
    name: string,
    duration: number = 300,
    timing: string = 'ease',
    delay: number = 0
  ): string => {
    return `${name} ${duration}ms ${timing} ${delay}ms`;
  },

  /**
   * 常用动画预设
   */
  presets: {
    fadeIn: 'fadeIn 300ms ease',
    fadeOut: 'fadeOut 300ms ease',
    slideInUp: 'slideInUp 300ms ease',
    slideInDown: 'slideInDown 300ms ease',
    zoomIn: 'zoomIn 300ms ease',
    zoomOut: 'zoomOut 300ms ease',
  },
};

/**
 * 布局工具函数
 */
export const layoutUtils = {
  /**
   * 生成Flex布局样式
   */
  createFlexStyle: (
    direction: 'row' | 'column' = 'row',
    justify: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' = 'flex-start',
    align: 'flex-start' | 'center' | 'flex-end' | 'stretch' = 'center'
  ): Record<string, string> => {
    return {
      display: 'flex',
      flexDirection: direction,
      justifyContent: justify,
      alignItems: align,
    };
  },

  /**
   * 生成Grid布局样式
   */
  createGridStyle: (
    columns: number,
    gap: number = theme.spacing.md
  ): Record<string, string> => {
    return {
      display: 'grid',
      gridTemplateColumns: `repeat(${columns}, 1fr)`,
      gap: `${gap}px`,
    };
  },
};

/**
 * 页面级样式处理
 */
export const pageStyleUtils = {
  /**
   * 生成页面背景样式
   */
  generateBackgroundStyle: (background?: PageBackground): Record<string, string> => {
    if (!background) {
      return { backgroundColor: theme.colors.background.secondary };
    }

    switch (background.type) {
      case 'solid':
        return {
          backgroundColor: background.color || theme.colors.background.secondary
        };

      case 'gradient':
        if (background.gradient) {
          const { type, colors, direction } = background.gradient;
          const colorStr = colors.join(', ');
          if (type === 'linear') {
            return {
              background: `linear-gradient(${direction || '180deg'}, ${colorStr})`
            };
          } else {
            return {
              background: `radial-gradient(circle, ${colorStr})`
            };
          }
        }
        return { backgroundColor: theme.colors.background.secondary };

      case 'image':
        if (background.image) {
          const { url, repeat, position, size } = background.image;
          return {
            backgroundImage: `url(${url})`,
            backgroundRepeat: repeat || 'no-repeat',
            backgroundPosition: position || 'center',
            backgroundSize: size || 'cover'
          };
        }
        return { backgroundColor: theme.colors.background.secondary };

      default:
        return { backgroundColor: theme.colors.background.secondary };
    }
  },

  /**
   * 生成页面内边距样式
   */
  generatePaddingStyle: (padding?: PagePadding): Record<string, string> => {
    const defaultPadding = { top: 0, right: 0, bottom: 0, left: 0 };
    const safePadding = { ...defaultPadding, ...(padding || {}) };

    return {
      paddingTop: `${safePadding.top}px`,
      paddingRight: `${safePadding.right}px`,
      paddingBottom: `${safePadding.bottom}px`,
      paddingLeft: `${safePadding.left}px`,
    };
  },

  /**
   * 生成完整的页面容器样式
   */
  generatePageContainerStyle: (pageConfig?: PageLevelConfig): Record<string, string> => {
    const backgroundStyle = pageStyleUtils.generateBackgroundStyle(pageConfig?.background);
    const paddingStyle = pageStyleUtils.generatePaddingStyle(pageConfig?.padding);

    return {
      ...backgroundStyle,
      ...paddingStyle,
      minHeight: '100vh',
      width: '100%',
      boxSizing: 'border-box',
    };
  },

  /**
   * 检查是否为移动端预览模式
   */
  isMobileMode: (pageConfig?: PageLevelConfig): boolean => {
    return pageConfig?.previewMode === 'mobile';
  },

  /**
   * 生成安全区域样式
   */
  generateSafeAreaStyle: (safeArea?: PageSafeArea): Record<string, string> => {
    if (!safeArea) return {};

    const style: Record<string, string> = {};

    if (safeArea.top) {
      style.paddingTop = 'env(safe-area-inset-top)';
    }

    if (safeArea.bottom) {
      style.paddingBottom = 'env(safe-area-inset-bottom)';
    }

    return style;
  },

  /**
   * 生成滚动配置样式
   */
  generateScrollStyle: (scroll?: PageScroll): Record<string, string> => {
    if (!scroll) return {};

    const style: Record<string, string> = {};

    if (!scroll.enabled) {
      style.overflow = 'hidden';
    } else {
      style.overflow = 'auto';

      if (!scroll.showScrollbar) {
        style.scrollbarWidth = 'none';
        style.msOverflowStyle = 'none';
      }
    }

    return style;
  },

  /**
   * 生成自定义CSS样式
   */
  generateCustomStyle: (customCSS?: string): Record<string, string> => {
    if (!customCSS) return {};

    try {
      // 简单的CSS解析（仅支持基本属性）
      const styles: Record<string, string> = {};
      const rules = customCSS.split(';');

      rules.forEach(rule => {
        const [property, value] = rule.split(':').map(s => s.trim());
        if (property && value) {
          // 转换CSS属性名为驼峰命名
          const camelProperty = property.replace(/-([a-z])/g, (g) => g[1].toUpperCase());
          styles[camelProperty] = value;
        }
      });

      return styles;
    } catch (error) {
      console.warn('Custom CSS parsing error:', error);
      return {};
    }
  },

  /**
   * 获取响应式容器样式
   */
  getResponsiveContainerStyle: (pageConfig?: PageLevelConfig): Record<string, string> => {
    const baseStyle = pageStyleUtils.generatePageContainerStyle(pageConfig);
    const safeAreaStyle = pageStyleUtils.generateSafeAreaStyle(pageConfig?.safeArea);
    const scrollStyle = pageStyleUtils.generateScrollStyle(pageConfig?.scroll);
    const customStyle = pageStyleUtils.generateCustomStyle(pageConfig?.customCSS);
    const isMobile = pageStyleUtils.isMobileMode(pageConfig);

    let responsiveStyle = {};
    if (isMobile) {
      responsiveStyle = {
        maxWidth: '100%',
      };
    } else {
      responsiveStyle = {
        maxWidth: '1200px',
        margin: '0 auto',
      };
    }

    return {
      ...baseStyle,
      ...safeAreaStyle,
      ...scrollStyle,
      ...customStyle,
      ...responsiveStyle,
    };
  },

  /**
   * 生成页面动画样式
   */
  generateAnimationStyle: (animation?: PageLevelConfig['animation']): Record<string, string> => {
    if (!animation?.transition) return {};

    const { type, duration, direction } = animation.transition;

    const animationMap = {
      slide: `slide-${direction || 'right'} ${duration}ms ease`,
      fade: `fade ${duration}ms ease`,
      zoom: `zoom ${duration}ms ease`,
      none: 'none'
    };

    return {
      animation: animationMap[type] || 'none',
      transition: `all ${duration}ms ease`
    };
  },

  /**
   * 生成加载状态样式
   */
  generateLoadingStyle: (loading?: PageLevelConfig['animation']['loading']): Record<string, string> => {
    if (!loading?.show) return { display: 'none' };

    return {
      position: 'fixed',
      top: '0',
      left: '0',
      width: '100%',
      height: '100%',
      backgroundColor: loading.backgroundColor || 'rgba(255, 255, 255, 0.8)',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: '9999'
    };
  }
};
