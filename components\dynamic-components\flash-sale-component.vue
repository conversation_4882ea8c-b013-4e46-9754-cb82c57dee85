<template>
  <view class="flash-sale-component" :style="containerStyle">
    <!-- 秒杀头部 -->
    <view class="flash-sale-header" :style="headerStyle">
      <view class="header-left">
        <text class="title">{{ config.title }}</text>
        <text class="subtitle">{{ config.subtitle }}</text>
      </view>
      
      <view class="header-right">
        <view class="countdown-wrapper">
          <text class="countdown-label">距结束</text>
          <view class="countdown-time">
            <text class="time-item">{{ countdown.hours }}</text>
            <text class="time-separator">:</text>
            <text class="time-item">{{ countdown.minutes }}</text>
            <text class="time-separator">:</text>
            <text class="time-item">{{ countdown.seconds }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 秒杀商品列表 -->
    <scroll-view scroll-x class="flash-sale-products">
      <view class="products-container">
        <view 
          class="product-item"
          v-for="product in config.products"
          :key="product.id"
          @click="handleProductClick(product)"
        >
          <!-- 商品图片 -->
          <view class="product-image-wrapper">
            <uv-image
              :src="product.image"
              width="120"
              height="120"
              :borderRadius="theme.borderRadius.md"
              mode="aspectFill"
              :showLoading="true"
              :showError="true"
            />
          </view>
          
          <!-- 商品信息 -->
          <view class="product-info">
            <text class="product-name">{{ product.name }}</text>
            
            <view class="price-wrapper">
              <text class="flash-price">¥{{ formatPrice(product.price) }}</text>
              <text class="original-price">¥{{ formatPrice(product.originalPrice) }}</text>
            </view>
            
            <!-- 进度条 -->
            <view class="progress-wrapper">
              <uv-line-progress 
                :percentage="product.progress"
                :activeColor="theme.colors.error"
                :inactiveColor="theme.colors.border.light"
                :height="6"
                :borderRadius="3"
              />
              <text class="progress-text">已抢{{ product.progress }}%</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue';
import { FlashSaleConfig } from '../../types/dynamic-page';
import { convertStyleToCSS, colorUtils } from '../../utils/style-helper';
import { theme } from '../../config/theme';

interface Props {
  config: FlashSaleConfig['config'];
  style?: FlashSaleConfig['style'];
}

const props = defineProps<Props>();

// 倒计时状态
const countdown = ref({
  hours: '00',
  minutes: '00',
  seconds: '00',
});

let countdownTimer: number | null = null;

// 容器样式
const containerStyle = computed(() => {
  const baseStyle = convertStyleToCSS(props.style);
  return {
    ...baseStyle,
    width: '100%',
  };
});

// 头部样式
const headerStyle = computed(() => {
  return {
    backgroundColor: colorUtils.getThemeColorOrDefault(
      props.config.backgroundColor, 
      theme.colors.error
    ),
    color: colorUtils.getThemeColorOrDefault(
      props.config.textColor, 
      theme.colors.text.inverse
    ),
    padding: `${theme.spacing.md}px`,
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  };
});

// 格式化价格
const formatPrice = (price: number): string => {
  return price.toFixed(2);
};

// 计算倒计时
const calculateCountdown = () => {
  const endTime = new Date(props.config.endTime).getTime();
  const now = new Date().getTime();
  const diff = endTime - now;
  
  if (diff <= 0) {
    countdown.value = { hours: '00', minutes: '00', seconds: '00' };
    if (countdownTimer) {
      clearInterval(countdownTimer);
      countdownTimer = null;
    }
    return;
  }
  
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((diff % (1000 * 60)) / 1000);
  
  countdown.value = {
    hours: hours.toString().padStart(2, '0'),
    minutes: minutes.toString().padStart(2, '0'),
    seconds: seconds.toString().padStart(2, '0'),
  };
};

// 处理商品点击
const handleProductClick = (product: FlashSaleConfig['config']['products'][0]) => {
  if (product.link) {
    uni.navigateTo({
      url: product.link,
      fail: () => {
        uni.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  }
};

// 组件挂载时启动倒计时
onMounted(() => {
  calculateCountdown();
  countdownTimer = setInterval(calculateCountdown, 1000);
});

// 组件卸载时清除倒计时
onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer);
    countdownTimer = null;
  }
});
</script>

<style lang="scss" scoped>
.flash-sale-component {
  width: 100%;
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  margin: 0 16px;
}

.flash-sale-header {
  position: relative;
}

.header-left {
  flex: 1;
}

.title {
  font-size: 18px;
  font-weight: 600;
  margin-right: 8px;
}

.subtitle {
  font-size: 14px;
  opacity: 0.9;
}

.header-right {
  display: flex;
  align-items: center;
}

.countdown-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.countdown-label {
  font-size: 14px;
  opacity: 0.9;
}

.countdown-time {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  padding: 4px 8px;
}

.time-item {
  font-size: 16px;
  font-weight: 600;
  min-width: 20px;
  text-align: center;
}

.time-separator {
  font-size: 14px;
  margin: 0 2px;
}

.flash-sale-products {
  width: 100%;
  white-space: nowrap;
}

.products-container {
  display: flex;
  padding: 16px;
  gap: 12px;
}

.product-item {
  flex-shrink: 0;
  width: 140px;
  background-color: white;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid v-bind('theme.colors.border.light');
  
  &:active {
    transform: scale(0.98);
    opacity: 0.9;
  }
}

.product-image-wrapper {
  margin-bottom: 8px;
}

.product-info {
  width: 100%;
}

.product-name {
  font-size: 14px;
  color: v-bind('theme.colors.text.primary');
  line-height: 1.3;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.price-wrapper {
  display: flex;
  align-items: baseline;
  gap: 6px;
  margin-bottom: 8px;
}

.flash-price {
  font-size: 16px;
  font-weight: 600;
  color: v-bind('theme.colors.error');
}

.original-price {
  font-size: 12px;
  color: v-bind('theme.colors.text.secondary');
  text-decoration: line-through;
}

.progress-wrapper {
  width: 100%;
}

.progress-text {
  font-size: 12px;
  color: v-bind('theme.colors.text.secondary');
  margin-top: 4px;
  text-align: center;
  display: block;
}
</style>
