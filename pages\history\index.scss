.content {
    padding-bottom: 10px;
    overflow: hidden;

    .scroll-view {
        height: 100vh;
    }
    .record {
        flex: 1;
        padding: 10px;
        display: flex;
        flex-direction: column;
        gap: 12px;
        .list {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .item {
            padding: 10px;
            background: #ffffff;
            border-radius: 6px;
            box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.06);
            .header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 12px;
                .no {
                    font-size: 17px;
                    font-weight: 500;
                    color: #00b678;
                }
                .status {
                    border-radius: 4px;
                    font-size: 12px;
                    padding: 4px 6px;
                    &.complete {
                        background-color: #f0f9eb;
                        color: #67c23a;
                    }
                    &.parsing {
                        background-color: #fcf6ec;
                        color: #e6a23c;
                    }
                    &.stop {
                        background-color: #fef0f0;
                        color: #f56c6c;
                    }
                }
            }
            .quotation-info {
                display: flex;
                flex-direction: column;
                gap: 6px;
                .info-item {
                    display: flex;
                    gap: 13px;
                    font-size: 13px;
                    .label {
                        color: #909399;
                    }
                    .value {
                        color: #303133;
                        &.total {
                            font-size: 13px;
                            font-weight: 500;
                            color: #f56c6c;
                        }
                    }
                }
            }

            .action {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-top: 12px;

                .re-quote {
                    display: flex;
                    gap: 8px;
                    align-items: center;
                    padding: 5px 12px;
                    border: 1px solid #dcdfe6;
                    border-radius: 4px;
                    .refresh-icon {
                        width: 12px;
                        height: 12px;
                    }
                    .name {
                        font-size: 12px;
                        color: #303133;
                    }
                }
                .preview-detail {
                    display: flex;
                    align-items: center;
                    gap: 4px;
                    .detail,
                    .preview {
                        border-radius: 4px;
                        padding: 5px 12px;
                        background: #00b678;
                        font-size: 12px;
                        color: #ffffff;
                    }
                }
            }
        }
    }
    .tip {
        display: flex;
        flex-direction: column;
        padding: 10px 16px;
        border: 1px solid #f8e3c5;
        border-radius: 4px;
        background-color: #fcf6ec;
        margin-top: 12px;
        .desc {
            display: flex;
            align-items: center;
            gap: 6px;
            .warning-icon {
                width: 16px;
                height: 16px;
            }
            .explain {
                color: #e6a23c;
                font-size: 16px;
                font-weight: 500;
            }
        }
        .desc-list {
            margin-top: 12px;
            display: flex;
            flex-direction: column;
            color: #606266;
            font-size: 13px;
            line-height: 22px;
        }
    }
}
