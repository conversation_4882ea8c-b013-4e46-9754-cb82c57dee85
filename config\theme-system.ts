/**
 * 多主题系统配置
 * 支持动态切换主色、辅助色和中性色
 */

import { ref, computed } from 'vue';

// 主题色彩定义
export interface ThemeColors {
  primary: string;      // 主色
  secondary: string;    // 辅助色
  accent: string;       // 强调色
  neutral: {           // 中性色系
    50: string;
    100: string;
    200: string;
    300: string;
    400: string;
    500: string;
    600: string;
    700: string;
    800: string;
    900: string;
  };
  success: string;
  warning: string;
  error: string;
  info: string;
}

// 预定义主题
export const themePresets = {
  // 默认主题 - 绿色系
  default: {
    primary: '#00b678',
    secondary: '#6366f1',
    accent: '#f59e0b',
    neutral: {
      50: '#f9fafb',
      100: '#f3f4f6',
      200: '#e5e7eb',
      300: '#d1d5db',
      400: '#9ca3af',
      500: '#6b7280',
      600: '#4b5563',
      700: '#374151',
      800: '#1f2937',
      900: '#111827',
    },
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#3b82f6',
  },
  
  // 蓝色主题
  blue: {
    primary: '#3b82f6',
    secondary: '#8b5cf6',
    accent: '#f59e0b',
    neutral: {
      50: '#f8fafc',
      100: '#f1f5f9',
      200: '#e2e8f0',
      300: '#cbd5e1',
      400: '#94a3b8',
      500: '#64748b',
      600: '#475569',
      700: '#334155',
      800: '#1e293b',
      900: '#0f172a',
    },
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#06b6d4',
  },
  
  // 紫色主题
  purple: {
    primary: '#8b5cf6',
    secondary: '#ec4899',
    accent: '#f59e0b',
    neutral: {
      50: '#faf5ff',
      100: '#f3e8ff',
      200: '#e9d5ff',
      300: '#d8b4fe',
      400: '#c084fc',
      500: '#a855f7',
      600: '#9333ea',
      700: '#7c3aed',
      800: '#6b21a8',
      900: '#581c87',
    },
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#8b5cf6',
  },
  
  // 红色主题
  red: {
    primary: '#ef4444',
    secondary: '#f97316',
    accent: '#eab308',
    neutral: {
      50: '#fef2f2',
      100: '#fee2e2',
      200: '#fecaca',
      300: '#fca5a5',
      400: '#f87171',
      500: '#ef4444',
      600: '#dc2626',
      700: '#b91c1c',
      800: '#991b1b',
      900: '#7f1d1d',
    },
    success: '#10b981',
    warning: '#f59e0b',
    error: '#dc2626',
    info: '#3b82f6',
  },
  
  // 暗色主题
  dark: {
    primary: '#60a5fa',
    secondary: '#a78bfa',
    accent: '#fbbf24',
    neutral: {
      50: '#18181b',
      100: '#27272a',
      200: '#3f3f46',
      300: '#52525b',
      400: '#71717a',
      500: '#a1a1aa',
      600: '#d4d4d8',
      700: '#e4e4e7',
      800: '#f4f4f5',
      900: '#fafafa',
    },
    success: '#22c55e',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#06b6d4',
  },
} as const;

// 当前主题状态
export const currentTheme = ref<keyof typeof themePresets>('default');

// 当前主题颜色
export const currentThemeColors = computed(() => {
  return themePresets[currentTheme.value];
});

// 完整的主题配置
export const dynamicTheme = computed(() => {
  const colors = currentThemeColors.value;
  
  return {
    colors: {
      primary: colors.primary,
      secondary: colors.secondary,
      accent: colors.accent,
      success: colors.success,
      warning: colors.warning,
      error: colors.error,
      info: colors.info,
      text: {
        primary: colors.neutral[900],
        secondary: colors.neutral[600],
        disabled: colors.neutral[400],
        inverse: colors.neutral[50],
      },
      background: {
        primary: colors.neutral[50],
        secondary: colors.neutral[100],
        card: colors.neutral[50],
        overlay: 'rgba(0, 0, 0, 0.5)',
      },
      border: {
        light: colors.neutral[200],
        medium: colors.neutral[300],
        dark: colors.neutral[400],
      },
      neutral: colors.neutral,
    },
    spacing: {
      xs: 4,
      sm: 8,
      md: 16,
      lg: 24,
      xl: 32,
      xxl: 48,
    },
    borderRadius: {
      sm: 4,
      md: 8,
      lg: 12,
      xl: 16,
      round: 9999,
    },
    fontSize: {
      xs: 12,
      sm: 14,
      md: 16,
      lg: 18,
      xl: 20,
      xxl: 24,
    },
    fontWeight: {
      light: 300,
      normal: 400,
      medium: 500,
      bold: 600,
    },
    shadows: {
      sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
      md: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
      lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
    },
  };
});

// 主题切换函数
export const switchTheme = (themeName: keyof typeof themePresets) => {
  currentTheme.value = themeName;
  
  // 保存到本地存储
  try {
    uni.setStorageSync('current-theme', themeName);
  } catch (error) {
    console.warn('Failed to save theme to storage:', error);
  }
};

// 初始化主题
export const initTheme = () => {
  try {
    const savedTheme = uni.getStorageSync('current-theme');
    if (savedTheme && themePresets[savedTheme as keyof typeof themePresets]) {
      currentTheme.value = savedTheme as keyof typeof themePresets;
    }
  } catch (error) {
    console.warn('Failed to load theme from storage:', error);
  }
};

// 获取主题颜色工具函数
export const getThemeColor = (colorPath: string): string => {
  const paths = colorPath.split('.');
  let color: any = dynamicTheme.value.colors;
  
  for (const path of paths) {
    color = color[path];
    if (!color) break;
  }
  
  return color || dynamicTheme.value.colors.primary;
};

// 获取中性色
export const getNeutralColor = (shade: keyof ThemeColors['neutral']): string => {
  return currentThemeColors.value.neutral[shade];
};

// 主题列表
export const themeList = Object.keys(themePresets).map(key => ({
  key: key as keyof typeof themePresets,
  name: getThemeName(key as keyof typeof themePresets),
  colors: themePresets[key as keyof typeof themePresets],
}));

// 获取主题显示名称
function getThemeName(key: keyof typeof themePresets): string {
  const names = {
    default: '默认主题',
    blue: '蓝色主题',
    purple: '紫色主题',
    red: '红色主题',
    dark: '暗色主题',
  };
  return names[key] || key;
}

// 主题应用工具函数
export const applyThemeToComponent = (componentConfig: any) => {
  const theme = dynamicTheme.value;

  // 如果组件配置中没有指定颜色，使用主题颜色
  const appliedConfig = { ...componentConfig };

  // 应用主题颜色到组件配置
  if (!appliedConfig.backgroundColor || appliedConfig.backgroundColor === 'auto') {
    appliedConfig.backgroundColor = theme.colors.background.primary;
  }

  if (!appliedConfig.primaryColor || appliedConfig.primaryColor === 'auto') {
    appliedConfig.primaryColor = theme.colors.primary;
  }

  if (!appliedConfig.secondaryColor || appliedConfig.secondaryColor === 'auto') {
    appliedConfig.secondaryColor = theme.colors.secondary;
  }

  if (!appliedConfig.textColor || appliedConfig.textColor === 'auto') {
    appliedConfig.textColor = theme.colors.text.primary;
  }

  if (!appliedConfig.borderColor || appliedConfig.borderColor === 'auto') {
    appliedConfig.borderColor = theme.colors.border.medium;
  }

  return appliedConfig;
};

// 生成主题CSS变量
export const generateThemeCSS = () => {
  const theme = dynamicTheme.value;
  const colors = theme.colors;

  return `
    :root {
      --theme-primary: ${colors.primary};
      --theme-secondary: ${colors.secondary};
      --theme-accent: ${colors.accent};
      --theme-success: ${colors.success};
      --theme-warning: ${colors.warning};
      --theme-error: ${colors.error};
      --theme-info: ${colors.info};
      --theme-text-primary: ${colors.text.primary};
      --theme-text-secondary: ${colors.text.secondary};
      --theme-text-disabled: ${colors.text.disabled};
      --theme-text-inverse: ${colors.text.inverse};
      --theme-bg-primary: ${colors.background.primary};
      --theme-bg-secondary: ${colors.background.secondary};
      --theme-bg-card: ${colors.background.card};
      --theme-border-light: ${colors.border.light};
      --theme-border-medium: ${colors.border.medium};
      --theme-border-dark: ${colors.border.dark};
      --theme-neutral-50: ${colors.neutral[50]};
      --theme-neutral-100: ${colors.neutral[100]};
      --theme-neutral-200: ${colors.neutral[200]};
      --theme-neutral-300: ${colors.neutral[300]};
      --theme-neutral-400: ${colors.neutral[400]};
      --theme-neutral-500: ${colors.neutral[500]};
      --theme-neutral-600: ${colors.neutral[600]};
      --theme-neutral-700: ${colors.neutral[700]};
      --theme-neutral-800: ${colors.neutral[800]};
      --theme-neutral-900: ${colors.neutral[900]};
    }
  `;
};

// 导出当前主题（兼容原有代码）
export const theme = dynamicTheme;
