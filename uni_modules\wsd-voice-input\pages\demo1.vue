<template>
    <view
        class="demo-page"
        :style="{
            '--safe-bottom': `${
                windowInfo.screenHeight - windowInfo.safeArea.bottom + 20
            }px`,
        }"
    >
        <button
            class="play-btn"
            @click="onPlay"
        >
            <wsd-voice-icon
                style="display: inline-flex"
                ref="iconRef"
                animation="true"
                :size="4"
                :strokeWidth="2"
                :strokeGutter="3"
                :auto-animation="false"
            ></wsd-voice-icon>
            <text>{{
                ending ? "点击播放" : paused ? "继续播放" : "暂停播放"
            }}</text>
        </button>
        <wsd-voice-input-mask
            v-model="maskVisible"
            :triggerIcon="{
                size: 4,
                strokeWidth: 2,
                strokeGutter: 3,
            }"
            :showCancelBtn="true"
            :showTransferBtn="true"
            :allowCancelByMove="false"
            @record="onRecord"
        >
            <template #reference>
                <button
                    class="press-btn"
                    @click="onPress"
                >
                    按住说话
                </button>
            </template>
        </wsd-voice-input-mask>
    </view>
</template>

<script setup lang="ts">
import { ref } from "vue";
import globalAudioContext from "../utils/globalAudioContext";

const windowInfo = uni.getWindowInfo();

const maskVisible = ref(false);
const paused = ref(false);
const ending = ref(true);
const iconRef = ref(null);
let _globalAudioContext;

function onPress() {
    maskVisible.value = true;
}

function onRecord(tempFilePath: string) {
    _globalAudioContext = globalAudioContext.getContext(tempFilePath, {
        autoplay: true,
        onPlay() {
            console.log(">>>play");
            ending.value = false;
            paused.value = _globalAudioContext.paused;
            iconRef.value?.startAnimation();
        },
        onPause() {
            console.log(">>>pause");
            paused.value = _globalAudioContext.paused;
            iconRef.value?.endAnimation();
        },
        onEnded() {
            console.log(">>>ended");
            ending.value = true;
            paused.value = _globalAudioContext.paused;
            iconRef.value?.endAnimation();
        },
    });
}

function onPlay() {
    if (!paused.value) {
        globalAudioContext.pause();
    } else {
        globalAudioContext.play();
    }
}
</script>

<style lang="scss">
.demo-page {
    position: relative;
    width: 100vw;
    height: 100vh;
    background-color: #efefef;
}

.press-btn {
    position: fixed;
    display: block;
    width: 80vw;
    left: 10vw;
    bottom: var(--safe-bottom);
}

.play-btn {
    position: fixed;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80vw;
    left: 10vw;
    bottom: calc(var(--safe-bottom) + 60px);
}
</style>
