<template>
  <view class="product-component" :style="containerStyle">
    <view class="product-content" :style="contentStyle">
      <view 
        class="product-item"
        v-for="product in config.products"
        :key="product.id"
        :style="itemStyle"
        @click="handleProductClick(product)"
      >
        <!-- 商品图片 -->
        <view class="product-image-wrapper">
          <image 
            :src="product.image" 
            class="product-image"
            mode="aspectFill"
          />
        </view>
        
        <!-- 商品信息 -->
        <view class="product-info">
          <!-- 商品标题 -->
          <text v-if="config.showTitle" class="product-title">{{ product.title }}</text>
          
          <!-- 评分 -->
          <view v-if="config.showRating && product.rating" class="product-rating">
            <view class="rating-stars">
              <text 
                v-for="star in 5" 
                :key="star"
                class="star"
                :class="{ 'star-filled': star <= Math.floor(product.rating) }"
              >★</text>
            </view>
            <text class="rating-text">{{ product.rating }}</text>
          </view>
          
          <!-- 价格信息 -->
          <view v-if="config.showPrice" class="product-price">
            <text class="current-price">¥{{ product.price }}</text>
            <text v-if="product.originalPrice" class="original-price">¥{{ product.originalPrice }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { ProductConfig } from '../../types/dynamic-page';
import { convertStyleToCSS, sizeUtils, colorUtils, layoutUtils } from '../../utils/style-helper';
import { theme } from '../../config/theme';

interface Props {
  config: ProductConfig['config'];
  style?: ProductConfig['style'];
}

const props = defineProps<Props>();

// 容器样式
const containerStyle = computed(() => {
  if (!props.style) {
    return { width: '100%' };
  }

  const baseStyle = convertStyleToCSS(props.style);

  // 确保所有样式属性都是字符串
  const processedStyle = {};
  Object.keys(baseStyle).forEach(key => {
    processedStyle[key] = String(baseStyle[key]);
  });

  return {
    ...processedStyle,
    width: '100%',
  };
});

// 内容样式
const contentStyle = computed(() => {
  const columns = props.config.columns || 2;
  return {
    display: 'grid',
    gridTemplateColumns: `repeat(${columns}, 1fr)`,
    gap: `${theme.spacing.md}px`,
    padding: `${theme.spacing.md}px`,
  };
});

// 商品项样式
const itemStyle = computed(() => {
  return {
    display: 'flex',
    flexDirection: props.config.layout === 'list' ? 'row' : 'column',
    backgroundColor: theme.colors.background.paper,
    borderRadius: `${theme.borderRadius.md}px`,
    padding: `${theme.spacing.sm}px`,
    border: `1px solid ${theme.colors.border.light}`,
    transition: 'all 0.2s ease',
    cursor: 'pointer',
  };
});

// 处理商品点击
const handleProductClick = (product: any) => {
  if (product.link) {
    handleNavigation(product.link);
  }
};

// 处理导航
const handleNavigation = (url: string) => {
  if (!url) return;
  
  // 判断是否为外部链接
  if (url.startsWith('http://') || url.startsWith('https://')) {
    // 外部链接，使用webview打开
    uni.navigateTo({
      url: `/pages/webview/index?url=${encodeURIComponent(url)}`
    });
  } else {
    // 内部页面跳转
    uni.navigateTo({
      url: url,
      fail: () => {
        uni.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  }
};
</script>

<style lang="scss" scoped>
.product-component {
  width: 100%;
}

.product-content {
  width: 100%;
}

.product-item {
  &:active {
    transform: scale(0.98);
    opacity: 0.8;
  }
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.product-image-wrapper {
  position: relative;
  width: 100%;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 8px;
}

.product-image {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.product-title {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  line-height: 1.3;
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-rating {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 4px;
}

.rating-stars {
  display: flex;
  gap: 1px;
}

.star {
  font-size: 12px;
  color: #ddd;
}

.star-filled {
  color: #ffa500;
}

.rating-text {
  font-size: 12px;
  color: #666;
}

.product-price {
  display: flex;
  align-items: center;
  gap: 8px;
}

.current-price {
  font-size: 16px;
  font-weight: 600;
  color: #ff4d4f;
}

.original-price {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
}

// 列表布局样式
.product-content[data-layout="list"] {
  .product-item {
    flex-direction: row;
  }
  
  .product-image-wrapper {
    width: 80px;
    height: 80px;
    margin-right: 12px;
    margin-bottom: 0;
  }
}

// 响应式适配
@media screen and (max-width: 750px) {
  .product-content {
    padding: 12px;
    gap: 12px;
  }
  
  .product-title {
    font-size: 13px;
  }
  
  .current-price {
    font-size: 14px;
  }
}
</style>
