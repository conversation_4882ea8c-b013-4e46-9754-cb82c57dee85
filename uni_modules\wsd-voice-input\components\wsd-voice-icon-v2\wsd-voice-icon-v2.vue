<template>
    <view
        class="wpt-voice-icon-v2"
        :style="iconStyle"
    >
        <svg
            class="icon-instance"
            viewBox="0 0 1024 1024"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            :fill="color"
            :width="size"
            :height="size"
        >
            <path :d="iconPath"></path>
        </svg>
    </view>
</template>

<script setup lang="ts">
import {
    computed,
    onBeforeUnmount,
    onMounted,
    ref,
    unref,
    type CSSProperties,
} from "vue";
defineOptions({
    name: "wpt-voice-icon-v2",
});

export interface VoiceIconProps {
    color?: string;
    size?: number;
    animation?: boolean;
    animationSpeed?: number; // ms动画速度
    autoAnimation?: boolean;
}

const props = withDefaults(defineProps<VoiceIconProps>(), {
    color: "rgba(143, 143, 143, 1)",
    size: 24,
    animation: false,
    animationSpeed: 250,
    autoAnimation: false,
});

let animationInterval: ReturnType<typeof setInterval>;
const animationStep = ref(2);

const iconStyle = computed<CSSProperties>(() => {
    return {
        "--voice-icon-size": `${props.size}px`,
    };
});

const iconPath = computed<string>(() => {
    const pathStep = [
        "M190.14254229 457.79831914a78.15126094 78.15126094 0 0 0 0 110.92436982 78.7815123 78.7815123 0 1 0 0-110.92436982z",
        "M467.45346641 242.88235332a47.26890791 47.26890791 0 0 0-63.02520967 0 46.63865566 46.63865566 0 0 0 0 63.02520967 299.36974747 299.36974747 0 0 1 0 422.26890732 47.26890791 47.26890791 0 0 0 0 63.02521055 46.63865566 46.63865566 0 0 0 63.02520967 0 393.90756329 393.90756329 0 0 0 0-548.31932753z",
        "M674.80640791 77.12605038A46.00840342 46.00840342 0 0 0 608.62993701 77.12605038a46.00840342 46.00840342 0 0 0 0 63.02521056 520.58823487 520.58823487 0 0 1 0 735.50420156 47.26890791 47.26890791 0 0 0 0 66.80672227 47.26890791 47.26890791 0 0 0 66.80672226 0A614.49579844 614.49579844 0 0 0 674.80640791 77.12605038z",
    ];
    return `${pathStep.slice(0, unref(animationStep) + 1).join(" ")}`;
});

function startAnimation() {
    if (!props.animation) {
        endAnimation();
    } else {
        if (!animationInterval) {
            animationInterval = setInterval(() => {
                animationStep.value = (animationStep.value + 1) % 3;
            }, props.animationSpeed);
        }
    }
}

function endAnimation() {
    if (animationInterval) {
        clearInterval(animationInterval);
    }
    animationInterval = undefined;
    animationStep.value = 2;
}

onMounted(() => {
    if (props.animation && props.autoAnimation) {
        startAnimation();
    }
});

onBeforeUnmount(() => {
    endAnimation();
});

defineExpose({
    startAnimation,
    endAnimation,
});
</script>

<style scoped lang="scss">
.wpt-voice-icon {
    position: relative;
    box-sizing: border-box;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: var(--voice-icon-size);
    height: var(--voice-icon-size);
}
</style>
