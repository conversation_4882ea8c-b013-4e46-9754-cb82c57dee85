<template>
    <view class="content">
        <view class="body">
            <view class="header" :class="{ 'parse-complete': parseComplete }">
                <view class="analysis">
                    <image v-if="parseComplete" class="check-circle" src="/static/check-circle.png"></image>
                    <image v-else class="circle" src="/static/circle.png"></image>
                    <view class="status">{{ parseStatusName }}</view>
                    <template v-if="state.quotationInquiryProgress?.inquiryAttachmentList?.length">
                        <view class="file-name-list">
                            <view
                                class="file-name"
                                v-for="fileItem in state.quotationInquiryProgress?.inquiryAttachmentList"
                                :key="fileItem.inquiryAttachmentId"
                                @click="onFilePreview(fileItem)"
                            >
                                {{ fileItem.inquiryAttachmentName }}
                            </view>
                        </view>
                    </template>
                </view>
                <view class="progress">{{ state.quotationInquiryProgress?.parsedInquiryCount || 0 }}/{{ state.quotationInquiryProgress?.totalInquiryParseCount || 0 }}</view>
            </view>
            <view class="search" v-if="!parseStop">
                <uv-search shape="square" v-model="state.searchKeyword" placeholder="请输入型号/电压/规格" :showAction="false" @search="onSearch($event)"></uv-search>
                <view class="actions">
                    <view class="search_action" @click="onShowQuotationSet()">
                        <image class="search-icon" src="/static/search.png"></image>
                    </view>
                    <view class="add-quote_action" @click="onShowAddInquiry()">
                        <image class="add-quote-icon" src="/static/add-quote.png"></image>
                    </view>
                </view>
            </view>
            <view class="list">
                <view class="item" v-for="(item, index) in state.list" :key="item.id">
                    <view class="index">{{ index + 1 }}</view>
                    <view class="inquiry-info">
                        <template v-if="state.createShowOriginal">
                            <view class="origin" v-show="state.showOriginal">
                                <text user-select>
                                    <text class="excel-sheet">{{ item.excelSheet }}-{{ item.excelRowIndex }}</text>
                                    {{ item.columnRawContent }}
                                </text>
                            </view>
                        </template>

                        <view class="specification">
                            <view class="mode" @click="onShowEditInquiry(item)">
                                <view>{{ item.modelName }}</view>
                                <view>{{ item.voltageLevel }}</view>
                                <view>{{ item.specification }}</view>
                            </view>
                            <view class="price">
                                <text class="price_icon">￥</text>
                                <text class="total">{{ formatThousands(item.taxTotalAmount) }}</text>
                            </view>
                        </view>
                        <view class="unit">
                            <view class="unit-info">
                                <view @click="onShowEditQuantity(item)">
                                    <text class="number">{{ item.quantity }}</text>
                                    <text class="unit-name">{{ item.unit }}</text>
                                </view>
                                <view>
                                    <text class="number">{{ item.taxUnitPrice }}</text>
                                    <text class="unit-name">元/{{ item.unit }}</text>
                                </view>
                            </view>
                            <view class="action">
                             <!--   <view class="edit" @click="onShowEditInquiry(item)">
                                    <image class="edit-icon" src="/static/edit.png"></image>
                                </view> -->
                                <view class="delete" @click="onDeleteQuotationInquiryRow(item.id)">
                                    <image class="delete-icon" src="/static/delete.png"></image>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view class="total-price-box" :class="{ 'parse-complete': parseComplete }">
            <view class="total-price-content">
                <view class="total-label">总价:</view>
                <view class="total-price">
                    <view class="price_icon">￥</view>
                    <view class="total">{{ formatThousands(state.quotationInquiryProgress?.quotationAmount || 0) }}</view>
                </view>
            </view>
            <view class="generate-quotation" @click="onGenerateQuotation()" v-if="parseComplete">生成报价单</view>
        </view>
        <uv-popup
            ref="addOrEditPopup"
            :closeOnClickOverlay="false"
            :safeAreaInsetBottom="false"
            mode="bottom"
            bgColor="#F0F2F6"
            :customStyle="{
                height: editOrAddInquiryHeight
            }"
        >
            <add-edit-inquiry
                :model="state.actionInquiryModel"
                :quotationInquiryDetail="state.editQuotationInquiryDetail"
                @onSaveSuccess="handleSaveInquirySuccess($event)"
                @onSaveAddSuccess="handleSaveAddSuccess()"
                @onDelete="handleOnDelete($event)"
                @onCancel="handleOnCancel()"
            ></add-edit-inquiry>
        </uv-popup>
        <uv-popup
            ref="quotationDisplaySetPopup"
            :closeOnClickOverlay="false"
            bgColor="none"
            :round="8"
            :safeAreaInsetBottom="false"
            mode="center"
            :customStyle="{
                width: '80%'
            }"
        >
            <quotation-display-set
                :showOriginal="state.showOriginal"
                @onCancel="handleOnCancelDisplaySet()"
                @onSelected="handleOnSelectedDisplaySet($event)"
            ></quotation-display-set>
        </uv-popup>

        <uv-popup
            ref="editInquiryQuantityPopup"
            :closeOnClickOverlay="false"
            bgColor="none"
            :round="8"
            :safeAreaInsetBottom="false"
            mode="center"
            :customStyle="{
                width: '70%'
            }"
        >
            <edit-inquiry-quantity
                :versionId="state.productVersion?.id"
                :quotationInquiryDetail="state.editQuotationInquiryDetail"
                @onCancel="handleOnCancelEditQuantity()"
                @onSaveSuccess="handleSaveEditInquiryQuantitySuccess($event)"
            ></edit-inquiry-quantity>
        </uv-popup>

        <uv-popup ref="generateQuotationPopup" :closeOnClickOverlay="false" :safeAreaInsetBottom="false" mode="bottom" bgColor="#F0F2F6">
            <quotation-content @onCancel="handleOnCancelGenerateQuotation()" @onConfirm="handleOnConfirmGenerateQuotation($event)"></quotation-content>
        </uv-popup>

        <loading-page :loading="loading" :loading-text="loadingText"></loading-page>
    </view>
</template>

<script lang="ts" setup>
import { computed, onMounted, onUnmounted, reactive, ref } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import {
    deleteTaskItemById,
    getParseDetailByTaskItemId,
    getQuotationInquiryCompletedParseList,
    getQuotationInquiryDetail,
    getQuotationInquiryProgress,
    quotationInquiryExport,
    uploadFileAndAnalysis
} from '@/api/quotation';
import { IProductVersion, IQuotationInquiryProgress } from '@/models';
import { TaskParseStatusEnum, TaskParseStatusEnumMapDesc } from '@/enums';
import { IInquiryAttachment, IQuotationInquiryDetail, IQuotationInquiryExport, InquiryAttachment } from '@/models/quotation';
import { deepClone, filePreview, formatThousands, previewImage, reLaunch, showModal } from '@/utils';
import { useLoadingFn, useSystemInfo } from '@/hooks';
import { createPolling } from '@/utils/polling';
import { onHide } from '@dcloudio/uni-app';
import { getLatestVersion } from '@/api/product-version';
import { onShow } from '@dcloudio/uni-app';
import { batchGetShareFile, getShareFile } from '../../api/file';

interface IQueryParams {
    taskId?: string;

    /* 语音解析内容 **/
    voiceContent?: string;
}

let taskId: string;
let polling = null;
let isDisabledQuery = false;
let pollingQuotationInquiry = null;
let preQuotationAttachmentId = null;
const loading = ref(true);
const loadingText = ref();
const addOrEditPopup = ref();
const inquiryAttachmentList = ref([]);
const quotationDisplaySetPopup = ref();
const editInquiryQuantityPopup = ref();
const generateQuotationPopup = ref();
const { safeHeight } = useSystemInfo();
let queryParams: IQueryParams = {};
let allDeepCloneQuotationInquiryList: Array<IQuotationInquiryDetail> = [];
const state = reactive<{
    quotationInquiryProgress: IQuotationInquiryProgress;
    list: Array<IQuotationInquiryDetail>;
    editQuotationInquiryDetail: IQuotationInquiryDetail;
    actionInquiryModel: 'add' | 'edit';
    productVersion: IProductVersion;
    searchKeyword: string;
    showOriginal: boolean;
    createShowOriginal: boolean;
}>({
    quotationInquiryProgress: {},
    list: [],
    editQuotationInquiryDetail: {},
    actionInquiryModel: 'edit',
    productVersion: {},
    searchKeyword: '',
    showOriginal: false,
    createShowOriginal: false
});

const parseStatusName = computed(() => {
    const { parseStatus } = state.quotationInquiryProgress || {};
    const name = TaskParseStatusEnumMapDesc[parseStatus];
    if ([TaskParseStatusEnum.PARSING].includes(parseStatus)) {
        return `${name}…`;
    }
    return name;
});

const parseComplete = computed(() => state.quotationInquiryProgress?.parseStatus === TaskParseStatusEnum.PARSED);
const parseStop = computed(() => state.quotationInquiryProgress?.parseStatus === TaskParseStatusEnum.PARSING_STOP);
const editOrAddInquiryHeight = computed(() => `${safeHeight.value - 20}px`);

// const handleGetQuotationInquiryCompletedParseList = useLoadingFn(onGetQuotationInquiryCompletedParseList, loading);

onLoad(async (query: IQueryParams) => {
    console.log('query', query);
    queryParams = query;
    isDisabledQuery = false;
});

onMounted(async () => {
    isDisabledQuery = false;
    const { data } = await getLatestVersion();
    state.productVersion = data;
});

onShow(async () => {
    // if (isDisabledQuery) {
    //     return;
    // }

    if (queryParams && Object.keys(queryParams).length) {
        handleQueryInquiry(queryParams.taskId);
    } else {
        const option: any = uni.getEnterOptionsSync();
        console.log('option', option);
        // 聊天素材打开小程序
        if (option.scene === 1173) {
            const url = option.forwardMaterials[0].path;
            taskId = await uploadFileAndAnalysis(url);
            if (taskId) {
                handleQueryInquiry(taskId);
            }
        } else {
            reLaunch({ url: '/pages/quote/index' });
        }
    }
});

onHide(() => {
    stopPoll();
});

onUnmounted(() => {
    isDisabledQuery = false;
    stopPoll();
});

const onSearch = (value: string) => {
    if (!value?.trim()) {
        state.list = allDeepCloneQuotationInquiryList;
        return;
    }
    const _value = value.toLocaleLowerCase();
    state.list = allDeepCloneQuotationInquiryList.filter(
        (x) => x.modelName?.toLocaleLowerCase().includes(_value) || x.voltageLevel?.toLocaleLowerCase().includes(_value) || x.specification?.toLocaleLowerCase().includes(_value)
    );
};

const onShowQuotationSet = () => {
    quotationDisplaySetPopup?.value?.open();
};

const handleOnCancelDisplaySet = () => {
    quotationDisplaySetPopup?.value?.close();
};

const handleOnSelectedDisplaySet = (value: { showOriginal: boolean }) => {
    console.log('value', value);

    if (!state.createShowOriginal && value.showOriginal) {
        state.createShowOriginal = true;
    }
    state.showOriginal = value.showOriginal;
    quotationDisplaySetPopup?.value?.close();
};

const onShowEditQuantity = (data: IQuotationInquiryDetail) => {
    state.editQuotationInquiryDetail = data;
    editInquiryQuantityPopup.value.open();
};

/** 新增报价单行 */
const onShowAddInquiry = () => {
    state.editQuotationInquiryDetail = { taskId: taskId };
    state.actionInquiryModel = 'add';
    addOrEditPopup.value.open();
};

const onShowEditInquiry = (data: IQuotationInquiryDetail) => {
    state.editQuotationInquiryDetail = data;
    state.actionInquiryModel = 'edit';
    addOrEditPopup.value.open();
};

const handleOnCancelEditQuantity = () => {
    editInquiryQuantityPopup.value?.close();
};

const handleSaveEditInquiryQuantitySuccess = async (id: string) => {
    await handleSaveSuccess(id);
    editInquiryQuantityPopup.value.close();
};

const onDeleteQuotationInquiryRow = async (id: string) => {
    const confirmRes = await showModal({ title: '确认删除', content: '删除成功后，数据将不能恢复' });
    if (!confirmRes.confirm) {
        return;
    }
    await deleteTaskItemById(id);
    await handleGetQuotationInquiryProgress();
    state.list = state.list.filter((x) => x.id != id);
};

const handleOnCancel = () => {
    handleCloseAddOrEditPopup();
};

const handleSaveInquirySuccess = async (id: string) => {
    await handleSaveSuccess(id);
    handleCloseAddOrEditPopup();
};

const handleSaveAddSuccess = async () => {
    try {
        loading.value = true;
        await onGetQuotationInquiryCompletedParseList();
        handleCloseAddOrEditPopup();
    } finally {
        loading.value = false;
    }
};

const handleCloseAddOrEditPopup = () => {
    state.editQuotationInquiryDetail = {};
    addOrEditPopup.value.close();
};

const handleSaveSuccess = async (id: string) => {
    const { data } = await getParseDetailByTaskItemId(id);
    await handleGetQuotationInquiryProgress();
    state.list = state.list.map((item) => {
        if (item.id == id) {
            return Object.assign(item, data);
        }
        return item;
    });
    allDeepCloneQuotationInquiryList = deepClone(state.list);
};

const handleOnDelete = async (id: string) => {
    state.list = state.list.filter((x) => x.id != id);
    allDeepCloneQuotationInquiryList = allDeepCloneQuotationInquiryList.filter((x) => x.id != id);
    await handleGetQuotationInquiryProgress();
    addOrEditPopup.value.close();
};

const onGenerateQuotation = async () => {
    generateQuotationPopup.value?.open();
};

const handleOnCancelGenerateQuotation = () => {
    generateQuotationPopup.value.close();
};

const handleOnConfirmGenerateQuotation = async (data: IQuotationInquiryExport) => {
    // const confirmRes = await showModal({ title: '确认生成报价单', content: '报价单生成后，可以预览发送', confirmColor: '#00B678', cancelColor: '#303133' });
    // if (!confirmRes.confirm) {
    //     return;
    // }

    // const { data: quotationInquiryDetail } = await getQuotationInquiryDetail(taskId);
    // if (quotationInquiryDetail.quotationAttachmentId && quotationInquiryDetail.quotationAttachmentUrl) {
    //     await filePreview({ url: quotationInquiryDetail.quotationAttachmentUrl });
    //     return;
    // }
    generateQuotationPopup.value.close();
    loading.value = true;
    await quotationInquiryExport({ ...(data || {}), taskId: taskId });

    polling = createPolling({
        fn: async (): Promise<any> => {
            return getQuotationInquiryDetail(taskId);
        },
        interval: 2000,
        maxRetry: 3, // 最大重试5次
        onError: (err: Error) => {
            console.error('轮询错误:', err);
        },
        onSuccess: async ({ data }) => {
            if (data.quotationAttachmentId && preQuotationAttachmentId !== data.quotationAttachmentId) {
                preQuotationAttachmentId = data.quotationAttachmentId;
                loading.value = false;
                polling.stop();
                const { data: fileUrlData } = await getShareFile(data.quotationAttachmentId);
                filePreview({ url: fileUrlData }, data.quotationAttachmentName || data.quotationBatchNo);
            }
        }
    });
    polling.start();
};

/** 轮询解析报价单 */
const handlePollingQuotationInquiry = () => {
    pollingQuotationInquiry = createPolling({
        fn: async (): Promise<any> => {
            return handleQueryInquiryProgressAndCompletedParseList();
        },
        interval: 2000,
        maxRetry: 2,
        onError: (err: Error) => {
            console.error('轮询错误:', err);
        }
        // onSuccess: () => {  }
    });
    pollingQuotationInquiry.start();
};

const handleQueryInquiryProgressAndCompletedParseList = async () => {
    try {
        await handleGetQuotationInquiryProgress();
        await onGetQuotationInquiryCompletedParseList();
    } catch (ex) {
        loading.value = false;
    }
};

async function onGetQuotationInquiryCompletedParseList() {
    const { data } = await getQuotationInquiryCompletedParseList(taskId);
    if (Array.isArray(data) && data.length) {
        state.list = data;
        allDeepCloneQuotationInquiryList = deepClone(data);
    } else {
        allDeepCloneQuotationInquiryList = [];
        state.list = [];
    }
}

const handleGetQuotationInquiryProgress = async () => {
    const { data } = await getQuotationInquiryProgress(taskId);
    state.quotationInquiryProgress = data;

    if (data && data.parseStatus === TaskParseStatusEnum.PARSING_STOP) {
        loading.value = false;
        pollingQuotationInquiry?.stop();
        return;
    }

    preQuotationAttachmentId = data.quotationAttachmentId;
    if (data && data.parseStatus === TaskParseStatusEnum.PARSED) {
        loading.value = false;
        pollingQuotationInquiry?.stop();
        onGetQuotationInquiryCompletedParseList();
    }
};

const handleQueryInquiry = async (_taskId: string) => {
    taskId = _taskId;
    loading.value = true;
    await handleQueryInquiryProgressAndCompletedParseList();

    if ([TaskParseStatusEnum.PARSING_STOP, TaskParseStatusEnum.PARSED].includes(state.quotationInquiryProgress?.parseStatus)) {
        pollingQuotationInquiry?.stop();
    } else {
        /** 轮询解析报价单 */
        loadingText.value = '解析中..';
        handlePollingQuotationInquiry();
    }
};

function stopPoll() {
    polling?.stop();
    pollingQuotationInquiry?.stop();
}

async function onFilePreview(file: InquiryAttachment) {
    // 判断文件是否为图片
    const parts = file.inquiryAttachmentName.split('.');

    if (parts.length < 2) {
        return;
    }

    const { data: fileUrlData } = await getShareFile(file.inquiryAttachmentId);
    const extension = parts.pop().toLowerCase();
    const imageExtensions = new Set(['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'tiff']);
    if (imageExtensions.has(extension)) {
        const inquiryAttachmentIds = state.quotationInquiryProgress?.inquiryAttachmentList.map((x) => x.inquiryAttachmentId);
        const urls = await batchGetShareFile(inquiryAttachmentIds);
        previewImage({
            count: fileUrlData,
            current: inquiryAttachmentIds.indexOf(file.inquiryAttachmentId),
            urls: urls
        });
    } else {
        filePreview(
            {
                url: fileUrlData
            },
            file.inquiryAttachmentName
        );
    }
}
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>
