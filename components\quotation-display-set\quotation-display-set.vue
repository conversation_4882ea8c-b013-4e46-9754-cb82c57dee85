<template>
    <view class="content">
        <view class="title">显示配置</view>
        <view class="set">
            <uv-checkbox-group v-model="selectValue">
                <uv-checkbox
                    :customStyle="{ marginBottom: '18px' }"
                    activeColor="#00B678"
                    v-for="(item, index) in checkboxList"
                    :key="index"
                    :label="item.label"
                    :name="item.name"
                ></uv-checkbox>
            </uv-checkbox-group>
        </view>
        <view class="action">
            <view class="cancel" @click="onCancel()">取消</view>
            <view class="confirm" @click="onConfirmSelected()">确定</view>
        </view>
    </view>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';

const emits = defineEmits<{
    (e: 'onCancel'): void;
    (e: 'onSelected', val: { showOriginal: boolean }): void;
}>();

const props = withDefaults(
    defineProps<{
        showOriginal: boolean;
    }>(),
    {
        showOriginal: false
    }
);

const selectValue = ref([]);

watch(
    () => props.showOriginal,
    (showOriginal) => {
        selectValue.value = [showOriginal];
    }
);

const checkboxList = [
    {
        label: '显示原始数据',
        name: true
    }
];

const onCancel = () => {
    emits('onCancel');
};

const onConfirmSelected = () => {
    emits('onSelected', {
        showOriginal: selectValue.value?.[0] || false
    });
};
</script>

<style lang="scss" scoped>
.content {
    display: flex;
    flex-direction: column;
    border-radius: 8px;
    background: #ffffff;
    padding: 20px;
    .title {
        font-size: 18px;
        font-weight: 600;
        text-align: center;
        color: #303133;
        margin-bottom: 20px;
    }
    .action {
        display: flex;
        margin-top: 10px;
        gap: 8px;
        .confirm,
        .cancel {
            font-size: 14px;
            border-radius: 4px;
            flex: 1;
            padding: 5px 0;
            text-align: center;
        }
        .cancel {
            color: #303133;
            border: 1px solid #dcdfe6;
        }
        .confirm {
            color: #ffffff;
            background: #00b678;
        }
    }
}
</style>
