.content {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: #ffffff;
    gap: 20px;
    justify-content: flex-end;
    .form {
        display: flex;
        background: #ffffff;
        box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.06);
        padding-left: 22px;
        flex-direction: column;
        border-radius: 10px;
        margin: 10px 10px;
        .item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #dcdfe6;
            .label {
                font-size: 14px;
                font-weight: 500;
                color: #303133;
                position: relative;
                &.required::before {
                    content: '*';
                    color: #f56c6c;
                    left: -10px;
                    position: absolute;
                }
            }
            .value {
                padding-right: 12px;
                flex: 1;
                &.organization {
                    display: flex;
                    align-items: center;
                    gap: 4px;
                    font-size: 14px;
                    color: #a8abb2;
                    justify-content: flex-end;
                    .arraw {
                        width: 14px;
                        height: 14px;
                        transform: rotate(270deg);
                    }
                    .name {
                        color: #303133;
                    }
                }
            }
        }
    }
    .action {
        display: flex;
        gap: 10px;
        padding: 10px;
        .btn {
            flex: 1;
            border-radius: 4px;
            padding: 10px 0;
            font-size: 14px;
            text-align: center;
        }
        .cancel {
            color: #303133;
            border: 1px solid #dcdfe6;
        }
        .confirm {
            color: #ffffff;
            background: #00b678;
        }
    }
}
