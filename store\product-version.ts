import { defineStore } from 'pinia';
import { getLatestVersion } from '@/api/product-version';

export const useShopConfigStore = defineStore('shopConfig', {
    state: () => {
        return {
            productVersion: {}
        };
    },
    getters: {
        productVersion: (state) => state.productVersion
    },
    actions: {
        async handleGetLatestVersion() {
            const { data } = await getLatestVersion();
            this.productVersion = data;
        }
    }
});
