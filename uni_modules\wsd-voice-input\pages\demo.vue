<template>
    <view
        class="demo-page"
        :style="{
            '--safe-bottom': `${
                windowInfo.screenHeight - windowInfo.safeArea.bottom + 20
            }px`,
        }"
    >
        <button
            class="play-btn"
            @click="onPlay"
        >
            <wsd-voice-icon
                style="display: inline-flex"
                ref="iconRef"
                animation="true"
                :size="4"
                :strokeWidth="2"
                :strokeGutter="3"
                :auto-animation="false"
            ></wsd-voice-icon>
            <text>{{
                ending ? "点击播放" : paused ? "继续播放" : "暂停播放"
            }}</text>
        </button>
        <wsd-voice-input-mask
            v-model="maskVisible"
            confirmColor="#D0E1FF"
            cancelColor="#ffffff"
            maskColor="rgba(255, 255, 255, .15)"
            maskClass="wsd-custom-voice-mask"
            popoverWidth="52vw"
            tipsColor="#777777"
            triggerActiveClass="wsd-custom-voice-input-trigger"
            triggerColor="#ffffff"
            triggerActiveColor="#ffffff"
            triggerIconColor="#8F8F8F"
            triggerActiveIconColor="#8F8F8F"
            :popoverIcon="{
                strokeColor: '#181818',
                strokeCount: 18,
                strokeWidth: 2,
                minScale: 0.1,
                initialScale: 0.8,
                awaitScale: 1.2,
                maxScale: 4,
                height: 56,
                awaitDelay: 200,
            }"
            :triggerIcon="{
                size: 4,
                strokeWidth: 2,
                strokeGutter: 3,
            }"
            :triggerShadow="true"
            triggerShadowColor="rgba(225, 229, 237, 1)"
            :showCancelBtn="false"
            :showTransferBtn="false"
            :allowCancelByMove="true"
            @record="onRecord"
        >
            <template #reference>
                <button
                    class="press-btn"
                    @click="onPress"
                >
                    按住说话
                </button>
            </template>
        </wsd-voice-input-mask>
    </view>
</template>

<script setup lang="ts">
import { ref } from "vue";
import globalAudioContext from "../utils/globalAudioContext";

const windowInfo = uni.getWindowInfo();

const maskVisible = ref(false);
const paused = ref(false);
const ending = ref(true);
const iconRef = ref(null);
let _globalAudioContext;

function onPress() {
    maskVisible.value = true;
}

function onRecord(tempFilePath: string) {
    _globalAudioContext = globalAudioContext.getContext(tempFilePath, {
        autoplay: true,
        onPlay() {
            console.log(">>>play");
            ending.value = false;
            paused.value = _globalAudioContext.paused;
            iconRef.value?.startAnimation();
        },
        onPause() {
            console.log(">>>pause");
            paused.value = _globalAudioContext.paused;
            iconRef.value?.endAnimation();
        },
        onEnded() {
            console.log(">>>ended");
            ending.value = true;
            paused.value = _globalAudioContext.paused;
            iconRef.value?.endAnimation();
        },
    });
}

function onPlay() {
    if (!paused.value) {
        globalAudioContext.pause();
    } else {
        globalAudioContext.play();
    }
}
</script>

<style lang="scss">
.demo-page {
    position: relative;
    width: 100vw;
    height: 100vh;
    background-color: #efefef;
}

.press-btn {
    position: fixed;
    display: block;
    width: 80vw;
    left: 10vw;
    bottom: var(--safe-bottom);
}

.play-btn {
    position: fixed;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80vw;
    left: 10vw;
    bottom: calc(var(--safe-bottom) + 60px);
}

.wsd-custom-voice-mask {
    // backdrop-filter: blur(1px);
}
.wsd-custom-voice-input-trigger {
    // 不生效 - clip-path之后
    /* &::before {
        content: "";
        position: absolute;
        top: -4rpx;
        left: 10rpx;
        width: 100%;
        height: 100%;
        z-index: 1;
        clip-path: ellipse(closest-side farthest-side);
        background-color: rgba(255, 255, 255, 0.5);
        backdrop-filter: blur(10px);
    } */
    .wsd-voice-input-mask__record-btn-inner {
        &::after {
            content: "";
            position: absolute;
            top: 6rpx;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: linear-gradient(
                180deg,
                #e1e5ed 0%,
                #f4f5f8 25.6%,
                #ffffff 43%
            );
            opacity: 0.9;
            z-index: 1;
            clip-path: ellipse(closest-side farthest-side);
        }
    }
}
</style>
