<template>
  <view class="divider-component" :style="containerStyle">
    <uv-divider
      :text="config.text"
      :text-color="config.textColor || theme.colors.text.secondary"
      :line-color="config.lineColor || theme.colors.border.light"
      :text-size="config.textSize || theme.fontSize.sm"
      :margin-top="config.marginTop || theme.spacing.md"
      :margin-bottom="config.marginBottom || theme.spacing.md"
      :half-width="config.halfWidth || false"
      :border-style="config.borderStyle || 'solid'"
      :text-position="config.textPosition || 'center'"
      :custom-style="dividerCustomStyle"
    />
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { convertStyleToCSS } from '../../utils/style-helper';
import { theme } from '../../config/theme';

interface DividerConfig {
  text: string;
  textColor: string;
  textSize: number;
  textPosition: 'left' | 'center' | 'right';
  lineColor: string;
  borderStyle: 'solid' | 'dashed' | 'dotted';
  marginTop: number;
  marginBottom: number;
  halfWidth: boolean;
  height: number;
  backgroundColor: string;
}

interface Props {
  config: DividerConfig;
  style?: any;
}

const props = defineProps<Props>();

// 容器样式
const containerStyle = computed(() => {
  if (!props.style) {
    return { width: '100%' };
  }
  
  const baseStyle = convertStyleToCSS(props.style);
  
  // 确保所有样式属性都是字符串
  const processedStyle = {};
  Object.keys(baseStyle).forEach(key => {
    processedStyle[key] = String(baseStyle[key]);
  });
  
  return {
    ...processedStyle,
    width: '100%',
    backgroundColor: props.config.backgroundColor || 'transparent',
  };
});

// 分割线自定义样式
const dividerCustomStyle = computed(() => {
  return {
    height: props.config.height ? `${props.config.height}px` : 'auto',
  };
});
</script>

<style lang="scss" scoped>
.divider-component {
  width: 100%;
}

// 响应式适配
@media screen and (max-width: 750px) {
  .divider-component {
    :deep(.uv-divider) {
      margin-left: 8px;
      margin-right: 8px;
    }
  }
}
</style>
