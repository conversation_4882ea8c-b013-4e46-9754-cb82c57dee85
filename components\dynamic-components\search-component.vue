<template>
  <view class="search-component" :style="containerStyle">
    <view class="search-wrapper" :style="searchWrapperStyle">
      <view class="search-input-container" :style="inputContainerStyle">
        <!-- 搜索图标 -->
        <uv-icon 
          v-if="config.showSearchIcon && config.iconPosition === 'left'"
          name="search"
          :color="config.iconColor || theme.colors.text.secondary"
          size="16"
          class="search-icon left-icon"
        />
        
        <!-- 输入框 -->
        <uv-input
          v-model="searchValue"
          :placeholder="config.placeholder || '请输入搜索内容'"
          :placeholder-style="`color: ${config.placeholderColor || theme.colors.text.disabled}`"
          :style="inputStyle"
          :border="false"
          :clearable="config.showClearButton"
          @input="handleInput"
          @confirm="handleSearch"
          @clear="handleClear"
          class="search-input"
        />
        
        <!-- 右侧搜索图标 -->
        <uv-icon 
          v-if="config.showSearchIcon && config.iconPosition === 'right'"
          name="search"
          :color="config.iconColor || theme.colors.text.secondary"
          size="16"
          class="search-icon right-icon"
          @click="handleSearch"
        />
      </view>
      
      <!-- 搜索按钮 -->
      <uv-button
        v-if="config.showButton"
        :text="config.buttonText || '搜索'"
        :color="config.buttonBackgroundColor || theme.colors.primary"
        :custom-style="buttonStyle"
        size="small"
        @click="handleSearch"
        class="search-button"
      />
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { convertStyleToCSS } from '../../utils/style-helper';
import { theme } from '../../config/theme';

interface SearchConfig {
  placeholder: string;
  backgroundColor: string;
  borderColor: string;
  borderRadius: number;
  height: number;
  fontSize: number;
  textColor: string;
  placeholderColor: string;
  showSearchIcon: boolean;
  iconPosition: 'left' | 'right';
  iconColor: string;
  showClearButton: boolean;
  buttonText: string;
  buttonBackgroundColor: string;
  buttonTextColor: string;
  buttonBorderRadius: number;
  showButton: boolean;
  searchType: string;
  margin: number;
}

interface Props {
  config: SearchConfig;
  style?: any;
}

const props = defineProps<Props>();

// 搜索值
const searchValue = ref('');

// 容器样式
const containerStyle = computed(() => {
  if (!props.style) {
    return { width: '100%' };
  }
  
  const baseStyle = convertStyleToCSS(props.style);
  
  // 确保所有样式属性都是字符串
  const processedStyle = {};
  Object.keys(baseStyle).forEach(key => {
    processedStyle[key] = String(baseStyle[key]);
  });
  
  return {
    ...processedStyle,
    width: '100%',
  };
});

// 搜索包装器样式
const searchWrapperStyle = computed(() => {
  return {
    display: 'flex',
    alignItems: 'center',
    gap: `${theme.spacing.sm}px`,
    padding: `${props.config.margin || 0}px`,
  };
});

// 输入容器样式
const inputContainerStyle = computed(() => {
  return {
    flex: '1',
    display: 'flex',
    alignItems: 'center',
    backgroundColor: props.config.backgroundColor || theme.colors.background.primary,
    borderRadius: `${props.config.borderRadius || theme.borderRadius.md}px`,
    border: `1px solid ${props.config.borderColor || theme.colors.border.light}`,
    height: `${props.config.height || 40}px`,
    paddingLeft: `${theme.spacing.sm}px`,
    paddingRight: `${theme.spacing.sm}px`,
  };
});

// 输入框样式
const inputStyle = computed(() => {
  return {
    flex: '1',
    fontSize: `${props.config.fontSize || theme.fontSize.sm}px`,
    color: props.config.textColor || theme.colors.text.primary,
    backgroundColor: 'transparent',
    border: 'none',
    outline: 'none',
  };
});

// 按钮样式
const buttonStyle = computed(() => {
  return {
    backgroundColor: props.config.buttonBackgroundColor || theme.colors.primary,
    color: props.config.buttonTextColor || theme.colors.text.inverse,
    borderRadius: `${props.config.buttonBorderRadius || theme.borderRadius.sm}px`,
    height: `${props.config.height || 40}px`,
    minWidth: '60px',
  };
});

// 处理输入
const handleInput = (value: string) => {
  searchValue.value = value;
  // 可以在这里添加实时搜索逻辑
};

// 处理搜索
const handleSearch = () => {
  if (!searchValue.value.trim()) {
    uni.showToast({
      title: '请输入搜索内容',
      icon: 'none'
    });
    return;
  }
  
  // 触发搜索事件
  uni.$emit('search', {
    keyword: searchValue.value,
    type: props.config.searchType
  });
  
  // 可以跳转到搜索结果页面
  uni.navigateTo({
    url: `/pages/search/result?keyword=${encodeURIComponent(searchValue.value)}`,
    fail: () => {
      uni.showToast({
        title: `搜索: ${searchValue.value}`,
        icon: 'none'
      });
    }
  });
};

// 处理清空
const handleClear = () => {
  searchValue.value = '';
};
</script>

<style lang="scss" scoped>
.search-component {
  width: 100%;
}

.search-wrapper {
  width: 100%;
}

.search-input-container {
  position: relative;
}

.search-icon {
  flex-shrink: 0;
  
  &.left-icon {
    margin-right: 8px;
  }
  
  &.right-icon {
    margin-left: 8px;
    cursor: pointer;
    
    &:active {
      opacity: 0.7;
    }
  }
}

.search-input {
  flex: 1;
  
  :deep(.uv-input__content) {
    background-color: transparent !important;
    border: none !important;
  }
}

.search-button {
  flex-shrink: 0;
}

// 响应式适配
@media screen and (max-width: 750px) {
  .search-wrapper {
    padding: 8px;
  }
  
  .search-button {
    min-width: 50px;
  }
}
</style>
