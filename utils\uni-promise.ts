import type { Response, UploadReq } from '@/models';

export const request = <T>(
    options: Pick<UniApp.RequestOptions, 'url' | 'data' | 'header' | 'method' | 'timeout' | 'dataType' | 'responseType' | 'sslVerify' | 'withCredentials' | 'firstIpv4'>
): Promise<Response<T>> => {
    return new Promise<Response<T>>((resolve, fail) => {
        uni.request({
            ...options,
            success: ({ data }) => {
                const response = <Response<T>>data;
                if (response.code === 401) {
                    return resolve(response);
                }
                if (response.code != 0) {
                    showToast({ title: response.msg, icon: 'none' });
                    return resolve(response);
                }
                resolve(response);
            },
            fail
        });
    });
};

export const uploadFile = <T>(options: UploadReq): Promise<Response<T>> => {
    return new Promise<Response<T>>((resolve, fail) => {
        uni.uploadFile({
            ...options,
            success: ({ data, statusCode }) => {
                resolve({
                    ...JSON.parse(data),
                    statusCode
                });
            },
            fail
        });
    });
};

export const login = (): Promise<UniApp.LoginRes> => {
    return new Promise<UniApp.LoginRes>((success, fail) => {
        uni.login({
            provider: 'weixin',
            success,
            fail
        });
    });
};

export const navigateTo = (options: Pick<UniApp.NavigateToOptions, 'url' | 'animationType' | 'animationDuration' | 'events'>): Promise<any> => {
    return new Promise((success, fail) => {
        uni.navigateTo({
            ...options,
            success,
            fail
        });
    });
};

export const navigateBack = (options?: Pick<UniApp.NavigateBackOptions, 'delta' | 'animationType' | 'animationDuration'>): Promise<any> => {
    return new Promise((success, fail) => {
        uni.navigateBack({
            ...(options || {}),
            success,
            fail
        });
    });
};

export const redirectTo = (options: Pick<UniApp.RedirectToOptions, 'url'>): Promise<any> => {
    return new Promise((success, fail) => {
        uni.redirectTo({
            ...options,
            success,
            fail
        });
    });
};

export const switchTab = (options: Pick<UniNamespace.SwitchTabOptions, 'url'>): Promise<any> => {
    return new Promise((success, fail) => {
        uni.switchTab({
            ...options,
            success,
            fail
        });
    });
};

export const reLaunch = (options: Pick<UniApp.ReLaunchOptions, 'url'>): Promise<any> => {
    return new Promise((success, fail) => {
        uni.reLaunch({
            ...options,
            success,
            fail
        });
    });
};

export const getUserProfile = (options: Pick<UniApp.GetUserProfileOptions, 'desc' | 'withCredentials' | 'lang' | 'timeout'>): Promise<UniApp.GetUserProfileRes> => {
    return new Promise((success, fail) => {
        uni.getUserProfile({
            ...options,
            provider: 'weixin',
            success,
            fail
        });
    });
};

export const getUserInfo = (options: Pick<UniApp.GetUserInfoOptions, 'withCredentials' | 'lang' | 'timeout'>): Promise<UniApp.GetUserInfoRes> => {
    return new Promise((success, fail) => {
        uni.getUserInfo({
            ...options,
            provider: 'weixin',
            success,
            fail
        });
    });
};

export const chooseImage = (options: UniApp.ChooseImageOptions): Promise<any> => {
    return new Promise((success, fail) => {
        uni.chooseImage({
            ...options,
            success,
            fail
        });
    });
};

export const chooseVideo = (options: UniApp.ChooseVideoOptions): Promise<any> => {
    return new Promise((success, fail) => {
        uni.chooseVideo({
            ...options,
            success,
            fail
        });
    });
};

export const previewImage = (options: UniApp.PreviewImageOptions): Promise<any> => {
    return new Promise((success, fail) => {
        uni.previewImage({
            ...options,
            success,
            fail
        });
    });
};

export const getLocation = (options: UniApp.GetLocationOptions): Promise<UniNamespace.GetLocationSuccess> => {
    return new Promise((success, fail) => {
        uni.getLocation({
            ...options,
            success,
            fail
        });
    });
};

export const getSetting = (): Promise<UniNamespace.GetSettingSuccessResult> => {
    return new Promise((success, fail) => {
        uni.getSetting({
            success,
            fail
        });
    });
};
export const openSetting = (): Promise<any> => {
    return new Promise((success, fail) => {
        uni.openSetting({
            success,
            fail
        });
    });
};

export const authorize = (options: UniNamespace.AuthorizeOptions): Promise<any> => {
    return new Promise((success, fail) => {
        uni.authorize({
            ...options,
            success,
            fail
        });
    });
};

export const showToast = (options: UniNamespace.ShowToastOptions): Promise<any> => {
    return new Promise((success, fail) => {
        uni.showToast({
            ...options,
            success,
            fail
        });
    });
};

export const showLoading = (options: UniNamespace.ShowLoadingOptions): Promise<any> => {
    return new Promise((success, fail) => {
        uni.showLoading({
            ...options,
            success,
            fail
        });
    });
};

export const showModal = (options: UniNamespace.ShowModalOptions): Promise<any> => {
    return new Promise((success, fail) => {
        uni.showModal({
            ...options,
            confirmColor: '#00b67',
            success,
            fail
        });
    });
};
export const getNetworkType = () => {
    return new Promise((success, fail) => {
        uni.getNetworkType({
            success,
            fail
        });
    });
};

export const chooseMedia = (options: UniNamespace.ChooseMediaOption): Promise<any> => {
    return new Promise((success, fail) => {
        uni.chooseMedia({
            ...options,
            success,
            fail
        });
    });
};

export const chooseMessageFile = (options: UniNamespace.ChooseMessageFileOption): Promise<any> => {
    return new Promise((success, fail) => {
        uni.chooseMessageFile({
            ...options,
            success,
            fail
        });
    });
};

export const downloadFile = (options: UniNamespace.DownloadFileOption, fileName?: string): Promise<UniNamespace.DownloadSuccessData> => {
    return new Promise((success, fail) => {
        uni.downloadFile({
            ...options,
            filePath: wx.env.USER_DATA_PATH + '/' + fileName,
            success,
            fail
        });
    });
};

export const openDocument = (options: UniNamespace.OpenDocumentOptions): Promise<any> => {
    return new Promise((success, fail) => {
        uni.openDocument({
            ...options,
            success,
            fail
        });
    });
};

export const filePreview = async (options: UniNamespace.DownloadFileOption, fileName?: string): Promise<any> => {
    try {
        showLoading({ title: '预览中' });
        let newFileName: string = fileName;
        const lowerCaseFileName: string = fileName.toLowerCase();
        if (!lowerCaseFileName.endsWith('.xlsx') && !lowerCaseFileName.endsWith('.xls')) {
            newFileName = `${fileName}.xlsx`;
        }
        const downloadRes = await downloadFile(options, newFileName);
        console.log('downloadRes', downloadRes);
        if (downloadRes.filePath) {
            await openDocument({ filePath: downloadRes.filePath, showMenu: true });
        }
        uni.hideLoading();
    } catch (ex) {
        showToast({ title: '文件预览失败', icon: 'none' });
        uni.hideLoading();
    }
};

export const requestSubscribeMessage = (option: UniNamespace.RequestSubscribeMessageOption) => {
    return new Promise((success, fail) => {
        uni.requestSubscribeMessage({
            ...option,
            success,
            fail
        });
    });
};

export default {
    request,
    login,
    navigateTo,
    navigateBack,
    redirectTo,
    reLaunch,
    uploadFile,
    getUserProfile,
    getUserInfo,
    chooseImage,
    chooseVideo,
    previewImage,
    getLocation,
    getSetting,
    openSetting,
    authorize,
    showToast,
    showModal,
    getNetworkType,
    chooseMedia,
    chooseMessageFile,
    downloadFile,
    openDocument,
    filePreview,
    switchTab,
    requestSubscribeMessage
};
