.content {
    display: flex;
    flex-direction: column;
    padding: 20px;
    .preview {
        border: 1px solid #dcdfe6;
        margin-bottom: 10px;
        background-color: #e6e8eb;
        border-radius: 6px;
        text-align: center;
    }
    .name {
        padding: 16px;
        gap: 10px;
        border-radius: 6px;
        background: #ffffff;
        .label {
            font-size: 12px;
            color: #909399;
        }
        .file_name {
            margin-top: 10px;
            font-size: 14px;
            color: #303133;
        }
    }
    .again {
        display: flex;
        align-items: center;
        gap: 12px;
        justify-content: center;
        margin-top: 20px;
        .again_icon {
            width: 14px;
            height: 14px;
        }
        .again_upload {
            font-size: 15px;
            line-height: 22px;
            color: #00b678;
        }
    }
}

.btn-contain {
    position: fixed;
    bottom: 0;
    background: #ffffff;
    width: 100%;
    padding-bottom: 20px;
    .btns {
        display: flex;
        justify-content: space-between;
        padding: 10px 16px;
        gap: 10px;
        .cancel,
        .submit {
            flex: 1;
            font-size: 18px;
            color: #303133;
            padding: 9px 0;
            text-align: center;
            border-radius: 4px;
        }
        .cancel {
            border: 1px solid #dcdfe6;
        }
        .submit {
            color: #ffffff;
            background: #00b678;
        }
    }
}
