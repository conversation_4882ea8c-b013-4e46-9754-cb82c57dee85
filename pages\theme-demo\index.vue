<template>
  <view class="theme-demo" :style="pageStyle">
    <!-- 头部 -->
    <view class="demo-header" :style="headerStyle">
      <text class="demo-title">主题系统演示</text>
      <theme-switcher />
    </view>
    
    <!-- 主题信息 -->
    <view class="theme-info-section" :style="cardStyle">
      <text class="section-title">当前主题信息</text>
      <view class="theme-details">
        <view class="color-row">
          <text class="color-label">主色：</text>
          <view class="color-sample" :style="{ backgroundColor: currentColors.primary }"></view>
          <text class="color-value">{{ currentColors.primary }}</text>
        </view>
        <view class="color-row">
          <text class="color-label">辅助色：</text>
          <view class="color-sample" :style="{ backgroundColor: currentColors.secondary }"></view>
          <text class="color-value">{{ currentColors.secondary }}</text>
        </view>
        <view class="color-row">
          <text class="color-label">强调色：</text>
          <view class="color-sample" :style="{ backgroundColor: currentColors.accent }"></view>
          <text class="color-value">{{ currentColors.accent }}</text>
        </view>
      </view>
    </view>
    
    <!-- 中性色展示 -->
    <view class="neutral-colors-section" :style="cardStyle">
      <text class="section-title">中性色系</text>
      <view class="neutral-grid">
        <view 
          v-for="(color, shade) in currentColors.neutral" 
          :key="shade"
          class="neutral-item"
          :style="{ backgroundColor: color }"
        >
          <text class="neutral-label" :style="{ color: getContrastColor(color) }">
            {{ shade }}
          </text>
        </view>
      </view>
    </view>
    
    <!-- 组件演示 -->
    <view class="components-demo">
      <!-- 按钮演示 -->
      <view class="demo-section" :style="cardStyle">
        <text class="section-title">按钮组件</text>
        <view class="button-group">
          <view class="demo-button primary" :style="primaryButtonStyle">
            <text class="button-text">主要按钮</text>
          </view>
          <view class="demo-button secondary" :style="secondaryButtonStyle">
            <text class="button-text">次要按钮</text>
          </view>
          <view class="demo-button accent" :style="accentButtonStyle">
            <text class="button-text">强调按钮</text>
          </view>
        </view>
      </view>
      
      <!-- 卡片演示 -->
      <view class="demo-section" :style="cardStyle">
        <text class="section-title">卡片组件</text>
        <view class="card-demo" :style="demoCardStyle">
          <text class="card-title">示例卡片</text>
          <text class="card-content">这是一个使用当前主题样式的卡片组件示例。</text>
          <view class="card-actions">
            <view class="card-button" :style="primaryButtonStyle">
              <text class="button-text small">确认</text>
            </view>
            <view class="card-button outline" :style="outlineButtonStyle">
              <text class="button-text small" :style="{ color: currentColors.primary }">取消</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 状态色演示 -->
      <view class="demo-section" :style="cardStyle">
        <text class="section-title">状态颜色</text>
        <view class="status-grid">
          <view class="status-item success" :style="{ backgroundColor: currentColors.success }">
            <text class="status-text">成功</text>
          </view>
          <view class="status-item warning" :style="{ backgroundColor: currentColors.warning }">
            <text class="status-text">警告</text>
          </view>
          <view class="status-item error" :style="{ backgroundColor: currentColors.error }">
            <text class="status-text">错误</text>
          </view>
          <view class="status-item info" :style="{ backgroundColor: currentColors.info }">
            <text class="status-text">信息</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 使用说明 -->
    <view class="usage-section" :style="cardStyle">
      <text class="section-title">使用说明</text>
      <view class="usage-content">
        <text class="usage-item">1. 点击右上角主题切换器选择不同主题</text>
        <text class="usage-item">2. 主题包含主色、辅助色、强调色和中性色系</text>
        <text class="usage-item">3. 所有组件会自动应用当前主题颜色</text>
        <text class="usage-item">4. 主题设置会自动保存到本地存储</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed, onMounted } from 'vue';
import ThemeSwitcher from '../../components/theme/theme-switcher.vue';
import { 
  currentThemeColors, 
  dynamicTheme, 
  initTheme 
} from '../../config/theme-system';

// 当前主题颜色
const currentColors = computed(() => currentThemeColors.value);
const theme = computed(() => dynamicTheme.value);

// 页面样式
const pageStyle = computed(() => ({
  backgroundColor: theme.value.colors.background.secondary,
  minHeight: '100vh',
  padding: '20px',
}));

// 头部样式
const headerStyle = computed(() => ({
  backgroundColor: theme.value.colors.background.primary,
  borderRadius: '12px',
  padding: '16px',
  marginBottom: '20px',
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  boxShadow: theme.value.shadows.sm,
}));

// 卡片样式
const cardStyle = computed(() => ({
  backgroundColor: theme.value.colors.background.primary,
  borderRadius: '12px',
  padding: '16px',
  marginBottom: '16px',
  boxShadow: theme.value.shadows.sm,
}));

// 按钮样式
const primaryButtonStyle = computed(() => ({
  backgroundColor: currentColors.value.primary,
  borderRadius: '8px',
  padding: '12px 24px',
}));

const secondaryButtonStyle = computed(() => ({
  backgroundColor: currentColors.value.secondary,
  borderRadius: '8px',
  padding: '12px 24px',
}));

const accentButtonStyle = computed(() => ({
  backgroundColor: currentColors.value.accent,
  borderRadius: '8px',
  padding: '12px 24px',
}));

const outlineButtonStyle = computed(() => ({
  backgroundColor: 'transparent',
  border: `1px solid ${currentColors.value.primary}`,
  borderRadius: '8px',
  padding: '12px 24px',
}));

// 演示卡片样式
const demoCardStyle = computed(() => ({
  backgroundColor: theme.value.colors.background.secondary,
  borderRadius: '8px',
  padding: '16px',
  border: `1px solid ${theme.value.colors.border.light}`,
}));

// 获取对比色（用于文字显示）
const getContrastColor = (backgroundColor) => {
  // 简单的对比度计算
  const hex = backgroundColor.replace('#', '');
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);
  const brightness = (r * 299 + g * 587 + b * 114) / 1000;
  return brightness > 128 ? '#000000' : '#ffffff';
};

// 初始化主题
onMounted(() => {
  initTheme();
});
</script>

<style lang="scss" scoped>
.theme-demo {
  width: 100%;
}

.demo-title {
  font-size: 20px;
  font-weight: 600;
  color: #374151;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
  display: block;
}

.theme-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.color-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-label {
  font-size: 14px;
  color: #6b7280;
  width: 60px;
}

.color-sample {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

.color-value {
  font-size: 12px;
  font-family: 'Courier New', monospace;
  color: #374151;
}

.neutral-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 8px;
}

.neutral-item {
  height: 60px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.neutral-label {
  font-size: 12px;
  font-weight: 500;
}

.button-group {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.demo-button {
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.2s ease;
  
  &:active {
    transform: scale(0.95);
  }
}

.button-text {
  color: white;
  font-size: 14px;
  font-weight: 500;
  
  &.small {
    font-size: 12px;
  }
}

.card-demo {
  .card-title {
    font-size: 16px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
    display: block;
  }
  
  .card-content {
    font-size: 14px;
    color: #6b7280;
    line-height: 1.5;
    margin-bottom: 16px;
    display: block;
  }
  
  .card-actions {
    display: flex;
    gap: 8px;
  }
  
  .card-button {
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    transition: transform 0.2s ease;
    
    &:active {
      transform: scale(0.95);
    }
  }
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.status-item {
  padding: 12px;
  border-radius: 8px;
  text-align: center;
}

.status-text {
  color: white;
  font-size: 14px;
  font-weight: 500;
}

.usage-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.usage-item {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
}

// 移动端适配
@media screen and (max-width: 750px) {
  .neutral-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .button-group {
    flex-direction: column;
  }
  
  .demo-button {
    text-align: center;
  }
  
  .status-grid {
    grid-template-columns: 1fr;
  }
}
</style>
