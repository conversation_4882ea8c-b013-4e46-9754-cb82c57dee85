<template>
  <view 
    class="image-component" 
    :style="containerStyle"
    @click="handleClick"
  >
    <uv-image
      :src="config.url"
      :width="config.width"
      :height="config.height"
      :borderRadius="config.borderRadius"
      :alt="config.alt"
      mode="aspectFill"
      :showLoading="true"
      :showError="true"
      :fade="true"
      loadingIcon="photo"
      errorIcon="error-circle"
      @click="handleImageClick"
      @load="handleImageLoad"
      @error="handleImageError"
    />
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { ImageConfig } from '../../types/dynamic-page';
import { convertStyleToCSS, sizeUtils } from '../../utils/style-helper';

interface Props {
  config: ImageConfig['config'];
  style?: ImageConfig['style'];
}

const props = defineProps<Props>();

// 容器样式
const containerStyle = computed(() => {
  const baseStyle = convertStyleToCSS(props.style);
  return {
    ...baseStyle,
    display: 'inline-block',
    width: sizeUtils.convertSize(props.config.width),
    height: sizeUtils.convertSize(props.config.height),
  };
});

// 处理容器点击事件
const handleClick = () => {
  if (props.config.link) {
    handleNavigation(props.config.link);
  }
};

// 处理图片点击事件
const handleImageClick = () => {
  if (props.config.link) {
    handleNavigation(props.config.link);
  } else {
    // 如果没有链接，则预览图片
    uni.previewImage({
      urls: [props.config.url],
      current: props.config.url
    });
  }
};

// 处理导航
const handleNavigation = (url: string) => {
  if (!url) return;
  
  // 判断是否为外部链接
  if (url.startsWith('http://') || url.startsWith('https://')) {
    // 外部链接，使用webview打开
    uni.navigateTo({
      url: `/pages/webview/index?url=${encodeURIComponent(url)}`
    });
  } else if (url.startsWith('/')) {
    // 内部页面链接
    uni.navigateTo({
      url: url,
      fail: () => {
        // 如果navigateTo失败，尝试switchTab
        uni.switchTab({ 
          url: url,
          fail: () => {
            uni.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      }
    });
  } else {
    // 其他类型的链接处理
    console.warn('Unsupported link type:', url);
  }
};

// 图片加载成功
const handleImageLoad = (e: any) => {
  console.log('Image loaded:', e);
};

// 图片加载失败
const handleImageError = (e: any) => {
  console.error('Image load error:', e);
  uni.showToast({
    title: '图片加载失败',
    icon: 'none'
  });
};
</script>

<style lang="scss" scoped>
.image-component {
  position: relative;
  overflow: hidden;
  
  // 添加点击效果
  &:active {
    opacity: 0.8;
    transform: scale(0.98);
    transition: all 0.1s ease;
  }
}

// 响应式适配
@media screen and (max-width: 750px) {
  .image-component {
    max-width: 100%;
    height: auto;
  }
}
</style>
