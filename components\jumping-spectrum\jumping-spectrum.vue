<template>
    <view
        class="content"
        :style="{
            '--safe-bottom': `${windowInfo.screenHeight - windowInfo.safeArea.bottom + 20}px`,
            height: `${height}vh`
        }"
    >
        <view v-if="!maskVisible" class="voice">
            <image class="voice-icon" src="/static/yuyin-2.png"></image>
        </view>
        <wsd-voice-input-mask
            v-model="maskVisible"
            confirmColor="#00b678"
            cancelColor="#ffffff"
            maskColor="rgba(255, 255, 255, .15)"
            maskClass="wsd-custom-voice-mask"
            popoverWidth="52vw"
            tipsColor="#777777"
            triggerActiveClass="wsd-custom-voice-input-trigger"
            triggerColor="#ffffff"
            triggerActiveColor="#ffffff"
            triggerIconColor="#8F8F8F"
            triggerActiveIconColor="#8F8F8F"
            :remote="true"
            :remoteMethod="handleUploadVoiceAndAnalysis"
            :popoverIcon="{
                strokeColor: '#181818',
                strokeCount: 18,
                strokeWidth: 2,
                minScale: 0.1,
                initialScale: 0.8,
                awaitScale: 1.2,
                maxScale: 4,
                height: 40,
                awaitDelay: 200
            }"
            :triggerIcon="{
                size: 4,
                strokeWidth: 2,
                strokeGutter: 3
            }"
            :triggerShadow="true"
            triggerShadowColor="#00b678"
            :showCancelBtn="false"
            :showTransferBtn="false"
            :allowCancelByMove="true"
            @record="onRecord"
        >
            <template #reference>
                <view class="press-btn-box">
                    <view class="press-btn" @click="onPress">长按住说话</view>
                </view>
            </template>
        </wsd-voice-input-mask>
        <loading-page :loading="loading" loadingText="解析中"></loading-page>
    </view>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import globalAudioContext from '@/uni_modules/wsd-voice-input/utils/globalAudioContext';
import { useLoadingFn } from '@/hooks';
import { uploadVoiceAnalysis } from '@/api/file';

const emits = defineEmits<{
    (e: 'onCancel'): void;
    (e: 'onConfirmVoice', val: string): void;
}>();

const props = withDefaults(defineProps<{ height: number }>(), {
    height: 80
});

const windowInfo = uni.getWindowInfo();
const loading = ref();
const maskVisible = ref(false);
const paused = ref(false);
const ending = ref(true);
const iconRef = ref(null);

const handleUploadVoiceAndAnalysis = useLoadingFn(uploadVoiceAndAnalysis, loading);

function onPress() {
    maskVisible.value = true;
}

function onRecord() {
    maskVisible.value = false;
}

async function uploadVoiceAndAnalysis(tempFilePath: string) {
    const { data } = await uploadVoiceAnalysis(tempFilePath);
    emits('onConfirmVoice', data);
}
</script>

<style lang="scss" scoped>
.content {
    position: relative;
    width: 100vw;
    //height: 80vh;
    border-radius: 8px;
    background-color: #ffffff;
    .voice {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
        .voice-icon {
            width: 40px;
            height: 40px;
            padding-bottom: 90px;
        }
    }
}

.press-btn-box {
    position: fixed;
    display: block;
    display: flex;
    align-items: center;
    bottom: var(--safe-bottom);
    width: 100%;
    justify-content: center;
    .press-btn {
        width: 80vw;
        text-align: center;
        font-size: 14px;
        color: #ffffff;
        padding: 9px 20px;
        border-radius: 4px;
        background: #00b678;
    }
}

.play-btn {
    position: fixed;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80vw;
    left: 10vw;
    bottom: calc(var(--safe-bottom) + 60px);
}

.wsd-custom-voice-mask {
    // backdrop-filter: blur(1px);
}
.wsd-custom-voice-input-trigger {
    // 不生效 - clip-path之后
    /* &::before {
        content: "";
        position: absolute;
        top: -4rpx;
        left: 10rpx;
        width: 100%;
        height: 100%;
        z-index: 1;
        clip-path: ellipse(closest-side farthest-side);
        background-color: rgba(255, 255, 255, 0.5);
        backdrop-filter: blur(10px);
    } */
    .wsd-voice-input-mask__record-btn-inner {
        &::after {
            content: '';
            position: absolute;
            top: 6rpx;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: linear-gradient(180deg, #e1e5ed 0%, #f4f5f8 25.6%, #ffffff 43%);
            opacity: 0.9;
            z-index: 1;
            clip-path: ellipse(closest-side farthest-side);
        }
    }
}
</style>
