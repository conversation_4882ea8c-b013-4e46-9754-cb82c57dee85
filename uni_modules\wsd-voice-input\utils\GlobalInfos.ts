// 全局设备信息
type LoadFontRecord = Pick<UniApp.LoadFontFaceOptions, 'family' | 'source'>

let systemInfo: UniApp.GetSystemInfoResult | undefined = undefined;
let deviceInfo: UniApp.GetDeviceInfoResult | undefined = undefined;
let windowInfo: UniApp.GetWindowInfoResult | undefined = undefined;
let menubarInfo: UniApp.GetMenuButtonBoundingClientRectRes | undefined = undefined;
let appBaseInfo: UniApp.GetAppBaseInfoResult | undefined = undefined;
const loadFonts: LoadFontRecord[] = [];


export function getSystemInfoSync(refresh = false) {
    if (!systemInfo || refresh) {
        systemInfo = uni.getSystemInfoSync();
    }
    return systemInfo;
}

export function getSystemInfo(refresh = false) {
    if (!systemInfo || refresh) {
        systemInfo = uni.getSystemInfoSync();
    }
    return systemInfo;
}

export function getSystemInfoASync(params: UniApp.GetSystemInfoOptions = {}, refresh = false) {
    if (!systemInfo || refresh) {
        return new Promise((resolve, reject) => {
            uni.getSystemInfo({
                success(res) {
                    params.success && params.success(res);
                    systemInfo = res;
                    resolve(res);
                },
                fail(err) {
                    params.fail && params.fail(err);
                    reject(err);
                },
                complete(res) {
                    params.complete && params.complete(res);
                }
            });
        });
    }
    params.success && params.success(systemInfo);
    return Promise.resolve(systemInfo);
}

export function getDeviceInfo(refresh = false) {
    if (!deviceInfo || refresh) {
        deviceInfo = uni.getDeviceInfo();
    }
    return deviceInfo;
}

export function getWindowInfo(refresh = false) {
    if (!windowInfo || refresh) {
        windowInfo = uni.getWindowInfo();
    }
    return windowInfo;
}

export function getMenuButtonBoundingClientRect(refresh = false) {
    if (!menubarInfo || refresh) {
        menubarInfo = uni.getMenuButtonBoundingClientRect();
    }
    return menubarInfo;
}

export function getAppBaseInfo(refresh = false) {
    if (!appBaseInfo || refresh) {
        appBaseInfo = uni.getAppBaseInfo();
    }
    return appBaseInfo;
}

export function loadFontFace(params: UniApp.LoadFontFaceOptions) {
    const hasLoaded = loadFonts.find((item: LoadFontRecord) => item.source === params.source && (!params.family || item.family === params.family));
    if (!hasLoaded) {
        uni.loadFontFace({
            ...params,
            success(res) {
                params.success && params.success(res);
                loadFonts.push({
                    family: params.family || '',
                    source: params.source
                })
            }
        })
    }
}

export default {
    loadFontFace,
    getAppBaseInfo,
    getDeviceInfo,
    getMenuButtonBoundingClientRect,
    getSystemInfo,
    getSystemInfoASync,
    getSystemInfoSync,
    getWindowInfo
}
