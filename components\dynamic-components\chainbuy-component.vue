<template>
  <view class="chainbuy-component" :style="containerStyle">
    <view class="chainbuy-wrapper" :style="wrapperStyle">
      <!-- 接龙头部 -->
      <view class="chainbuy-header" :style="headerStyle">
        <view class="header-content">
          <view class="header-left">
            <view class="badge" :style="badgeStyle">
              <text class="badge-text">{{ config.badgeText || '接龙' }}</text>
            </view>
            <view class="header-text">
              <text class="header-title">{{ config.title || '接龙专区' }}</text>
              <text class="header-desc">{{ config.description || '好友接龙，优惠共享' }}</text>
            </view>
          </view>
          
          <view class="header-right">
            <uv-button
              text="发起接龙"
              :color="config.buttonColor || theme.colors.primary"
              size="mini"
              @click="handleCreateChain"
            />
          </view>
        </view>
      </view>
      
      <!-- 接龙商品列表 -->
      <view class="chainbuy-content">
        <view
          v-for="(chain, index) in config.chains"
          :key="chain.id"
          class="chain-item"
          :style="chainItemStyle"
          @click="handleChainClick(chain, index)"
        >
          <!-- 商品信息 -->
          <view class="chain-product">
            <view class="product-image-wrapper">
              <uv-image
                :src="chain.product.image"
                :width="'60px'"
                :height="'60px'"
                :border-radius="4"
                mode="aspectFill"
                class="product-image"
              />
            </view>
            
            <view class="product-info">
              <text class="product-title">{{ chain.product.title }}</text>
              <view class="price-info">
                <text class="chain-price">¥{{ chain.product.chainPrice }}</text>
                <text class="original-price">原价¥{{ chain.product.originalPrice }}</text>
              </view>
              <text class="savings-text">接龙省¥{{ (chain.product.originalPrice - chain.product.chainPrice).toFixed(2) }}</text>
            </view>
          </view>
          
          <!-- 接龙状态 -->
          <view class="chain-status">
            <view class="status-header">
              <text class="organizer-name">{{ chain.organizer.name }} 发起</text>
              <view class="status-badge" :class="getStatusClass(chain.status)">
                <text class="status-text">{{ getStatusText(chain.status) }}</text>
              </view>
            </view>
            
            <!-- 参与者头像列表 -->
            <view class="participants-list">
              <view class="participants-avatars">
                <view
                  v-for="(participant, pIndex) in chain.participants.slice(0, 5)"
                  :key="participant.id"
                  class="participant-avatar"
                  :style="{ zIndex: 5 - pIndex }"
                >
                  <uv-image
                    :src="participant.avatar"
                    :width="'24px'"
                    :height="'24px'"
                    :border-radius="12"
                    mode="aspectFill"
                    class="avatar-image"
                  />
                </view>
                
                <view v-if="chain.participants.length > 5" class="more-participants">
                  <text class="more-text">+{{ chain.participants.length - 5 }}</text>
                </view>
              </view>
              
              <text class="participants-count">{{ chain.participants.length }}人已参与</text>
            </view>
            
            <!-- 接龙进度 -->
            <view class="chain-progress">
              <uv-line-progress
                :percentage="calculateChainProgress(chain.participants.length, chain.targetCount)"
                :active-color="config.progressColor || '#1890ff'"
                :inactive-color="'#f0f0f0'"
                :height="6"
                :show-text="false"
              />
              <view class="progress-text">
                <text class="current-count">{{ chain.participants.length }}</text>
                <text class="target-count">目标{{ chain.targetCount }}人</text>
              </view>
            </view>
            
            <!-- 倒计时 -->
            <view v-if="chain.endTime" class="countdown-section">
              <uv-count-down
                :time="getCountdownTime(chain.endTime)"
                format="DD天HH:mm:ss"
                :auto-start="true"
                @finish="handleCountdownFinish(chain)"
              >
                <template #default="{ days, hours, minutes, seconds }">
                  <view class="countdown-display">
                    <text class="countdown-label">剩余时间：</text>
                    <text class="countdown-time">{{ days }}天{{ hours }}:{{ minutes }}:{{ seconds }}</text>
                  </view>
                </template>
              </uv-count-down>
            </view>
          </view>
          
          <!-- 操作按钮 -->
          <view class="chain-actions">
            <uv-button
              :text="getActionButtonText(chain)"
              :color="getActionButtonColor(chain)"
              size="small"
              :disabled="chain.status === 'ended'"
              @click.stop="handleJoinChain(chain)"
            />
          </view>
        </view>
        
        <!-- 空状态 -->
        <view v-if="config.chains.length === 0" class="empty-state">
          <text class="empty-text">暂无接龙活动</text>
          <uv-button
            text="发起第一个接龙"
            :color="config.buttonColor || theme.colors.primary"
            size="small"
            @click="handleCreateChain"
          />
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { convertStyleToCSS } from '../../utils/style-helper';
import { theme } from '../../config/theme';

interface ChainProduct {
  id: string;
  title: string;
  image: string;
  chainPrice: number;
  originalPrice: number;
}

interface Participant {
  id: string;
  name: string;
  avatar: string;
}

interface ChainBuy {
  id: string;
  product: ChainProduct;
  organizer: {
    id: string;
    name: string;
    avatar: string;
  };
  participants: Participant[];
  targetCount: number;
  status: 'active' | 'success' | 'ended';
  endTime: string;
  createTime: string;
}

interface ChainBuyConfig {
  title: string;
  description: string;
  badgeText: string;
  headerAlign: 'left' | 'center' | 'right';
  chains: ChainBuy[];
  backgroundColor: string;
  headerBackgroundColor: string;
  badgeBackgroundColor: string;
  badgeTextColor: string;
  progressColor: string;
  buttonColor: string;
  spacing: number;
  margin: number;
  maxVisibleChains: number;
}

interface Props {
  config: ChainBuyConfig;
  style?: any;
}

const props = defineProps<Props>();

// 容器样式
const containerStyle = computed(() => {
  if (!props.style) {
    return { width: '100%' };
  }
  
  const baseStyle = convertStyleToCSS(props.style);
  
  const processedStyle = {};
  Object.keys(baseStyle).forEach(key => {
    processedStyle[key] = String(baseStyle[key]);
  });
  
  return {
    ...processedStyle,
    width: '100%',
  };
});

// 包装器样式
const wrapperStyle = computed(() => {
  return {
    backgroundColor: props.config.backgroundColor || '#ffffff',
    borderRadius: '12px',
    margin: `${props.config.margin || theme.spacing.sm}px`,
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
    overflow: 'hidden',
  };
});

// 头部样式
const headerStyle = computed(() => {
  return {
    backgroundColor: props.config.headerBackgroundColor || '#f8f9fa',
    padding: `${theme.spacing.md}px`,
    borderBottom: '1px solid #f0f0f0',
  };
});

// 徽章样式
const badgeStyle = computed(() => {
  return {
    backgroundColor: props.config.badgeBackgroundColor || '#1890ff',
    color: props.config.badgeTextColor || '#ffffff',
  };
});

// 接龙项样式
const chainItemStyle = computed(() => {
  return {
    borderBottom: '1px solid #f0f0f0',
    padding: `${theme.spacing.md}px`,
    cursor: 'pointer',
    transition: 'all 0.2s ease',
  };
});

// 计算接龙进度
const calculateChainProgress = (current: number, target: number) => {
  return target > 0 ? Math.round((current / target) * 100) : 0;
};

// 获取倒计时时间
const getCountdownTime = (endTime: string) => {
  const end = new Date(endTime).getTime();
  const now = Date.now();
  return Math.max(0, end - now);
};

// 获取状态文字
const getStatusText = (status: string) => {
  switch (status) {
    case 'active':
      return '进行中';
    case 'success':
      return '已成功';
    case 'ended':
      return '已结束';
    default:
      return '未知';
  }
};

// 获取状态样式类
const getStatusClass = (status: string) => {
  return `status-${status}`;
};

// 获取操作按钮文字
const getActionButtonText = (chain: ChainBuy) => {
  switch (chain.status) {
    case 'active':
      return '参与接龙';
    case 'success':
      return '查看详情';
    case 'ended':
      return '已结束';
    default:
      return '参与接龙';
  }
};

// 获取操作按钮颜色
const getActionButtonColor = (chain: ChainBuy) => {
  switch (chain.status) {
    case 'active':
      return props.config.buttonColor || '#1890ff';
    case 'success':
      return '#52c41a';
    case 'ended':
      return '#d9d9d9';
    default:
      return props.config.buttonColor || '#1890ff';
  }
};

// 处理接龙点击
const handleChainClick = (chain: ChainBuy, index: number) => {
  uni.navigateTo({
    url: `/pages/chainbuy/detail?id=${chain.id}`,
    fail: () => {
      uni.showToast({ title: '查看接龙详情', icon: 'none' });
    }
  });
};

// 处理参与接龙
const handleJoinChain = (chain: ChainBuy) => {
  if (chain.status === 'ended') {
    uni.showToast({
      title: '接龙已结束',
      icon: 'none'
    });
    return;
  }
  
  uni.showToast({
    title: `参与接龙: ${chain.product.title}`,
    icon: 'success'
  });
  
  // 可以在这里添加参与接龙逻辑
  uni.$emit('chainbuy-join', { chain });
};

// 处理发起接龙
const handleCreateChain = () => {
  uni.navigateTo({
    url: '/pages/chainbuy/create',
    fail: () => {
      uni.showToast({ title: '发起接龙', icon: 'none' });
    }
  });
};

// 处理倒计时结束
const handleCountdownFinish = (chain: ChainBuy) => {
  console.log('接龙倒计时结束:', chain.id);
};
</script>

<style lang="scss" scoped>
.chainbuy-component {
  width: 100%;
}

.chainbuy-wrapper {
  width: 100%;
}

.chainbuy-header {
  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .header-left {
    display: flex;
    align-items: center;
    flex: 1;
  }
  
  .badge {
    padding: 4px 8px;
    border-radius: 12px;
    margin-right: 12px;
    
    .badge-text {
      font-size: 12px;
      font-weight: 600;
    }
  }
  
  .header-text {
    flex: 1;
    
    .header-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      display: block;
      margin-bottom: 4px;
    }
    
    .header-desc {
      font-size: 12px;
      color: #666;
      display: block;
    }
  }
  
  .header-right {
    flex-shrink: 0;
  }
}

.chainbuy-content {
  max-height: 400px;
  overflow-y: auto;
}

.chain-item {
  &:active {
    background-color: #f8f9fa;
  }
  
  &:last-child {
    border-bottom: none;
  }
}

.chain-product {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  
  .product-image-wrapper {
    margin-right: 12px;
    flex-shrink: 0;
  }
  
  .product-info {
    flex: 1;
    
    .product-title {
      font-size: 14px;
      font-weight: 500;
      color: #333;
      margin-bottom: 4px;
      display: block;
    }
    
    .price-info {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 2px;
      
      .chain-price {
        font-size: 16px;
        color: #ff4757;
        font-weight: 600;
      }
      
      .original-price {
        font-size: 12px;
        color: #999;
        text-decoration: line-through;
      }
    }
    
    .savings-text {
      font-size: 12px;
      color: #52c41a;
    }
  }
}

.chain-status {
  margin-bottom: 12px;
  
  .status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    
    .organizer-name {
      font-size: 12px;
      color: #666;
    }
    
    .status-badge {
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 10px;
      
      &.status-active {
        background-color: #e6f7ff;
        color: #1890ff;
      }
      
      &.status-success {
        background-color: #f6ffed;
        color: #52c41a;
      }
      
      &.status-ended {
        background-color: #f5f5f5;
        color: #999;
      }
    }
  }
  
  .participants-list {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    
    .participants-avatars {
      display: flex;
      align-items: center;
      
      .participant-avatar {
        margin-right: -6px;
        border: 2px solid #ffffff;
        border-radius: 50%;
        
        .avatar-image {
          display: block;
        }
      }
      
      .more-participants {
        background-color: #f0f0f0;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 6px;
        
        .more-text {
          font-size: 10px;
          color: #666;
        }
      }
    }
    
    .participants-count {
      font-size: 12px;
      color: #666;
    }
  }
  
  .chain-progress {
    margin-bottom: 8px;
    
    .progress-text {
      display: flex;
      justify-content: space-between;
      margin-top: 4px;
      
      .current-count,
      .target-count {
        font-size: 10px;
        color: #666;
      }
    }
  }
  
  .countdown-section {
    .countdown-display {
      display: flex;
      align-items: center;
      
      .countdown-label {
        font-size: 10px;
        color: #666;
        margin-right: 4px;
      }
      
      .countdown-time {
        font-size: 10px;
        color: #ff4757;
        font-weight: 600;
      }
    }
  }
}

.chain-actions {
  text-align: right;
}

.empty-state {
  padding: 40px 20px;
  text-align: center;
  
  .empty-text {
    font-size: 14px;
    color: #999;
    margin-bottom: 16px;
    display: block;
  }
}

// 响应式适配
@media screen and (max-width: 750px) {
  .chain-product {
    .product-info {
      .product-title {
        font-size: 13px;
      }
      
      .chain-price {
        font-size: 14px;
      }
    }
  }
}
</style>
