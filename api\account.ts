import { uploadImage } from './file';
import { IAccountRegister, IAuth, ILoginWechatReq, IProfile, Response, IAccountForm, IAccountRegisterReq } from '@/models';
import { post, get } from '@/utils';
import { uploadFile } from '@/utils/http';

export const createRegister = (data: IAccountRegister): Promise<Response<boolean>> => {
    return post('admin-api/system/wechat/createRegisterReview', { data });
};

/** 获取注册信息 */
export const getRegisterInfo = (data: IAccountRegisterReq): Promise<Response<IAccountRegister>> => {
    return post('admin-api/system/wechat/getRegisterDetail', { data });
};

/** 登录 */
export const loginWeChat = (data: ILoginWechatReq): Promise<Response<IAuth>> => {
    return post('admin-api/system/wechat/ma/login', { data });
};

export const getProfile = (): Promise<Response<IProfile>> => {
    return get('admin-api/system/user/profile/get');
};

export const editAccount = (data: Partial<IAccountForm>) => {
    return post('admin-api/system/account/update', { data });
};

export const editAvatar = async (url: string) => {
    return await uploadFile('admin-api/system/user/profile/update-avatar', url, 'avatarFile');
};
