<template>
    <view class="dynamic-page" :style="pageContainerStyle">
        <!-- 自定义导航栏 -->
        <view v-if="pageConfig.pageConfig.navigation?.show" class="custom-navbar" :style="navbarStyle">
            <view class="navbar-content">
                <view class="navbar-left">
                    <view v-if="pageConfig.pageConfig.navigation.backButton" class="back-button" @click="handleBack">
                        <text class="back-icon">←</text>
                    </view>
                </view>

                <view class="navbar-center">
                    <text class="navbar-title" :style="navbarTitleStyle">
                        {{ pageConfig.pageConfig.navigation.title || '动态页面' }}
                    </text>
                </view>

                <view class="navbar-right">
                    <view v-for="button in pageConfig.pageConfig.navigation.customButtons" :key="button.id" class="custom-button" @click="handleCustomButton(button)">
                        <text class="button-text" :style="navbarTitleStyle">{{ button.text }}</text>
                    </view>
                </view>
            </view>
        </view>

        <!-- 页面内容 -->
        <view class="page-content" :style="contentStyle">
            <!-- 动态组件渲染 -->
            <view
                v-for="component in sortedComponents"
                :key="component.id"
                class="dynamic-component-wrapper"
                :style="getComponentWrapperStyle(component)"
            >
                <!-- 搜索组件 -->
                <search-component
                    v-if="component.type === 'search'"
                    :config="component.config"
                    :style="getComponentStyle(component)"
                />

                <!-- 轮播图组件 -->
                <carousel-component
                    v-else-if="component.type === 'carousel'"
                    :config="component.config"
                    :style="getComponentStyle(component)"
                />

                <!-- 公告组件 -->
                <notice-component
                    v-else-if="component.type === 'notice'"
                    :config="component.config"
                    :style="getComponentStyle(component)"
                />

                <!-- 图片组件 -->
                <image-component
                    v-else-if="component.type === 'image'"
                    :config="component.config"
                    :style="getComponentStyle(component)"
                />

                <!-- 九宫格菜单组件 -->
                <grid-menu-component
                    v-else-if="component.type === 'grid-menu'"
                    :config="component.config"
                    :style="getComponentStyle(component)"
                />

                <!-- 优惠券组件 -->
                <coupon-component
                    v-else-if="component.type === 'coupon'"
                    :config="component.config"
                    :style="getComponentStyle(component)"
                />

                <!-- 秒杀组件 -->
                <seckill-component
                    v-else-if="component.type === 'seckill'"
                    :config="component.config"
                    :style="getComponentStyle(component)"
                />

                <!-- 商品组件 -->
                <product-component
                    v-else-if="component.type === 'product'"
                    :config="component.config"
                    :style="getComponentStyle(component)"
                />

                <!-- 分割线组件 -->
                <divider-component
                    v-else-if="component.type === 'divider'"
                    :config="component.config"
                    :style="getComponentStyle(component)"
                />

                <!-- 文章组件 -->
                <article-component
                    v-else-if="component.type === 'article'"
                    :config="component.config"
                    :style="getComponentStyle(component)"
                />

                <!-- 团购组件 -->
                <group-buy-component
                    v-else-if="component.type === 'group-buy'"
                    :config="component.config"
                    :style="getComponentStyle(component)"
                />

                <!-- 接龙购买组件 -->
                <chain-buy-component
                    v-else-if="component.type === 'chain-buy'"
                    :config="component.config"
                    :style="getComponentStyle(component)"
                />

                <!-- 服务商品组件 -->
                <service-product-component
                    v-else-if="component.type === 'service-product'"
                    :config="component.config"
                    :style="getComponentStyle(component)"
                />

                <!-- 未知组件类型 -->
                <view v-else class="component-error">
                    <view class="error-icon">⚠️</view>
                    <text class="error-text">未知组件类型: {{ component.type }}</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import NoticeComponent from '../../components/dynamic-components/notice-component.vue';
import ImageComponent from '../../components/dynamic-components/image-component.vue';
import GridMenuComponent from '../../components/dynamic-components/grid-menu-component.vue';
import ProductComponent from '../../components/dynamic-components/product-component.vue';
import SearchComponent from '../../components/dynamic-components/search-component.vue';
import CarouselComponent from '../../components/dynamic-components/carousel-component.vue';
import CouponComponent from '../../components/dynamic-components/coupon-component.vue';
import DividerComponent from '../../components/dynamic-components/divider-component.vue';
import SeckillComponent from '../../components/dynamic-components/seckill-component.vue';
import ArticleComponent from '../../components/dynamic-components/article-component.vue';
import GroupBuyComponent from '../../components/dynamic-components/group-buy-component.vue';
import ChainBuyComponent from '../../components/dynamic-components/chainbuy-component.vue';
import ServiceProductComponent from '../../components/dynamic-components/service-product-component.vue';
import { convertStyleToCSS } from '../../utils/style-helper';
import { defaultConfig } from './config';

// 使用最新的JSON配置
const pageConfig = ref(defaultConfig);

// 计算属性
const sortedComponents = computed(() => {
    return [...pageConfig.value.components].sort((a, b) => a.sort - b.sort);
});

const pageContainerStyle = computed(() => {
    const config = pageConfig.value.pageConfig;
    const backgroundStyle = getBackgroundStyle(config.background);
    const paddingStyle = getPaddingStyle(config.padding);

    return {
        ...backgroundStyle,
        ...paddingStyle,
        minHeight: '100vh',
        width: '100%',
        boxSizing: 'border-box',
    };
});

// 获取背景样式
const getBackgroundStyle = (background) => {
    if (!background) return { backgroundColor: '#ffffff' };

    switch (background.type) {
        case 'solid':
            return { backgroundColor: background.color || '#ffffff' };
        case 'gradient':
            if (background.gradientType === 'linear') {
                const colors = background.gradientColors.join(', ');
                return {
                    background: `linear-gradient(${background.gradientDirection || 'to right'}, ${colors})`
                };
            }
            return { backgroundColor: background.color || '#ffffff' };
        default:
            return { backgroundColor: background.color || '#ffffff' };
    }
};

// 获取内边距样式
const getPaddingStyle = (padding) => {
    if (!padding) return {};

    return {
        paddingTop: `${padding.top || 0}px`,
        paddingRight: `${padding.right || 0}px`,
        paddingBottom: `${padding.bottom || 0}px`,
        paddingLeft: `${padding.left || 0}px`,
    };
};

// 获取组件包装器样式
const getComponentWrapperStyle = (component) => {
    const style = {
        marginBottom: '8px',
    };

    // 如果组件有背景配置，应用背景样式
    if (component.background) {
        const bgStyle = getBackgroundStyle(component.background);
        Object.assign(style, bgStyle);
    }

    return style;
};

// 获取组件样式 - 使用 convertStyleToCSS 工具函数
const getComponentStyle = (component) => {
    if (!component.style) {
        return {};
    }

    // 使用工具函数转换样式对象为CSS样式
    return convertStyleToCSS(component.style);
};

const navbarStyle = computed(() => {
    const nav = pageConfig.value.pageConfig.navigation;
    return {
        backgroundColor: nav?.backgroundColor || '#ffffff',
        borderBottom: '1px solid #f0f0f0'
    };
});

const navbarTitleStyle = computed(() => {
    const nav = pageConfig.value.pageConfig.navigation;
    return {
        color: nav?.textColor || '#333333',
        fontSize: '18px',
        fontWeight: '600'
    };
});

const contentStyle = computed(() => {
    const nav = pageConfig.value.pageConfig.navigation;
    return {
        paddingTop: nav?.show ? '44px' : '0'
    };
});
// 方法
const handleBack = () => {
    uni.navigateBack();
};

const handleCustomButton = (button) => {
    uni.showToast({ title: `点击了: ${button.text}`, icon: 'none' });
};
// 页面生命周期
onMounted(() => {
    console.log('动态页面加载');

    // 获取页面参数
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const options = currentPage.options;

    // 如果有传入的配置参数
    if (options.config) {
        try {
            const config = JSON.parse(decodeURIComponent(options.config));
            pageConfig.value = config;
            console.log('从URL参数加载配置:', config);
        } catch (error) {
            console.error('解析URL配置失败:', error);
        }
    }
});
</script>

<style lang="scss" scoped>
.dynamic-page {
    position: relative;
    width: 100%;
    min-height: 100vh;
}

.custom-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 44px;
    z-index: 1000;
}

.navbar-content {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 0 16px;
}

.navbar-left,
.navbar-right {
    flex: 1;
    display: flex;
    align-items: center;
}

.navbar-right {
    justify-content: flex-end;
    gap: 12px;
}

.navbar-center {
    flex: 2;
    text-align: center;
}

.back-button,
.custom-button {
    padding: 8px;
    cursor: pointer;

    &:active {
        opacity: 0.7;
    }
}

.back-icon {
    font-size: 18px;
    font-weight: bold;
}

.button-text {
    font-size: 14px;
}

.page-content {
    width: 100%;
}

.dynamic-component-wrapper {
    margin-bottom: 8px;
}

.component-error {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100px;
    background-color: #fff2f0;
    border: 1px solid #ffccc7;
    border-radius: 8px;
    margin: 8px 16px;

    .error-icon {
        font-size: 24px;
        margin-bottom: 8px;
    }

    .error-text {
        font-size: 14px;
        color: #ff4d4f;
    }
}
</style>
