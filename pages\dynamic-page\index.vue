<template>
    <view class="dynamic-page" :style="pageContainerStyle">
        <!-- 页面内容 -->
        <view class="page-content">
            <!-- 动态组件渲染 -->
            <view
                v-for="component in sortedComponents"
                :key="component.id"
                class="dynamic-component-wrapper"
                :style="getComponentWrapperStyle(component)"
            >
                <!-- 搜索组件 -->
                <search-component
                    v-if="component.type === 'search'"
                    :config="component.config"
                    :style="getComponentStyle(component)"
                />

                <!-- 轮播图组件 -->
                <carousel-component
                    v-else-if="component.type === 'carousel'"
                    :config="component.config"
                    :style="getComponentStyle(component)"
                />

                <!-- 公告组件 -->
                <notice-component
                    v-else-if="component.type === 'notice'"
                    :config="component.config"
                    :style="getComponentStyle(component)"
                />

                <!-- 图片组件 -->
                <image-component
                    v-else-if="component.type === 'image'"
                    :config="component.config"
                    :style="getComponentStyle(component)"
                />

                <!-- 九宫格菜单组件 -->
                <grid-menu-component
                    v-else-if="component.type === 'grid-menu'"
                    :config="component.config"
                    :style="getComponentStyle(component)"
                />

                <!-- 优惠券组件 -->
                <coupon-component
                    v-else-if="component.type === 'coupon'"
                    :config="component.config"
                    :style="getComponentStyle(component)"
                />

                <!-- 秒杀组件 -->
                <seckill-component
                    v-else-if="component.type === 'seckill'"
                    :config="component.config"
                    :style="getComponentStyle(component)"
                />

                <!-- 商品组件 -->
                <product-component
                    v-else-if="component.type === 'product'"
                    :config="component.config"
                    :style="getComponentStyle(component)"
                />

                <!-- 分割线组件 -->
                <divider-component
                    v-else-if="component.type === 'divider'"
                    :config="component.config"
                    :style="getComponentStyle(component)"
                />

                <!-- 文章组件 -->
                <article-component
                    v-else-if="component.type === 'article'"
                    :config="component.config"
                    :style="getComponentStyle(component)"
                />

                <!-- 团购组件 -->
                <group-buy-component
                    v-else-if="component.type === 'group-buy'"
                    :config="component.config"
                    :style="getComponentStyle(component)"
                />

                <!-- 接龙购买组件 -->
                <chain-buy-component
                    v-else-if="component.type === 'chain-buy'"
                    :config="component.config"
                    :style="getComponentStyle(component)"
                />

                <!-- 服务商品组件 -->
                <service-product-component
                    v-else-if="component.type === 'service-product'"
                    :config="component.config"
                    :style="getComponentStyle(component)"
                />

                <!-- 未知组件类型 -->
                <view v-else class="component-error">
                    <view class="error-icon">⚠️</view>
                    <text class="error-text">未知组件类型: {{ component.type }}</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import NoticeComponent from '../../components/dynamic-components/notice-component.vue';
import ImageComponent from '../../components/dynamic-components/image-component.vue';
import GridMenuComponent from '../../components/dynamic-components/grid-menu-component.vue';
import ProductComponent from '../../components/dynamic-components/product-component.vue';
import SearchComponent from '../../components/dynamic-components/search-component.vue';
import CarouselComponent from '../../components/dynamic-components/carousel-component.vue';
import CouponComponent from '../../components/dynamic-components/coupon-component.vue';
import DividerComponent from '../../components/dynamic-components/divider-component.vue';
import SeckillComponent from '../../components/dynamic-components/seckill-component.vue';
import ArticleComponent from '../../components/dynamic-components/article-component.vue';
import GroupBuyComponent from '../../components/dynamic-components/group-buy-component.vue';
import ChainBuyComponent from '../../components/dynamic-components/chainbuy-component.vue';
import ServiceProductComponent from '../../components/dynamic-components/service-product-component.vue';
import { defaultConfig } from './config';

// 使用最新的JSON配置
const pageConfig = ref(defaultConfig);

// 计算属性
const sortedComponents = computed(() => {
    return [...pageConfig.value.components].sort((a, b) => a.sort - b.sort);
});

const pageContainerStyle = computed(() => {
    const config = pageConfig.value.pageConfig;
    const backgroundStyle = getBackgroundStyle(config.background);
    const paddingStyle = getPaddingStyle(config.padding);

    return {
        ...backgroundStyle,
        ...paddingStyle,
        minHeight: '100vh',
        width: '100%',
        boxSizing: 'border-box',
    };
});

// 获取背景样式
const getBackgroundStyle = (background) => {
    if (!background) return { backgroundColor: '#ffffff' };

    switch (background.type) {
        case 'solid':
            return { backgroundColor: background.color || '#ffffff' };
        case 'gradient':
            if (background.gradientType === 'linear') {
                const colors = background.gradientColors.join(', ');
                return {
                    background: `linear-gradient(${background.gradientDirection || 'to right'}, ${colors})`
                };
            }
            return { backgroundColor: background.color || '#ffffff' };
        default:
            return { backgroundColor: background.color || '#ffffff' };
    }
};

// 获取内边距样式
const getPaddingStyle = (padding) => {
    if (!padding) return {};

    return {
        paddingTop: `${padding.top || 0}px`,
        paddingRight: `${padding.right || 0}px`,
        paddingBottom: `${padding.bottom || 0}px`,
        paddingLeft: `${padding.left || 0}px`,
    };
};
// 获取组件包装器样式
const getComponentWrapperStyle = (component) => {
    const style = {
        marginBottom: '8px',
    };

    // 如果组件有背景配置，应用背景样式
    if (component.background) {
        const bgStyle = getBackgroundStyle(component.background);
        Object.assign(style, bgStyle);
    }

    return style;
};

// 获取组件样式
const getComponentStyle = (component) => {
    const style = {};

    // 应用组件的 style 配置
    if (component.style) {
        const { padding, margin, borderRadius } = component.style;

        if (padding) {
            style.paddingTop = `${padding.top || 0}px`;
            style.paddingRight = `${padding.right || 0}px`;
            style.paddingBottom = `${padding.bottom || 0}px`;
            style.paddingLeft = `${padding.left || 0}px`;
        }

        if (margin) {
            style.marginTop = `${margin.top || 0}px`;
            style.marginRight = `${margin.right || 0}px`;
            style.marginBottom = `${margin.bottom || 0}px`;
            style.marginLeft = `${margin.left || 0}px`;
        }

        if (borderRadius) {
            style.borderRadius = `${borderRadius || 0}px`;
        }
    }

    return style;
};
// 页面生命周期
onMounted(() => {
    console.log('动态页面加载，使用最新JSON配置');
    console.log('页面配置:', pageConfig.value);

    // 获取页面参数
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const options = currentPage.options;

    // 如果有传入的配置参数，使用新的配置格式
    if (options.config) {
        try {
            const config = JSON.parse(decodeURIComponent(options.config));
            // 验证配置格式是否符合新的JSON结构
            if (config.pageConfig && config.components && Array.isArray(config.components)) {
                pageConfig.value = config;
                console.log('从URL参数加载新配置:', config);
            } else {
                console.warn('传入的配置格式不符合新的JSON结构，使用默认配置');
            }
        } catch (error) {
            console.error('解析URL配置失败:', error);
        }
    }

    // 输出组件信息用于调试
    console.log('排序后的组件列表:', sortedComponents.value);
});
</script>

<style lang="scss" scoped>
.dynamic-page {
    position: relative;
    width: 100%;
    min-height: 100vh;
    overflow-x: hidden;
}

.page-content {
    width: 100%;
    position: relative;
}

.dynamic-component-wrapper {
    position: relative;
    width: 100%;

    // 确保组件之间有适当的间距
    &:not(:last-child) {
        margin-bottom: 8px;
    }

    // 移动端适配
    @media screen and (max-width: 750px) {
        margin-bottom: 6px;
    }
}

.component-error {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100px;
    background-color: #fff2f0;
    border: 1px solid #ffccc7;
    border-radius: 8px;
    margin: 8px 16px;

    .error-icon {
        font-size: 24px;
        margin-bottom: 8px;
    }

    .error-text {
        font-size: 14px;
        color: #ff4d4f;
        text-align: center;
    }
}

// 响应式设计
@media screen and (max-width: 750px) {
    .dynamic-page {
        font-size: 14px;
    }

    .component-error {
        margin: 6px 12px;
        height: 80px;

        .error-icon {
            font-size: 20px;
            margin-bottom: 6px;
        }

        .error-text {
            font-size: 12px;
        }
    }
}

// 确保页面在不同设备上的兼容性
.dynamic-page {
    // 防止水平滚动
    overflow-x: hidden;

    // 确保在iOS设备上的正确显示
    -webkit-overflow-scrolling: touch;

    // 确保背景渐变在所有浏览器中正确显示
    background-attachment: fixed;
}
</style>
