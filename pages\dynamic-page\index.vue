<template>
    <view class="dynamic-page" :style="pageContainerStyle">
        <!-- 自定义导航栏 -->
        <view v-if="pageConfig.pageConfig.navigation?.show" class="custom-navbar" :style="navbarStyle">
            <view class="navbar-content">
                <view class="navbar-left">
                    <view v-if="pageConfig.pageConfig.navigation.backButton" class="back-button" @click="handleBack">
                        <text class="back-icon">←</text>
                    </view>
                </view>

                <view class="navbar-center">
                    <text class="navbar-title" :style="navbarTitleStyle">
                        {{ pageConfig.pageConfig.navigation.title || '动态页面' }}
                    </text>
                </view>

                <view class="navbar-right">
                    <view v-for="button in pageConfig.pageConfig.navigation.customButtons" :key="button.id" class="custom-button" @click="handleCustomButton(button)">
                        <text class="button-text" :style="navbarTitleStyle">{{ button.text }}</text>
                    </view>
                </view>
            </view>
        </view>

        <!-- 页面内容 -->
        <view class="page-content" :style="contentStyle">
            <!-- 动态组件渲染 -->
            <view v-for="component in sortedComponents" :key="component.id" class="dynamic-component-wrapper">
                <!-- 搜索组件 -->
                <search-component v-if="component.type === 'search'" :config="component.config" :style="component.style" />

                <!-- 轮播图组件 -->
                <carousel-component v-else-if="component.type === 'carousel'" :config="component.config" :style="component.style" />

                <!-- 公告组件 -->
                <notice-component v-else-if="component.type === 'notice'" :config="component.config" :style="component.style" />

                <!-- 图片组件 -->
                <image-component v-else-if="component.type === 'image'" :config="component.config" :style="component.style" />

                <!-- 九宫格菜单组件 -->
                <grid-menu-component v-else-if="component.type === 'grid-menu'" :config="component.config" :style="component.style" />

                <!-- 优惠券组件 -->
                <coupon-component v-else-if="component.type === 'coupon'" :config="component.config" :style="component.style" />

                <!-- 秒杀组件 -->
                <seckill-component v-else-if="component.type === 'seckill'" :config="component.config" :style="component.style" />

                <!-- 商品标签页组件 -->
                <product-tab-component v-else-if="component.type === 'product-tab'" :config="component.config" :style="component.style" />

                <!-- 团购组件 -->
                <groupbuy-component v-else-if="component.type === 'groupbuy'" :config="component.config" :style="component.style" />

                <!-- 接龙购买组件 -->
                <chainbuy-component v-else-if="component.type === 'chainbuy'" :config="component.config" :style="component.style" />

                <!-- 服务商品组件 -->
                <service-product-component v-else-if="component.type === 'service-product'" :config="component.config" :style="component.style" />

                <!-- 海报组件 -->
                <poster-component v-else-if="component.type === 'poster'" :config="component.config" :style="component.style" />

                <!-- 文章组件 -->
                <article-component v-else-if="component.type === 'article'" :config="component.config" :style="component.style" />

                <!-- 商品列表组件 -->
                <product-list-component v-else-if="component.type === 'product-list'" :config="component.config" :style="component.style" />

                <!-- 商品组件 -->
                <product-component v-else-if="component.type === 'product'" :config="component.config" :style="component.style" />

                <!-- 分割线组件 -->
                <divider-component v-else-if="component.type === 'divider'" :config="component.config" :style="component.style" />

                <!-- 轮播组件 -->
                <banner-component v-else-if="component.type === 'banner'" :config="component.config" :style="component.style" />

                <!-- 秒杀组件 -->
                <flash-sale-component v-else-if="component.type === 'flash-sale'" :config="component.config" :style="component.style" />

                <!-- 团购组件 -->
                <group-buy-component v-else-if="component.type === 'group-buy'" :config="component.config" :style="component.style" />

                <!-- 未知组件类型 -->
                <view v-else class="component-error">
                    <view class="error-icon">⚠️</view>
                    <text class="error-text">未知组件类型: {{ component.type }}</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import NoticeComponent from '../../components/dynamic-components/notice-component.vue';
import ImageComponent from '../../components/dynamic-components/image-component.vue';
import GridMenuComponent from '../../components/dynamic-components/grid-menu-component.vue';
import ProductListComponent from '../../components/dynamic-components/product-list-component.vue';
import ProductComponent from '../../components/dynamic-components/product-component.vue';
import BannerComponent from '../../components/dynamic-components/banner-component.vue';
import SearchComponent from '../../components/dynamic-components/search-component.vue';
import CarouselComponent from '../../components/dynamic-components/carousel-component.vue';
import CouponComponent from '../../components/dynamic-components/coupon-component.vue';
import DividerComponent from '../../components/dynamic-components/divider-component.vue';
import SeckillComponent from '../../components/dynamic-components/seckill-component.vue';
import ProductTabComponent from '../../components/dynamic-components/product-tab-component.vue';
import GroupbuyComponent from '../../components/dynamic-components/groupbuy-component.vue';
import ChainbuyComponent from '../../components/dynamic-components/chainbuy-component.vue';
import ServiceProductComponent from '../../components/dynamic-components/service-product-component.vue';
import PosterComponent from '../../components/dynamic-components/poster-component.vue';
import ArticleComponent from '../../components/dynamic-components/article-component.vue';
import FlashSaleComponent from '../../components/dynamic-components/flash-sale-component.vue';
import GroupBuyComponent from '../../components/dynamic-components/group-buy-component.vue';
import { pageStyleUtils } from '../../utils/style-helper';
import { defaultConfig } from './config';

// 使用您提供的JSON配置
const pageConfig = ref(defaultConfig);

// 计算属性
const sortedComponents = computed(() => {
    return [...pageConfig.value.components].sort((a, b) => a.sort - b.sort);
});

const pageContainerStyle = computed(() => {
    return pageStyleUtils.getResponsiveContainerStyle(pageConfig.value.pageConfig);
});

const navbarStyle = computed(() => {
    const nav = pageConfig.value.pageConfig.navigation;
    return {
        backgroundColor: nav?.backgroundColor || '#ffffff',
        borderBottom: '1px solid #f0f0f0'
    };
});

const navbarTitleStyle = computed(() => {
    const nav = pageConfig.value.pageConfig.navigation;
    return {
        color: nav?.textColor || '#333333',
        fontSize: '18px',
        fontWeight: '600'
    };
});

const contentStyle = computed(() => {
    const nav = pageConfig.value.pageConfig.navigation;
    return {
        paddingTop: nav?.show ? '44px' : '0'
    };
});
// 方法
const handleBack = () => {
    uni.navigateBack();
};

const handleCustomButton = (button) => {
    uni.showToast({ title: `点击了: ${button.text}`, icon: 'none' });
};
// 页面生命周期
onMounted(() => {
    console.log('动态页面加载');

    // 获取页面参数
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const options = currentPage.options;

    // 如果有传入的配置参数
    if (options.config) {
        try {
            const config = JSON.parse(decodeURIComponent(options.config));
            pageConfig.value = config;
            console.log('从URL参数加载配置:', config);
        } catch (error) {
            console.error('解析URL配置失败:', error);
        }
    }
});
</script>

<style lang="scss" scoped>
.dynamic-page {
    position: relative;
    width: 100%;
    min-height: 100vh;
}

.custom-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 44px;
    z-index: 1000;
}

.navbar-content {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 0 16px;
}

.navbar-left,
.navbar-right {
    flex: 1;
    display: flex;
    align-items: center;
}

.navbar-right {
    justify-content: flex-end;
    gap: 12px;
}

.navbar-center {
    flex: 2;
    text-align: center;
}

.back-button,
.custom-button {
    padding: 8px;
    cursor: pointer;

    &:active {
        opacity: 0.7;
    }
}

.back-icon {
    font-size: 18px;
    font-weight: bold;
}

.button-text {
    font-size: 14px;
}

.page-content {
    width: 100%;
}

.dynamic-component-wrapper {
    margin-bottom: 8px;
}

.component-error {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100px;
    background-color: #fff2f0;
    border: 1px solid #ffccc7;
    border-radius: 8px;
    margin: 8px 16px;

    .error-icon {
        font-size: 24px;
        margin-bottom: 8px;
    }

    .error-text {
        font-size: 14px;
        color: #ff4d4f;
    }
}
</style>
