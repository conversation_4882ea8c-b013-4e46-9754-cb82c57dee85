# 主题系统设计文档

## 概述

本主题系统支持动态切换主色、辅助色、强调色和中性色，为整个应用提供一致的视觉体验。

## 核心特性

### 🎨 多主题支持
- **默认主题**：绿色系，适合商务应用
- **蓝色主题**：经典蓝色，专业稳重
- **紫色主题**：现代紫色，创意时尚
- **红色主题**：活力红色，热情醒目
- **暗色主题**：深色模式，护眼舒适

### 🌈 颜色体系
每个主题包含：
- **主色 (Primary)**：品牌主色，用于主要按钮、链接等
- **辅助色 (Secondary)**：辅助色彩，用于次要元素
- **强调色 (Accent)**：强调色彩，用于突出重要信息
- **中性色系 (Neutral)**：10个层级的灰度色彩，用于文字、背景、边框等

### 🔄 动态切换
- 实时切换主题，无需刷新页面
- 自动保存主题设置到本地存储
- 支持主题变更事件监听

## 文件结构

```
config/
├── theme-system.ts          # 主题系统核心文件
└── THEME_SYSTEM.md          # 本文档

components/
└── theme/
    └── theme-switcher.vue   # 主题切换组件

pages/
├── theme-demo/
│   └── index.vue           # 主题演示页面
└── dynamic-page/
    └── theme-integration.vue # 主题集成示例
```

## 使用方法

### 1. 基础使用

```javascript
import { 
  currentThemeColors, 
  dynamicTheme, 
  switchTheme,
  initTheme 
} from '@/config/theme-system';

// 初始化主题（在 App.vue 或入口文件中调用）
initTheme();

// 获取当前主题颜色
const colors = currentThemeColors.value;
console.log(colors.primary); // 主色

// 切换主题
switchTheme('blue'); // 切换到蓝色主题
```

### 2. 在组件中使用

```vue
<template>
  <view :style="containerStyle">
    <text :style="textStyle">主题化文本</text>
  </view>
</template>

<script setup>
import { computed } from 'vue';
import { dynamicTheme } from '@/config/theme-system';

const theme = computed(() => dynamicTheme.value);

const containerStyle = computed(() => ({
  backgroundColor: theme.value.colors.background.primary,
  padding: `${theme.value.spacing.md}px`,
  borderRadius: `${theme.value.borderRadius.md}px`,
}));

const textStyle = computed(() => ({
  color: theme.value.colors.text.primary,
  fontSize: `${theme.value.fontSize.md}px`,
}));
</script>
```

### 3. 主题切换组件

```vue
<template>
  <theme-switcher />
</template>

<script setup>
import ThemeSwitcher from '@/components/theme/theme-switcher.vue';
</script>
```

### 4. 动态组件主题化

```javascript
import { applyThemeToComponent } from '@/config/theme-system';

// 自动应用主题到组件配置
const themedConfig = applyThemeToComponent({
  backgroundColor: 'auto', // 将使用主题背景色
  primaryColor: 'auto',    // 将使用主题主色
  textColor: 'auto'        // 将使用主题文字色
});
```

## API 参考

### 主要函数

#### `switchTheme(themeName)`
切换到指定主题
- **参数**：`themeName` - 主题名称 ('default' | 'blue' | 'purple' | 'red' | 'dark')
- **返回**：无

#### `initTheme()`
初始化主题系统，从本地存储加载保存的主题
- **参数**：无
- **返回**：无

#### `getThemeColor(colorPath)`
获取主题颜色
- **参数**：`colorPath` - 颜色路径，如 'primary'、'text.secondary'
- **返回**：颜色值字符串

#### `getNeutralColor(shade)`
获取中性色
- **参数**：`shade` - 色阶 (50-900)
- **返回**：颜色值字符串

#### `applyThemeToComponent(config)`
将主题应用到组件配置
- **参数**：`config` - 组件配置对象
- **返回**：应用主题后的配置对象

### 响应式数据

#### `currentTheme`
当前主题名称
```javascript
import { currentTheme } from '@/config/theme-system';
console.log(currentTheme.value); // 'default'
```

#### `currentThemeColors`
当前主题颜色
```javascript
import { currentThemeColors } from '@/config/theme-system';
console.log(currentThemeColors.value.primary); // '#00b678'
```

#### `dynamicTheme`
完整的主题配置
```javascript
import { dynamicTheme } from '@/config/theme-system';
console.log(dynamicTheme.value.colors);
console.log(dynamicTheme.value.spacing);
```

## 主题配置结构

```typescript
interface ThemeColors {
  primary: string;      // 主色
  secondary: string;    // 辅助色
  accent: string;       // 强调色
  neutral: {           // 中性色系
    50: string;   // 最浅
    100: string;
    // ... 
    900: string;  // 最深
  };
  success: string;     // 成功色
  warning: string;     // 警告色
  error: string;       // 错误色
  info: string;        // 信息色
}
```

## 最佳实践

### 1. 组件设计
- 使用 `auto` 值让组件自动应用主题颜色
- 提供颜色配置的默认值
- 支持主题变更时的动态更新

### 2. 颜色使用
- 主色：用于主要操作按钮、重要链接
- 辅助色：用于次要按钮、标签
- 强调色：用于警告、提示等需要注意的元素
- 中性色：用于文字、背景、边框等基础元素

### 3. 响应式设计
- 使用 `computed` 属性确保主题变更时样式自动更新
- 避免直接使用固定颜色值

### 4. 性能优化
- 主题切换时避免不必要的重新渲染
- 合理使用缓存和计算属性

## 扩展主题

### 添加新主题

```javascript
// 在 theme-system.ts 中添加新主题
export const themePresets = {
  // 现有主题...
  
  // 新主题
  orange: {
    primary: '#f97316',
    secondary: '#06b6d4',
    accent: '#8b5cf6',
    neutral: {
      // 定义中性色系...
    },
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#3b82f6',
  },
};
```

### 自定义主题属性

可以扩展主题配置，添加更多设计属性：
- 阴影样式
- 动画配置
- 字体设置
- 间距规范

## 注意事项

1. **兼容性**：确保所有组件都支持主题系统
2. **性能**：避免频繁的主题切换操作
3. **存储**：主题设置会自动保存到本地存储
4. **事件**：可以监听 `theme-changed` 事件处理主题变更

## 示例页面

- **主题演示**：`/pages/theme-demo/index` - 展示所有主题效果
- **集成示例**：`/pages/dynamic-page/theme-integration` - 动态组件主题化示例
