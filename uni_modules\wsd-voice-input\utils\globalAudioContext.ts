/**
 * 播放音频
 * 注意：
 * 1. 返回值不能赋值给ref对象，否则innerAudioContent的方法可能失效
 */

import { compareVersion } from "./common";
import { getAppBaseInfo } from "./GlobalInfos";

export interface InnerAudioContextOptions {
    startTime?: number;
    autoplay?: boolean;
    loop?: boolean;
    obeyMuteSwitch?: boolean;
    volume?: number;
    sessionCategory?: "ambient" | "soloAmbient" | "playback";
    playbackRate?: number;
    onCanplay?: (args: unknown) => void;
    onPlay?: (args: unknown) => void;
    onPause?: (args: unknown) => void;
    onStop?: (args: unknown) => void;
    onEnded?: (args: unknown) => void;
    onTimeUpdate?: (args: unknown) => void;
    onError?: (args: unknown) => void;
    onWaiting?: (args: unknown) => void;
    onSeeking?: (args: unknown) => void;
    onSeeked?: (args: unknown) => void;
    offCanplay?: (args: unknown) => void;
    offPlay?: (args: unknown) => void;
    offPause?: (args: unknown) => void;
    offStop?: (args: unknown) => void;
    offEnded?: (args: unknown) => void;
    offTimeUpdate?: (args: unknown) => void;
    offError?: (args: unknown) => void;
    offWaiting?: (args: unknown) => void;
    offSeeking?: (args: unknown) => void;
    offSeeked?: (args: unknown) => void;
}

export const InnerAudioContextHooks = [
    "onCanplay",
    "onPlay",
    "onPause",
    "onStop",
    "onEnded",
    "onTimeUpdate",
    "onError",
    "onWaiting",
    "onSeeking",
    "onSeeked",
    "offCanplay",
    "offPlay",
    "offPause",
    "offStop",
    "offEnded",
    "offTimeUpdate",
    "offError",
    "offWaiting",
    "offSeeking",
    "offSeeked",
];

export const InnerAudioContextPublicProps = [
    // "src", // 音频的数据链接，用于直接播放
    "startTime", // 开始播放的位置（单位：s）
    "autoplay", // 是否自动开始播放
    "loop", // 是否循环播放
    "obeyMuteSwitch", // 是否遵循系统静音开关
    "volume", // 音量，0-1
    "sessionCategory", // 设置音频播放模式
    "playbackRate", // 倍率
];

export const InnerAudioContextPrivateProps = [
    "duration",
    "currentTime",
    "paused",
    "buffered",
];

let _globalInnerAudioContext: UniApp.InnerAudioContext | undefined;

const DefaultInnerAudioContextPublicProps = {
    startTime: 0,
    autoplay: true,
    loop: false,
    obeyMuteSwitch: true,
    volume: undefined,
    sessionCategory: "playback",
    playbackRate: 1.0,
};

/**
 * 播放资源，如果不想马上播放资源，resoucePath设置为空，自行通过返回的innerAudioContext实例赋值。
 * @param resoucePath 音频资源地址
 * @param options innerAudioContext 对象配置
 * @returns innerAudioContext实例
 */
export function getContext(
    resoucePath: string,
    options?: InnerAudioContextOptions
): UniApp.InnerAudioContext {
    if (!_globalInnerAudioContext) {
        _globalInnerAudioContext = uni.createInnerAudioContext();
    }

    _globalInnerAudioContext.src = resoucePath;

    const version = getAppBaseInfo().SDKVersion;
    if (compareVersion(version, "2.3.0")) {
        // 2.3.0 obeyMuteSwitch不生效，只能使用wx.setInnerAudioOption设置
        wx.setInnerAudioOption({
            obeyMuteSwitch:
                options?.obeyMuteSwitch ??
                DefaultInnerAudioContextPublicProps.obeyMuteSwitch,
        });
    }

    for (const key of InnerAudioContextPublicProps) {
        _globalInnerAudioContext[key] =
            options?.[key] ?? DefaultInnerAudioContextPublicProps[key];
    }

    for (const hook of InnerAudioContextHooks) {
        if (options?.[hook]) {
            _globalInnerAudioContext[hook]?.(options[hook]);
        }
    }

    return _globalInnerAudioContext;
}

export function play() {
    if (_globalInnerAudioContext && _globalInnerAudioContext.paused) {
        _globalInnerAudioContext.play();
    }
}

export function pause() {
    if (_globalInnerAudioContext && !_globalInnerAudioContext.paused) {
        _globalInnerAudioContext.pause();
    }
}

export function stop() {
    if (_globalInnerAudioContext && !_globalInnerAudioContext.paused) {
        _globalInnerAudioContext.stop();
    }
}

export function seek(position: number) {
    if (_globalInnerAudioContext) {
        _globalInnerAudioContext.seek(position);
    }
}

export function destroy() {
    if (_globalInnerAudioContext) {
        _globalInnerAudioContext.destroy();
        _globalInnerAudioContext = undefined;
    }
}

export function isPaused() {
    if (_globalInnerAudioContext) {
        return _globalInnerAudioContext.paused;
    }
    return false;
}

export default {
    getContext,
    play,
    pause,
    seek,
    stop,
    destroy,
    isPaused,
};
