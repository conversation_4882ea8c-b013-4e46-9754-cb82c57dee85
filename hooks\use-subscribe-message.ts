import { requestSubscribeMessage, showToast } from "../utils";

export const useSubscribeMessageHook = () => {
    const RegisterAuditResultSubscribeMessage = async () => {
        try {
            return await requestSubscribeMessage({ tmplIds: ['XFxVAGCrs96eaiy2qaoXwrYE_M_NJreIiRxxeys_krU'] });
        } catch (e) {
            console.log(e);
            showToast({ title: '请到设置界面打开消息通知', icon: 'none' });
        }
    };

    return {
        RegisterAuditResultSubscribeMessage
    };
};
