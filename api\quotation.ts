import { IQuotationInquiryDetail } from '@/models/quotation/quotation-inquiry-detail';
import { batchUploadImageGetFiles, uploadImage } from './file';
import { getLatestVersion } from './product-version';
import { IPasteContentInquiry, IQuotationInquiryExport, IQuotationInquiryParse, IQuotationInquiryProgress, Response } from '@/models';
import { post, get, put, showToast } from '@/utils';
import { QuotationInquiryParseEnum } from '../enums';

/** 解析询价单 */
export const quotationInquiryParseInquiry = (data: IQuotationInquiryParse): Promise<Response<string>> => {
    return post('admin-api/business/quotationInquiry/parseInquiry', { data: { ...(data || {}), sourceType: QuotationInquiryParseEnum.MINI_PROGRAM } });
};

/** 询价单解析详情 */
export const getQuotationInquiryProgress = (taskId: string): Promise<Response<IQuotationInquiryProgress>> => {
    return get(`admin-api/business/quotationInquiry/getParseProgress/${taskId}`);
};

/** 解析明细查询-解析成功 */
export const getQuotationInquiryCompletedParseList = (taskId: string): Promise<Response<Array<IQuotationInquiryDetail>>> => {
    return get(`admin-api/business/quotationInquiry/getCompletedParseDetail/${taskId}`);
};

/** 删除报价单行 */
export const deleteTaskItemById = (taskId: string): Promise<Response<boolean>> => {
    return post(`admin-api/business/quotationInquiry/deleteTaskItemById/${taskId}`);
};

/** 单条解析明细编辑  */
export const quotationInquiryEditItem = (data: IQuotationInquiryDetail) => {
    return put(`admin-api/business/quotationInquiry/editItem/${data.id}`, { data });
};

/*** 根据id 查询解析 单条详情 */
export const getParseDetailByTaskItemId = (id: string): Promise<Response<IQuotationInquiryDetail>> => {
    return get(`admin-api/business/quotationInquiry/getParseDetailByTaskItemId/${id}`);
};

/** 报价单导出 */
export const quotationInquiryExport = (data: IQuotationInquiryExport) => {
    return post('admin-api/business/quotationInquiry/export', { data });
};

/** 报价单详情 */
export const getQuotationInquiryDetail = (taskId: string): Promise<Response<IQuotationInquiryProgress>> => {
    return get(`admin-api/business/quotationInquiry/getEnrichedParseProgress/${taskId}`);
};

/*** 添加单条解析详情 */
export const addQuotationInquiryItem = (data: IQuotationInquiryDetail) => {
    return post('admin-api/business/quotationInquiry/addItem', { data });
};

/** 文本解析型号规格 */
export const quotationInquiryByParseProduct = (data: IQuotationInquiryParse): Promise<Response<IQuotationInquiryDetail>> => {
    return post('admin-api/business/quotationInquiry/parseProduct', { data });
};

/*** 粘贴内容解析 */
export const pasteContentInquiry = (data: IPasteContentInquiry) => {
    return post('admin-api/business/quotationInquiry/pasteContentInquiry', { data: { ...(data || {}), sourceType: QuotationInquiryParseEnum.MINI_PROGRAM } });
};

export const uploadFileAndAnalysis = async (fileTempUrl: string) => {
    if (!fileTempUrl) {
        showToast({ title: '文件不能为空' });
        return null;
    }
    const { data: uploadRes } = await uploadImage(fileTempUrl);
    if (!uploadRes.id) {
        showToast({ title: '上传失败，请重新上传' });
        return null;
    }

    const { data: productVersion } = await getLatestVersion();

    const { data: taskId } = await quotationInquiryParseInquiry({
        versionId: productVersion?.id,
        inquiryFileIdList: [uploadRes.id]
    });
    return taskId;
};

export const batchUploadFileAndAnalysis = async (fileTempUrls: Array<string>) => {
    if (!Array.isArray(fileTempUrls) || fileTempUrls.length === 0) {
        showToast({ title: '文件不能为空' });
        return null;
    }
    const files = await batchUploadImageGetFiles(fileTempUrls);

    const fileIds = files.map((x) => x.id);

    if (!Array.isArray(fileIds) || fileIds.length === 0) {
        showToast({ title: '上传失败，请重新上传' });
        return null;
    }

    const { data: productVersion } = await getLatestVersion();
    if (!productVersion.id) {
        showToast({ title: '获取产品版本失败，请联系管理员' });
        return null;
    }

    const { data: taskId } = await quotationInquiryParseInquiry({
        versionId: productVersion.id,
        inquiryFileIdList: fileIds,
        sourceType: QuotationInquiryParseEnum.MINI_PROGRAM
    });
    return taskId;
};
