<template>
  <view class="grid-menu-component" :style="containerStyle">
    <view class="grid-menu-content" :style="contentStyle">
      <view 
        class="grid-menu-item"
        v-for="(item, index) in sortedItems"
        :key="item.id"
        :style="itemStyle"
        @click="handleItemClick(item)"
      >
        <!-- 图片 -->
        <view class="item-image-wrapper">
          <image
            :src="item.image"
            class="item-image"
            mode="aspectFill"
          />
        </view>

        <!-- 标题 -->
        <text class="item-title" :style="titleStyle">{{ item.name }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { GridMenuConfig } from '../../types/dynamic-page';
import { convertStyleToCSS, sizeUtils, colorUtils, layoutUtils } from '../../utils/style-helper';
import { theme } from '../../config/theme';

interface Props {
  config: GridMenuConfig['config'];
  style?: GridMenuConfig['style'];
}

const props = defineProps<Props>();

// 排序后的菜单项
const sortedItems = computed(() => {
  return [...props.config.items].sort((a, b) => a.sort - b.sort);
});

// 容器样式
const containerStyle = computed(() => {
  if (!props.style) {
    return { width: '100%' };
  }

  const baseStyle = convertStyleToCSS(props.style);

  // 确保所有样式属性都是字符串
  const processedStyle = {};
  Object.keys(baseStyle).forEach(key => {
    processedStyle[key] = String(baseStyle[key]);
  });

  return {
    ...processedStyle,
    width: '100%',
  };
});

// 内容样式
const contentStyle = computed(() => {
  const gridStyle = layoutUtils.createGridStyle(props.config.columns, theme.spacing.sm);
  
  return {
    ...gridStyle,
    backgroundColor: colorUtils.getThemeColorOrDefault(
      props.config.backgroundColor, 
      theme.colors.background.card
    ),
    padding: `${theme.spacing.md}px`,
    borderRadius: `${theme.borderRadius.lg}px`,
    border: props.config.showBorder 
      ? `1px solid ${colorUtils.getThemeColorOrDefault(props.config.borderColor, theme.colors.border.light)}`
      : 'none',
  };
});

// 菜单项样式
const itemStyle = computed(() => {
  return {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    padding: `${props.config.itemPadding || theme.spacing.sm}px`,
    margin: `${props.config.itemMargin || theme.spacing.xs}px`,
    backgroundColor: props.config.itemBackgroundColor || theme.colors.background.paper,
    borderRadius: `${props.config.itemBorderRadius || theme.borderRadius.md}px`,
    border: props.config.showBorder ? `1px solid ${props.config.borderColor || theme.colors.border.light}` : 'none',
    transition: 'all 0.2s ease',
    cursor: 'pointer',
  };
});

// 标题样式
const titleStyle = computed(() => {
  return {
    color: props.config.textColor || theme.colors.text.primary,
    fontSize: `${props.config.fontSize || theme.fontSize.sm}px`,
    fontWeight: theme.fontWeight.normal,
    textAlign: 'center',
    marginTop: `${theme.spacing.xs}px`,
    lineHeight: '1.2',
  };
});

// 处理菜单项点击
const handleItemClick = (item: any) => {
  if (item.clickType === 'navigate' && item.url) {
    handleNavigation(item.url);
  } else if (item.clickType === 'message' && item.message) {
    uni.showToast({
      title: item.message,
      icon: 'none'
    });
  }
};

// 处理导航
const handleNavigation = (url: string) => {
  if (!url) return;
  
  // 判断是否为外部链接
  if (url.startsWith('http://') || url.startsWith('https://')) {
    // 外部链接，使用webview打开
    uni.navigateTo({
      url: `/pages/webview/index?url=${encodeURIComponent(url)}`
    });
  } else if (url.startsWith('/')) {
    // 内部页面链接
    uni.navigateTo({
      url: url,
      fail: () => {
        // 如果navigateTo失败，尝试switchTab
        uni.switchTab({ 
          url: url,
          fail: () => {
            uni.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      }
    });
  } else {
    // 其他类型的链接处理
    console.warn('Unsupported link type:', url);
  }
};
</script>

<style lang="scss" scoped>
.grid-menu-component {
  width: 100%;
}

.grid-menu-content {
  width: 100%;
  box-sizing: border-box;
}

.grid-menu-item {
  position: relative;
  box-sizing: border-box;
  
  &:active {
    background-color: rgba(0, 0, 0, 0.05);
    transform: scale(0.95);
  }
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.02);
  }
}

.item-image-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  overflow: hidden;
}

.item-image {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}

.item-title {
  text-align: center;
  word-break: break-all;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// 响应式适配
@media screen and (max-width: 750px) {
  .grid-menu-content {
    padding: 12px;
  }
  
  .item-title {
    font-size: 12px;
  }
}
</style>
