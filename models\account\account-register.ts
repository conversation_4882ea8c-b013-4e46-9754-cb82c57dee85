export interface IAccountRegister {
    /**
     * 所在部门
     */
    department?: string;
    /**
     * 邮箱
     */
    email?: string;
    /**
     * 所属企业
     */
    enterprise?: string;
    /**
     * 手机号
     */
    phone: string;
    /**
     * 岗位
     */
    position?: string;
    /**
     * 备注
     */
    remark?: string;
    /**
     * 用户名
     */
    username: string;

    wechatUnionId?: string;

    /** 小程序OpendId */
    wechatMaOpenId: string;

    /** MA: 微信小程序 MP: 微信公众号 */
    registerSource: 'MA' | 'MP';
}

export interface IAccountRegisterReq{
    wechatUnionId?: string;
    
    /** 小程序OpendId */
    wechatMaOpenId: string;
}