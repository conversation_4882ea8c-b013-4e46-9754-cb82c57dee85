<template>
  <view class="carousel-component" :style="containerStyle">
    <swiper
      :autoplay="config.autoplay"
      :interval="config.interval || 3000"
      :duration="500"
      :circular="true"
      :indicator-dots="config.showIndicators"
      :indicator-color="indicatorColor"
      :indicator-active-color="indicatorActiveColor"
      :style="swiperStyle"
      @change="handleSwiperChange"
      class="carousel-swiper"
    >
      <swiper-item
        v-for="(image, index) in config.images"
        :key="image.id || index"
        class="swiper-item"
      >
        <view class="image-wrapper" @click="handleImageClick(image, index)">
          <uv-image
            :src="image.url"
            :mode="'aspectFill'"
            :width="'100%'"
            :height="'100%'"
            :border-radius="0"
            :show-loading="true"
            :show-error="true"
            :fade="true"
            class="carousel-image"
          />
          
          <!-- 图片标题覆盖层 -->
          <view v-if="image.title" class="image-overlay">
            <text class="image-title">{{ image.title }}</text>
          </view>
        </view>
      </swiper-item>
    </swiper>
    
    <!-- 自定义箭头 -->
    <view v-if="config.showArrows && config.images.length > 1" class="arrow-controls">
      <view class="arrow arrow-left" @click="handlePrevious">
        <uv-icon name="arrow-left" color="#ffffff" size="20" />
      </view>
      <view class="arrow arrow-right" @click="handleNext">
        <uv-icon name="arrow-right" color="#ffffff" size="20" />
      </view>
    </view>
    
    <!-- 自定义指示器 -->
    <view v-if="config.showIndicators && config.images.length > 1" class="custom-indicators">
      <view
        v-for="(image, index) in config.images"
        :key="index"
        class="indicator-dot"
        :class="{ 'active': currentIndex === index }"
        @click="handleIndicatorClick(index)"
      />
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { convertStyleToCSS } from '../../utils/style-helper';
import { theme } from '../../config/theme';

interface CarouselImage {
  id: string;
  url: string;
  title?: string;
  link?: string;
}

interface CarouselConfig {
  images: CarouselImage[];
  autoplay: boolean;
  interval: number;
  height: number;
  showIndicators: boolean;
  showArrows: boolean;
}

interface Props {
  config: CarouselConfig;
  style?: any;
}

const props = defineProps<Props>();

// 当前轮播索引
const currentIndex = ref(0);

// 容器样式
const containerStyle = computed(() => {
  if (!props.style) {
    return { width: '100%', position: 'relative' };
  }
  
  const baseStyle = convertStyleToCSS(props.style);
  
  // 确保所有样式属性都是字符串
  const processedStyle = {};
  Object.keys(baseStyle).forEach(key => {
    processedStyle[key] = String(baseStyle[key]);
  });
  
  return {
    ...processedStyle,
    width: '100%',
    position: 'relative',
  };
});

// 轮播样式
const swiperStyle = computed(() => {
  return {
    width: '100%',
    height: `${props.config.height || 200}px`,
    borderRadius: '8px',
    overflow: 'hidden',
  };
});

// 指示器颜色
const indicatorColor = computed(() => 'rgba(255, 255, 255, 0.5)');
const indicatorActiveColor = computed(() => '#ffffff');

// 处理轮播变化
const handleSwiperChange = (e: any) => {
  currentIndex.value = e.detail.current;
};

// 处理图片点击
const handleImageClick = (image: CarouselImage, index: number) => {
  if (image.link) {
    // 判断是否为外部链接
    if (image.link.startsWith('http://') || image.link.startsWith('https://')) {
      // 外部链接，使用webview打开
      uni.navigateTo({
        url: `/pages/webview/index?url=${encodeURIComponent(image.link)}`,
        fail: () => {
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    } else {
      // 内部页面跳转
      uni.navigateTo({
        url: image.link,
        fail: () => {
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    }
  } else {
    // 没有链接，显示图片标题或索引
    uni.showToast({
      title: image.title || `图片 ${index + 1}`,
      icon: 'none'
    });
  }
};

// 处理上一张
const handlePrevious = () => {
  const newIndex = currentIndex.value === 0 ? props.config.images.length - 1 : currentIndex.value - 1;
  currentIndex.value = newIndex;
};

// 处理下一张
const handleNext = () => {
  const newIndex = currentIndex.value === props.config.images.length - 1 ? 0 : currentIndex.value + 1;
  currentIndex.value = newIndex;
};

// 处理指示器点击
const handleIndicatorClick = (index: number) => {
  currentIndex.value = index;
};
</script>

<style lang="scss" scoped>
.carousel-component {
  width: 100%;
  position: relative;
}

.carousel-swiper {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.swiper-item {
  width: 100%;
  height: 100%;
}

.image-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  cursor: pointer;
  
  &:active {
    opacity: 0.9;
  }
}

.carousel-image {
  width: 100%;
  height: 100%;
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  padding: 20px 16px 16px;
}

.image-title {
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.arrow-controls {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  pointer-events: none;
  z-index: 10;
}

.arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  pointer-events: auto;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.5);
  }
  
  &:active {
    transform: translateY(-50%) scale(0.95);
  }
  
  &.arrow-left {
    left: 16px;
  }
  
  &.arrow-right {
    right: 16px;
  }
}

.custom-indicators {
  position: absolute;
  bottom: 16px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
  z-index: 10;
}

.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.2s ease;
  
  &.active {
    background-color: #ffffff;
    transform: scale(1.2);
  }
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.8);
  }
}

// 隐藏默认指示器
:deep(.uni-swiper-dots) {
  display: none !important;
}

// 响应式适配
@media screen and (max-width: 750px) {
  .arrow {
    width: 32px;
    height: 32px;
    
    &.arrow-left {
      left: 8px;
    }
    
    &.arrow-right {
      right: 8px;
    }
  }
  
  .custom-indicators {
    bottom: 12px;
  }
  
  .indicator-dot {
    width: 6px;
    height: 6px;
  }
}
</style>
