.content {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: auto;
    .body {
        flex: 1;
        overflow: auto;
        display: flex;
        flex-direction: column;
    }
    .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: #e5f8f1;
        padding: 13px 20px;

        .analysis {
            display: flex;
            align-items: center;
            gap: 12px;
            .circle {
                width: 12px;
                height: 12px;
            }
            .status {
                font-size: 14px;
                color: #00b678;
            }
            .file-name-list {
                display: flex;
                gap: 4;
                flex: 1;
                flex-wrap: wrap;
                .file-name {
                    font-size: 14px;
                    color: #606266;
                    word-break: break-all;
                }
            }
        }
        .progress {
            font-size: 12px;
            line-height: 16px;
            color: #00b678;
        }
        &.parse-complete {
            background: #f0f9eb;
            .analysis {
                .check-circle{
                   width: 16px;
                   height: 16px; 
                }
                .status {
                    color: #67c23a;
                }
            }
            .progress {
                color: #67c23a;
            }
        }
    }

    .search {
        background-color: #ffffff;
        padding: 16px 20px;
        display: flex;
        align-items: center;
        .actions {
            display: flex;
            .search_action,
            .add-quote_action {
                margin-left: 16px;
                margin-top: 6px;
                .search-icon,
                .add-quote-icon {
                    width: 24px;
                    height: 24px;
                }
            }
        }
    }
    .list {
        padding: 0 10px 10px;
        margin-top: 10px;
        display: flex;
        flex-direction: column;
        gap: 12px;
        flex: 1;
        overflow: auto;
        .item {
            border-radius: 4px;
            background: #ffffff;
            display: flex;
            align-items: flex-start;
            padding: 10px;
            gap: 8px;
            .index {
                background: #e5f8f1;
                color: #4dcca1;
                font-size: 10px;
                border-radius: 99px;
                padding: 2px 8px;
                margin-top: 3rpx;
            }
            .inquiry-info {
                display: flex;
                flex-direction: column;
                flex: 1;
                .origin {
                    border-bottom: 1px solid #d8d8d8;
                    padding-bottom: 10px;
                    font-size: 12px;
                    word-break: break-all;
                    color: #303133;
                    margin-bottom: 10px;
                    .excel-sheet {
                        color: #999999;
                        margin-right: 4px;
                    }
                }
                .specification {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    .mode {
                        font-size: 12px;
                        color: #606266;
                        display: flex;
                        gap: 10px;
                    }
                    .price {
                        color: #f56c6c;
                        font-weight: 500;
                        display: flex;
                        align-items: center;
                        .price_icon {
                            font-size: 12px;
                        }
                        .total {
                            font-size: 16px;
                        }
                    }
                }
                .unit {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin-top: 6px;
                    .unit-info {
                        display: flex;
                        gap: 20px;
                        .number {
                            font-size: 12px;
                            font-weight: 500;
                            color: #00b678;
                        }
                        .unit-name {
                            font-size: 12px;
                            font-weight: 600;
                            color: #303133;
                            margin-left: 2px;
                        }
                    }
                    .action {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        .edit {
                            width: 30px;
                            text-align: end;
                            .edit-icon {
                                width: 14px;
                                height: 14px;
                            }
                        }
                        .delete {
                            width: 30px;
                            text-align: end;
                            .delete-icon {
                                width: 14px;
                                height: 14px;
                            }
                        }
                    }
                }
            }
        }
    }
    .total-price-box {
        background: linear-gradient(180deg, #d7f6f4 0%, #fbfcfc 49%);
        box-shadow: 0px 2.5px 12.5px 0px rgba(21, 105, 137, 0.08);
        padding: 16px 0 26px;
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        &.parse-complete {
            justify-content: space-around;
        }
        .total-price-content {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16px;
            .total-label {
                font-size: 12px;
                font-weight: 500;
                line-height: 16px;
                color: #3d3d3d;
                margin-right: 10px;
                white-space: nowrap;
            }
            .total-price {
                display: flex;
                align-items: center;
                font-weight: 500;
                .price_icon {
                    font-size: 24px;
                }
                .total {
                    font-size: 32px;
                }
            }
        }
        .generate-quotation {
            background: #00b678;
            border-radius: 4px;
            color: #ffffff;
            white-space: nowrap;
            padding: 5px 16px;
        }
    }
}
