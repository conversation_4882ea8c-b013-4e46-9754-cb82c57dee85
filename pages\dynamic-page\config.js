// 您提供的完整JSON配置
export const defaultConfig = {
  "pageConfig": {
    "background": {
      "type": "gradient",
      "color": "#ffffff",
      "gradientType": "linear",
      "gradientDirection": "to right",
      "gradientColors": ["#ff6b6b", "#4ecdc4", "#45b7d1"]
    },
    "padding": {
      "top": 14,
      "right": 12,
      "bottom": 12,
      "left": 14
    },
    "previewMode": "mobile"
  },
  "components": [
    {
      "id": "search-1752665384172",
      "type": "search",
      "config": {
        "displayMode": "input",
        "backgroundColor": "#ffffff",
        "borderRadius": 20,
        "showBorder": true,
        "borderColor": "#e5e7eb",
        "outerMargin": 0,
        "innerPadding": 0,
        "placeholder": "请输入搜索关键词",
        "searchType": "button",
        "showButton": true,
        "buttonText": "搜索",
        "iconPosition": "left"
      },
      "style": {
        "padding": {
          "top": 0,
          "right": 0,
          "bottom": 0,
          "left": 0
        },
        "margin": {
          "top": 0,
          "right": 0,
          "bottom": 0,
          "left": 0
        },
        "borderRadius": 0
      },
      "background": {
        "type": "solid",
        "color": "transparent"
      },
      "sort": 0
    },
    {
      "id": "notice-1752665862275",
      "type": "notice",
      "config": {
        "displayMode": "scroll",
        "backgroundColor": "#fff7e6",
        "borderRadius": 4,
        "showBorder": false,
        "borderColor": "#e5e7eb",
        "outerMargin": 0,
        "innerPadding": 12,
        "content": "这是一条公告信息",
        "textColor": "#fa8c16",
        "fontSize": 14,
        "height": 32,
        "scrollable": true,
        "scrollSpeed": 50,
        "showIcon": true,
        "iconColor": "#fa8c16",
        "iconType": "bell",
        "padding": 12,
        "margin": 0,
        "showCloseButton": false,
        "clickable": false,
        "clickType": "none",
        "url": "",
        "message": ""
      },
      "style": {
        "padding": {
          "top": 0,
          "right": 0,
          "bottom": 0,
          "left": 0
        },
        "margin": {
          "top": 0,
          "right": 0,
          "bottom": 0,
          "left": 0
        },
        "borderRadius": 0
      },
      "background": {
        "type": "solid",
        "color": "transparent"
      },
      "sort": 1
    },
    {
      "id": "image-1752665740585",
      "type": "image",
      "config": {
        "displayMode": "single",
        "backgroundColor": "#ffffff",
        "borderRadius": 8,
        "showBorder": false,
        "borderColor": "#e5e7eb",
        "outerMargin": 0,
        "innerPadding": 0,
        "url": "https://m.360buyimg.com/seckillcms/s150x150_jfs/t20280409/276203/1/18347/52095/67f788dcF34d4e028/f5f4b1484638c602.jpg",
        "width": 300,
        "height": 200,
        "link": "",
        "alt": "示例图片"
      },
      "style": {
        "padding": {
          "top": 0,
          "right": 0,
          "bottom": 0,
          "left": 0
        },
        "margin": {
          "top": 0,
          "right": 0,
          "bottom": 0,
          "left": 0
        },
        "borderRadius": 0
      },
      "background": {
        "type": "solid",
        "color": "transparent"
      },
      "sort": 2
    },
    {
      "id": "carousel-1752665406084",
      "type": "carousel",
      "config": {
        "displayMode": "banner",
        "backgroundColor": "#ffffff",
        "borderRadius": 8,
        "showBorder": false,
        "borderColor": "#e5e7eb",
        "outerMargin": 0,
        "innerPadding": 0,
        "images": [
          {
            "url": "https://m.360buyimg.com/seckillcms/s150x150_jfs/t20271109/200546/26/48008/8292/672f4620F373f786d/1c2d071497af6709.jpg",
            "link": "",
            "title": "轮播图1"
          },
          {
            "id": "md5vy3pc99kckea1rt8",
            "url": "https://m.360buyimg.com/seckillcms/s150x150_jfs/t20271109/200546/26/48008/8292/672f4620F373f786d/1c2d071497af6709.jpg",
            "title": "",
            "link": ""
          }
        ],
        "height": 200,
        "interval": 3500,
        "autoplay": true,
        "showIndicators": true,
        "showArrows": false
      },
      "style": {
        "padding": {
          "top": 0,
          "right": 0,
          "bottom": 0,
          "left": 0
        },
        "margin": {
          "top": 0,
          "right": 0,
          "bottom": 0,
          "left": 0
        },
        "borderRadius": 0
      },
      "background": {
        "type": "solid",
        "color": "transparent"
      },
      "sort": 3
    },
    {
      "id": "grid-menu-1752665796131",
      "type": "grid-menu",
      "config": {
        "displayMode": "grid",
        "backgroundColor": "#ffffff",
        "borderRadius": 8,
        "showBorder": false,
        "borderColor": "#e5e7eb",
        "outerMargin": 0,
        "innerPadding": 0,
        "columns": 3,
        "fontSize": 12,
        "itemPadding": 8,
        "itemMargin": 4,
        "itemBorderRadius": 4,
        "itemBackgroundColor": "#f8f9fa",
        "textColor": "#333333",
        "items": [
          {
            "id": "1",
            "name": "菜单1",
            "icon": "home",
            "link": "",
            "image": "https://m.360buyimg.com/seckillcms/s150x150_jfs/t20271109/200546/26/48008/8292/672f4620F373f786d/1c2d071497af6709.jpg"
          },
          {
            "id": "md5vyscbimxgh4huk3",
            "name": "新菜单",
            "image": "https://m.360buyimg.com/seckillcms/s150x150_jfs/t20271109/200546/26/48008/8292/672f4620F373f786d/1c2d071497af6709.jpg",
            "clickType": "navigate",
            "url": "/",
            "sort": null
          },
          {
            "id": "md5vyufa9w0c3vbpddv",
            "name": "新菜单",
            "image": "https://m.360buyimg.com/seckillcms/s150x150_jfs/t20271109/200546/26/48008/8292/672f4620F373f786d/1c2d071497af6709.jpg",
            "clickType": "navigate",
            "url": "/",
            "sort": null
          },
          {
            "id": "md5vyv8ovteghp09l1g",
            "name": "新菜单",
            "image": "https://m.360buyimg.com/seckillcms/s150x150_jfs/t20271109/200546/26/48008/8292/672f4620F373f786d/1c2d071497af6709.jpg",
            "clickType": "navigate",
            "url": "/",
            "sort": null
          },
          {
            "id": "md5vyvqu6u4uapzesbx",
            "name": "新菜单",
            "image": "https://m.360buyimg.com/seckillcms/s150x150_jfs/t20271109/200546/26/48008/8292/672f4620F373f786d/1c2d071497af6709.jpg",
            "clickType": "navigate",
            "url": "/",
            "sort": null
          },
          {
            "id": "md5vyw68cv6m5mo016e",
            "name": "新菜单",
            "image": "https://m.360buyimg.com/seckillcms/s150x150_jfs/t20271109/200546/26/48008/8292/672f4620F373f786d/1c2d071497af6709.jpg",
            "clickType": "navigate",
            "url": "/",
            "sort": null
          }
        ]
      },
      "style": {
        "padding": {
          "top": 0,
          "right": 0,
          "bottom": 0,
          "left": 0
        },
        "margin": {
          "top": 0,
          "right": 0,
          "bottom": 0,
          "left": 0
        },
        "borderRadius": 0
      },
      "background": {
        "type": "solid",
        "color": "transparent"
      },
      "sort": 4
    },
    {
      "id": "product-1752665783660",
      "type": "product",
      "config": {
        "displayMode": "grid",
        "backgroundColor": "#ffffff",
        "borderRadius": 8,
        "showBorder": false,
        "borderColor": "#e5e7eb",
        "outerMargin": 0,
        "innerPadding": 0,
        "layout": "grid",
        "columns": 2,
        "showPrice": true,
        "showTitle": true,
        "showRating": true,
        "products": [
          {
            "id": "1",
            "title": "示例商品",
            "image": "https://m.360buyimg.com/seckillcms/s150x150_jfs/t20280409/276203/1/18347/52095/67f788dcF34d4e028/f5f4b1484638c602.jpg",
            "price": 99.99,
            "originalPrice": 199.99,
            "rating": 4.5,
            "link": ""
          },
          {
            "id": "md5vyf9olldjd1w4rcq",
            "title": "新商品",
            "price": 99.99,
            "originalPrice": 129.99,
            "image": "https://m.360buyimg.com/seckillcms/s150x150_jfs/t1/149444/5/43456/63752/66d6744aFcf132eca/885f81c729aeb1ca.jpg",
            "rating": 4.5,
            "link": ""
          },
          {
            "id": "md5vyhqu3p97qhl2jad",
            "title": "新商品",
            "price": 99.99,
            "originalPrice": 129.99,
            "image": "https://m.360buyimg.com/seckillcms/s150x150_jfs/t1/149444/5/43456/63752/66d6744aFcf132eca/885f81c729aeb1ca.jpg",
            "rating": 4.5,
            "link": ""
          },
          {
            "id": "md5vyifac0i2rntu0ot",
            "title": "新商品",
            "price": 99.99,
            "originalPrice": 129.99,
            "image": "https://m.360buyimg.com/seckillcms/s150x150_jfs/t1/149444/5/43456/63752/66d6744aFcf132eca/885f81c729aeb1ca.jpg",
            "rating": 4.5,
            "link": ""
          }
        ]
      },
      "style": {
        "padding": {
          "top": 0,
          "right": 0,
          "bottom": 0,
          "left": 0
        },
        "margin": {
          "top": 0,
          "right": 0,
          "bottom": 0,
          "left": 0
        },
        "borderRadius": 0
      },
      "background": {
        "type": "solid",
        "color": "transparent"
      },
      "sort": 5
    },
    {
      "id": "divider-1752665843314",
      "type": "divider",
      "config": {
        "displayMode": "line",
        "backgroundColor": "#ffffff",
        "borderRadius": 0,
        "showBorder": false,
        "borderColor": "#e5e7eb",
        "outerMargin": 16,
        "innerPadding": 0,
        "type": "solid",
        "color": "#e5e7eb",
        "thickness": 1,
        "margin": 16,
        "width": 100,
        "alignment": "center",
        "showText": true,
        "text": "查询更多",
        "textColor": "#666666",
        "textSize": 12,
        "textPosition": "center"
      },
      "style": {
        "padding": {
          "top": 0,
          "right": 0,
          "bottom": 0,
          "left": 0
        },
        "margin": {
          "top": 0,
          "right": 0,
          "bottom": 0,
          "left": 0
        },
        "borderRadius": 0
      },
      "background": {
        "type": "solid",
        "color": "transparent"
      },
      "sort": 6
    },
    {
      "id": "article-1752665882045",
      "type": "article",
      "config": {
        "displayMode": "list",
        "backgroundColor": "#ffffff",
        "borderRadius": 8,
        "showBorder": false,
        "borderColor": "#e5e7eb",
        "outerMargin": 0,
        "innerPadding": 0,
        "articles": [
          {
            "id": "1",
            "title": "示例文章标题",
            "summary": "这是文章摘要内容...",
            "coverImage": "https://m.360buyimg.com/seckillcms/s150x150_jfs/t20271109/200546/26/48008/8292/672f4620F373f786d/1c2d071497af6709.jpg",
            "author": "作者",
            "publishTime": "2024-01-01",
            "readCount": 100,
            "category": "科技",
            "tags": ["前端", "Vue"],
            "link": "",
            "tagsString": "前端, Vue"
          },
          {
            "id": "1752665907910",
            "title": "新文章标题",
            "summary": "这里是文章摘要，简要介绍文章内容...",
            "coverImage": "https://m.360buyimg.com/seckillcms/s150x150_jfs/t20271109/200546/26/48008/8292/672f4620F373f786d/1c2d071497af6709.jpg",
            "publishTime": "2025-07-16",
            "readCount": 0,
            "author": "作者",
            "category": "分类",
            "tags": [],
            "tagsString": ""
          }
        ],
        "showCover": true,
        "showAuthor": true,
        "showSummary": true,
        "showTime": true,
        "showCategory": true,
        "showReadCount": true,
        "showTags": true,
        "articleCount": 2
      },
      "style": {
        "padding": {
          "top": 0,
          "right": 0,
          "bottom": 0,
          "left": 0
        },
        "margin": {
          "top": 0,
          "right": 0,
          "bottom": 0,
          "left": 0
        },
        "borderRadius": 0
      },
      "background": {
        "type": "solid",
        "color": "transparent"
      },
      "sort": 7
    },
    {
      "id": "coupon-1752671917893",
      "type": "coupon",
      "config": {
        "displayMode": "horizontal",
        "backgroundColor": "#ffffff",
        "borderRadius": 8,
        "showBorder": false,
        "borderColor": "#e5e7eb",
        "outerMargin": 0,
        "innerPadding": 0,
        "couponCount": 2,
        "maxVisibleCount": 2,
        "spacing": 10,
        "primaryColor": "#ff4d4f",
        "textColor": "#333333",
        "height": 80,
        "margin": 16,
        "clickable": true,
        "clickType": "navigate",
        "url": "",
        "message": "",
        "coupons": [
          {
            "id": "1",
            "title": "满100减10",
            "subtitle": "新用户专享",
            "discount": "10",
            "discountType": "amount",
            "startTime": "2024-01-01",
            "endTime": "2024-12-31"
          },
          {
            "id": "1752671923326",
            "title": "新优惠券",
            "subtitle": "",
            "discount": "10",
            "discountType": "amount",
            "startTime": "2024-01-01",
            "endTime": "2024-12-31"
          }
        ]
      },
      "style": {
        "padding": {
          "top": 0,
          "right": 0,
          "bottom": 0,
          "left": 0
        },
        "margin": {
          "top": 0,
          "right": 0,
          "bottom": 0,
          "left": 0
        },
        "borderRadius": 0
      },
      "background": {
        "type": "solid",
        "color": "transparent"
      },
      "sort": 8
    },
    {
      "id": "seckill-1752671976093",
      "type": "seckill",
      "config": {
        "displayMode": "horizontal",
        "backgroundColor": "#ff4d4f",
        "borderRadius": 8,
        "showBorder": false,
        "borderColor": "#e5e7eb",
        "outerMargin": 0,
        "innerPadding": 16,
        "layout": "horizontal",
        "columns": 2,
        "primaryColor": "#ff4d4f",
        "secondaryColor": "#ffffff",
        "headerAlign": "center",
        "badgeText": "限时秒杀",
        "title": "秒杀活动",
        "titleSize": 18,
        "titleColor": "#ffffff",
        "description": "限时抢购，手慢无",
        "descriptionSize": 14,
        "descriptionColor": "#ffffff",
        "showCountdown": true,
        "endTime": "2024-12-31 23:59:59",
        "products": [
          {
            "id": "1",
            "name": "秒杀商品",
            "image": "https://m.360buyimg.com/seckillcms/s150x150_jfs/t20280409/276203/1/18347/52095/67f788dcF34d4e028/f5f4b1484638c602.jpg",
            "price": 199.99,
            "originalPrice": 299.99,
            "seckillPrice": 99.99,
            "stock": 100,
            "sold": 50,
            "status": "active"
          }
        ],
        "scrollHeight": 300
      },
      "style": {
        "padding": {
          "top": 0,
          "right": 0,
          "bottom": 0,
          "left": 0
        },
        "margin": {
          "top": 0,
          "right": 0,
          "bottom": 0,
          "left": 0
        },
        "borderRadius": 0
      },
      "background": {
        "type": "solid",
        "color": "transparent"
      },
      "sort": 9
    },
    {
      "id": "group-buy-1752671988344",
      "type": "group-buy",
      "config": {
        "displayMode": "normal",
        "backgroundColor": "#ffffff",
        "borderRadius": 12,
        "showBorder": false,
        "borderColor": "#e5e7eb",
        "outerMargin": 0,
        "innerPadding": 1,
        "layout": "vertical",
        "columns": 2,
        "primaryColor": "#ff6b35",
        "secondaryColor": "#f7931e",
        "headerAlign": "center",
        "badgeText": "团购优惠",
        "title": "团购专区",
        "titleSize": 20,
        "titleColor": "#333333",
        "description": "邀请好友一起团购，享受更多优惠！",
        "descriptionSize": 14,
        "descriptionColor": "#666666",
        "showRules": true,
        "minPeople": 2,
        "timeLimit": 24,
        "products": [
          {
            "id": "1",
            "name": "团购商品示例",
            "image": "https://m.360buyimg.com/seckillcms/s150x150_jfs/t20280409/276203/1/18347/52095/67f788dcF34d4e028/f5f4b1484638c602.jpg",
            "specs": "规格：红色 L码",
            "originalPrice": 199.99,
            "groupPrice": 99.99,
            "status": "active",
            "groupTag": "热门",
            "showProgress": true,
            "currentPeople": 1,
            "targetPeople": 3
          },
          {
            "id": "1752671995239",
            "name": "新商品",
            "image": "https://m.360buyimg.com/seckillcms/s150x150_jfs/t20280409/276203/1/18347/52095/67f788dcF34d4e028/f5f4b1484638c602.jpg",
            "specs": "默认规格",
            "originalPrice": 100,
            "groupPrice": 80,
            "status": "active",
            "groupTag": "热门",
            "showProgress": true,
            "currentPeople": 1,
            "targetPeople": 2
          }
        ],
        "scrollHeight": 400
      },
      "style": {
        "padding": {
          "top": 0,
          "right": 0,
          "bottom": 0,
          "left": 0
        },
        "margin": {
          "top": 0,
          "right": 0,
          "bottom": 0,
          "left": 0
        },
        "borderRadius": 0
      },
      "background": {
        "type": "solid",
        "color": "transparent"
      },
      "sort": 10
    },
    {
      "id": "chain-buy-1752672055188",
      "type": "chain-buy",
      "config": {
        "displayMode": "vertical",
        "backgroundColor": "#722ed1",
        "borderRadius": 8,
        "showBorder": false,
        "borderColor": "#e5e7eb",
        "outerMargin": 0,
        "innerPadding": 16,
        "layout": "vertical",
        "columns": 1,
        "primaryColor": "#722ed1",
        "secondaryColor": "#ffffff",
        "headerAlign": "center",
        "badgeText": "接龙活动",
        "title": "商品接龙",
        "titleSize": 18,
        "titleColor": "#ffffff",
        "description": "跟团接龙，优惠多多",
        "descriptionSize": 14,
        "descriptionColor": "#ffffff",
        "showStats": true,
        "endTime": "2024-12-31 23:59:59",
        "products": [
          {
            "id": "1752672060754",
            "name": "新商品",
            "image": "https://m.360buyimg.com/seckillcms/s150x150_jfs/t20280409/276203/1/18347/52095/67f788dcF34d4e028/f5f4b1484638c602.jpg",
            "specs": "默认规格",
            "originalPrice": 100,
            "chainPrice": 80,
            "status": "active",
            "chainTag": "热门",
            "participants": []
          },
          {
            "id": "1752672063541",
            "name": "新商品",
            "image": "https://m.360buyimg.com/seckillcms/s150x150_jfs/t20280409/276203/1/18347/52095/67f788dcF34d4e028/f5f4b1484638c602.jpg",
            "specs": "默认规格",
            "originalPrice": 100,
            "chainPrice": 80,
            "status": "active",
            "chainTag": "热门",
            "participants": []
          }
        ],
        "scrollHeight": 300,
        "showParticipants": true,
        "participantsHeight": 100
      },
      "style": {
        "padding": {
          "top": 0,
          "right": 0,
          "bottom": 0,
          "left": 0
        },
        "margin": {
          "top": 0,
          "right": 0,
          "bottom": 0,
          "left": 0
        },
        "borderRadius": 0
      },
      "background": {
        "type": "solid",
        "color": "transparent"
      },
      "sort": 11
    },
    {
      "id": "service-product-1752672094429",
      "type": "service-product",
      "config": {
        "displayMode": "list",
        "backgroundColor": "#13c2c2",
        "borderRadius": 8,
        "showBorder": false,
        "borderColor": "#e5e7eb",
        "outerMargin": 0,
        "innerPadding": 16,
        "layout": "vertical",
        "columns": 1,
        "primaryColor": "#13c2c2",
        "secondaryColor": "#ffffff",
        "headerAlign": "center",
        "badgeText": "服务预约",
        "title": "服务商品",
        "titleSize": 18,
        "titleColor": "#ffffff",
        "description": "专业服务，预约优先",
        "descriptionSize": 14,
        "descriptionColor": "#ffffff",
        "showFeatures": true,
        "products": [
          {
            "id": "1752672100083",
            "name": "新服务商品",
            "image": "https://m.360buyimg.com/seckillcms/s150x150_jfs/t20280409/276203/1/18347/52095/67f788dcF34d4e028/f5f4b1484638c602.jpg",
            "specs": "服务规格说明",
            "price": 99,
            "originalPrice": 129,
            "sales": 100,
            "rating": 4.8,
            "status": "available",
            "serviceType": "virtual",
            "serviceTag": "热门"
          }
        ],
        "scrollHeight": 300
      },
      "style": {
        "padding": {
          "top": 0,
          "right": 0,
          "bottom": 0,
          "left": 0
        },
        "margin": {
          "top": 0,
          "right": 0,
          "bottom": 0,
          "left": 0
        },
        "borderRadius": 0
      },
      "background": {
        "type": "solid",
        "color": "transparent"
      },
      "sort": 12
    }
  ],
  "metadata": {
    "version": "1.0.0",
    "timestamp": 1752674942210,
    "exportedAt": "2025-07-16T14:09:02.210Z"
  }
}
