<template>
  <view class="dynamic-demo-page">
    <!-- 动态页面组件 -->
    <dynamic-page 
      :pageConfig="pageConfig"
      @component-click="handleComponentClick"
      @component-error="handleComponentError"
      @page-ready="handlePageReady"
    />
    
    <!-- 调试信息 -->
    <view v-if="showDebug" class="debug-panel">
      <view class="debug-header">
        <text class="debug-title">调试信息</text>
        <uv-button 
          size="mini" 
          type="primary" 
          @click="toggleDebug"
          text="关闭"
        />
      </view>
      <view class="debug-content">
        <text class="debug-text">组件数量: {{ pageConfig.components.length }}</text>
        <text class="debug-text">预览模式: {{ pageConfig.previewMode }}</text>
        <text class="debug-text">最后更新: {{ formatTimestamp(pageConfig.timestamp) }}</text>
      </view>
    </view>
    
    <!-- 调试按钮 -->
    <view class="debug-toggle" @click="toggleDebug">
      <uv-icon name="bug" color="#666" size="20" />
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { PageConfig, ComponentConfig } from '../../types/dynamic-page';
import DynamicPage from '../../components/dynamic-page/dynamic-page.vue';

// 调试模式
const showDebug = ref(false);

// 页面配置数据（基于您提供的JSON，扩展了更多组件示例）
const pageConfig = reactive<PageConfig>({
  components: [
    {
      id: "notice-demo",
      type: "notice",
      config: {
        content: "🎉 欢迎使用动态页面系统！支持多种组件类型的灵活配置",
        backgroundColor: "#fef3c7",
        textColor: "#92400e",
        fontSize: 14,
        height: 40,
        borderRadius: 6,
        showIcon: true,
        iconType: "bell",
        iconColor: "#f59e0b",
        scrollable: true,
        scrollSpeed: 50,
        showCloseButton: false,
        clickable: false,
        clickType: "navigate",
        url: "",
        message: "",
        margin: 10,
        padding: 12
      },
      style: {
        padding: { top: 0, right: 16, bottom: 0, left: 16 },
        margin: { top: 8, right: 0, bottom: 8, left: 0 },
        borderRadius: { topLeft: 8, topRight: 8, bottomLeft: 8, bottomRight: 8 }
      },
      sort: 0
    },
    {
      id: "banner-demo",
      type: "banner",
      config: {
        images: [
          {
            url: "https://m.360buyimg.com/seckillcms/s750x300_jfs/t1/282953/11/19481/57627/67fe4e1dF30e3c5a9/0f21b8c076b10ae2.jpg",
            link: "/pages/quote/index",
            alt: "轮播图1"
          },
          {
            url: "https://m.360buyimg.com/seckillcms/s750x300_jfs/t1/282953/11/19481/57627/67fe4e1dF30e3c5a9/0f21b8c076b10ae2.jpg",
            link: "/pages/history/index",
            alt: "轮播图2"
          }
        ],
        height: 180,
        borderRadius: 8,
        autoplay: true,
        interval: 3000,
        indicatorDots: true,
        indicatorColor: "rgba(255,255,255,0.5)",
        indicatorActiveColor: "#00b678"
      },
      style: {
        padding: { top: 0, right: 16, bottom: 0, left: 16 },
        margin: { top: 0, right: 0, bottom: 16, left: 0 },
        borderRadius: { topLeft: 0, topRight: 0, bottomLeft: 0, bottomRight: 0 }
      },
      sort: 1
    },
    {
      id: "grid-menu-demo",
      type: "grid-menu",
      config: {
        columns: 4,
        items: [
          {
            id: "menu-1",
            title: "报价",
            icon: "file-text",
            iconColor: "#00b678",
            link: "/pages/quote/index"
          },
          {
            id: "menu-2",
            title: "历史",
            icon: "clock",
            iconColor: "#1890ff",
            link: "/pages/history/index"
          },
          {
            id: "menu-3",
            title: "我的",
            icon: "account",
            iconColor: "#722ed1",
            link: "/pages/me/index"
          },
          {
            id: "menu-4",
            title: "设置",
            icon: "setting",
            iconColor: "#fa8c16",
            link: "/pages/me/index",
            badge: "新",
            badgeColor: "#ff4d4f"
          }
        ],
        backgroundColor: "#ffffff",
        itemHeight: 80,
        showBorder: true,
        borderColor: "#f0f0f0"
      },
      style: {
        padding: { top: 0, right: 16, bottom: 0, left: 16 },
        margin: { top: 0, right: 0, bottom: 16, left: 0 },
        borderRadius: { topLeft: 0, topRight: 0, bottomLeft: 0, bottomRight: 0 }
      },
      sort: 2
    }
  ],
  previewMode: "mobile",
  timestamp: Date.now()
});

// 处理组件点击事件
const handleComponentClick = (data: { component: ComponentConfig; action: string; payload?: any }) => {
  console.log('页面接收到组件点击事件:', data);
  
  // 这里可以添加全局的点击处理逻辑
  // 比如埋点统计、用户行为分析等
  
  uni.showToast({
    title: `点击了${data.component?.type}组件`,
    icon: 'none',
    duration: 1500,
  });
};

// 处理组件错误
const handleComponentError = (data: { component: ComponentConfig; error: Error }) => {
  console.error('页面接收到组件错误:', data);
  
  // 这里可以添加错误上报逻辑
  // 比如发送错误日志到服务器
  
  uni.showModal({
    title: '组件错误',
    content: `组件 ${data.component?.type} 发生错误: ${data.error.message}`,
    showCancel: false,
  });
};

// 处理页面准备就绪
const handlePageReady = () => {
  console.log('动态页面准备就绪');
  
  // 这里可以添加页面加载完成后的逻辑
  // 比如发送页面访问统计
};

// 切换调试模式
const toggleDebug = () => {
  showDebug.value = !showDebug.value;
};

// 格式化时间戳
const formatTimestamp = (timestamp: number): string => {
  return new Date(timestamp).toLocaleString();
};

// 页面生命周期
import { onLoad, onShow, onHide } from '@dcloudio/uni-app';

onLoad((options) => {
  console.log('动态页面加载', options);
  
  // 如果有传入的配置参数，可以在这里处理
  if (options.config) {
    try {
      const config = JSON.parse(decodeURIComponent(options.config));
      Object.assign(pageConfig, config);
    } catch (error) {
      console.error('解析页面配置失败:', error);
    }
  }
});

onShow(() => {
  console.log('动态页面显示');
});

onHide(() => {
  console.log('动态页面隐藏');
});
</script>

<style lang="scss" scoped>
.dynamic-demo-page {
  position: relative;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.debug-panel {
  position: fixed;
  bottom: 60px;
  left: 16px;
  right: 16px;
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  padding: 16px;
  z-index: 1000;
}

.debug-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.debug-title {
  color: white;
  font-size: 16px;
  font-weight: 600;
}

.debug-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.debug-text {
  color: #ccc;
  font-size: 14px;
  line-height: 1.4;
}

.debug-toggle {
  position: fixed;
  bottom: 16px;
  right: 16px;
  width: 48px;
  height: 48px;
  background-color: white;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 999;
  
  &:active {
    transform: scale(0.95);
  }
}
</style>
