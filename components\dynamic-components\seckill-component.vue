<template>
  <view class="seckill-component" :style="containerStyle">
    <view class="seckill-wrapper" :style="wrapperStyle">
      <!-- 秒杀头部 -->
      <view class="seckill-header">
        <view class="header-left">
          <view class="seckill-icon">
            <uv-icon name="flash" color="#ff4757" size="20" />
          </view>
          <text class="seckill-title">{{ config.title || '限时秒杀' }}</text>
          
          <!-- 倒计时 -->
          <view v-if="config.showCountdown" class="countdown-wrapper">
            <uv-count-down
              :time="remainingTime"
              format="HH:mm:ss"
              :auto-start="true"
              :millisecond="false"
              @finish="handleCountdownFinish"
            >
              <template #default="{ hours, minutes, seconds }">
                <view class="countdown-container">
                  <text class="countdown-label">距结束</text>
                  <view class="countdown-time">
                    <text class="time-unit">{{ hours }}</text>
                    <text class="time-separator">:</text>
                    <text class="time-unit">{{ minutes }}</text>
                    <text class="time-separator">:</text>
                    <text class="time-unit">{{ seconds }}</text>
                  </view>
                </view>
              </template>
            </uv-count-down>
          </view>
        </view>
        
        <view class="header-right">
          <uv-button
            text="更多"
            type="info"
            size="mini"
            plain
            @click="handleViewMore"
          />
        </view>
      </view>
      
      <!-- 秒杀商品列表 -->
      <scroll-view scroll-x :show-scrollbar="false" class="products-scroll">
        <view class="products-list">
          <view
            v-for="(product, index) in config.products"
            :key="product.id"
            class="product-item"
            :style="productItemStyle"
            @click="handleProductClick(product, index)"
          >
            <!-- 商品图片 -->
            <view class="product-image-wrapper">
              <uv-image
                :src="product.image"
                :width="'100%'"
                :height="'100%'"
                :border-radius="4"
                mode="aspectFill"
                class="product-image"
              />
              
              <!-- 秒杀标签 -->
              <view class="seckill-badge">
                <text class="badge-text">秒杀</text>
              </view>
            </view>
            
            <!-- 商品信息 -->
            <view class="product-info">
              <text class="product-title">{{ product.title }}</text>
              
              <!-- 价格信息 -->
              <view class="price-container">
                <text class="seckill-price">¥{{ product.seckillPrice }}</text>
                <text class="original-price">¥{{ product.originalPrice }}</text>
              </view>
              
              <!-- 进度条 -->
              <view class="progress-container">
                <view class="progress-info">
                  <text class="progress-text">已抢{{ product.soldCount }}件</text>
                  <text class="stock-text">剩{{ product.stock }}件</text>
                </view>
                <uv-line-progress
                  :percentage="calculateProgress(product.soldCount, product.stock)"
                  :active-color="config.progressColor || '#ff4757'"
                  :inactive-color="'#f0f0f0'"
                  :height="4"
                  :show-text="false"
                />
              </view>
              
              <!-- 抢购按钮 -->
              <uv-button
                text="立即抢购"
                :color="config.buttonColor || '#ff4757'"
                size="mini"
                :custom-style="buyButtonStyle"
                @click.stop="handleBuyNow(product)"
              />
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { convertStyleToCSS } from '../../utils/style-helper';
import { theme } from '../../config/theme';

interface SeckillProduct {
  id: string;
  title: string;
  image: string;
  seckillPrice: number;
  originalPrice: number;
  soldCount: number;
  stock: number;
  link: string;
}

interface SeckillConfig {
  title: string;
  endTime: string;
  showCountdown: boolean;
  products: SeckillProduct[];
  backgroundColor: string;
  progressColor: string;
  buttonColor: string;
  spacing: number;
  margin: number;
}

interface Props {
  config: SeckillConfig;
  style?: any;
}

const props = defineProps<Props>();

// 剩余时间（毫秒）
const remainingTime = ref(0);
let timer: any = null;

// 容器样式
const containerStyle = computed(() => {
  if (!props.style) {
    return { width: '100%' };
  }
  
  const baseStyle = convertStyleToCSS(props.style);
  
  const processedStyle = {};
  Object.keys(baseStyle).forEach(key => {
    processedStyle[key] = String(baseStyle[key]);
  });
  
  return {
    ...processedStyle,
    width: '100%',
  };
});

// 包装器样式
const wrapperStyle = computed(() => {
  return {
    backgroundColor: props.config.backgroundColor || '#ffffff',
    borderRadius: '12px',
    padding: `${props.config.margin || theme.spacing.md}px`,
    margin: `${theme.spacing.sm}px`,
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
  };
});

// 商品项样式
const productItemStyle = computed(() => {
  return {
    width: '120px',
    marginRight: `${props.config.spacing || theme.spacing.sm}px`,
    backgroundColor: '#ffffff',
    borderRadius: '8px',
    border: '1px solid #f0f0f0',
    overflow: 'hidden',
  };
});

// 购买按钮样式
const buyButtonStyle = computed(() => {
  return {
    backgroundColor: props.config.buttonColor || '#ff4757',
    color: '#ffffff',
    borderRadius: '4px',
    fontSize: '12px',
    height: '24px',
    marginTop: '8px',
  };
});

// 计算进度百分比
const calculateProgress = (soldCount: number, stock: number) => {
  const total = soldCount + stock;
  return total > 0 ? Math.round((soldCount / total) * 100) : 0;
};

// 计算剩余时间
const calculateRemainingTime = () => {
  if (!props.config.endTime) return 0;
  
  const endTime = new Date(props.config.endTime).getTime();
  const now = Date.now();
  const remaining = endTime - now;
  
  return remaining > 0 ? remaining : 0;
};

// 处理倒计时结束
const handleCountdownFinish = () => {
  uni.showToast({
    title: '秒杀活动已结束',
    icon: 'none'
  });
};

// 处理商品点击
const handleProductClick = (product: SeckillProduct, index: number) => {
  if (product.link) {
    uni.navigateTo({
      url: product.link,
      fail: () => {
        uni.showToast({ title: '页面跳转失败', icon: 'none' });
      }
    });
  }
};

// 处理立即抢购
const handleBuyNow = (product: SeckillProduct) => {
  if (product.stock <= 0) {
    uni.showToast({
      title: '商品已售罄',
      icon: 'none'
    });
    return;
  }
  
  uni.showToast({
    title: `抢购: ${product.title}`,
    icon: 'success'
  });
  
  // 可以在这里添加抢购逻辑
  uni.$emit('seckill-buy', { product });
};

// 处理查看更多
const handleViewMore = () => {
  uni.navigateTo({
    url: '/pages/seckill/list',
    fail: () => {
      uni.showToast({ title: '查看更多秒杀商品', icon: 'none' });
    }
  });
};

// 初始化倒计时
const initCountdown = () => {
  remainingTime.value = calculateRemainingTime();
  
  timer = setInterval(() => {
    remainingTime.value = calculateRemainingTime();
    if (remainingTime.value <= 0) {
      clearInterval(timer);
    }
  }, 1000);
};

onMounted(() => {
  if (props.config.showCountdown) {
    initCountdown();
  }
});

onUnmounted(() => {
  if (timer) {
    clearInterval(timer);
  }
});
</script>

<style lang="scss" scoped>
.seckill-component {
  width: 100%;
}

.seckill-wrapper {
  width: 100%;
}

.seckill-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.header-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.seckill-icon {
  margin-right: 8px;
}

.seckill-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-right: 16px;
}

.countdown-wrapper {
  flex: 1;
}

.countdown-container {
  display: flex;
  align-items: center;
}

.countdown-label {
  font-size: 12px;
  color: #666;
  margin-right: 8px;
}

.countdown-time {
  display: flex;
  align-items: center;
}

.time-unit {
  background-color: #333;
  color: #ffffff;
  padding: 2px 4px;
  border-radius: 2px;
  font-size: 12px;
  font-weight: 600;
  min-width: 20px;
  text-align: center;
}

.time-separator {
  color: #333;
  margin: 0 2px;
  font-weight: 600;
}

.header-right {
  flex-shrink: 0;
}

.products-scroll {
  width: 100%;
  white-space: nowrap;
}

.products-list {
  display: flex;
  flex-direction: row;
  padding-right: 16px;
}

.product-item {
  flex-shrink: 0;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:active {
    transform: scale(0.98);
    opacity: 0.9;
  }
}

.product-image-wrapper {
  position: relative;
  width: 100%;
  height: 80px;
  overflow: hidden;
}

.product-image {
  width: 100%;
  height: 100%;
}

.seckill-badge {
  position: absolute;
  top: 4px;
  left: 4px;
  background-color: #ff4757;
  border-radius: 2px;
  padding: 2px 4px;
}

.badge-text {
  color: #ffffff;
  font-size: 10px;
  font-weight: 600;
}

.product-info {
  padding: 8px;
}

.product-title {
  font-size: 12px;
  color: #333;
  font-weight: 500;
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.3;
}

.price-container {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.seckill-price {
  font-size: 16px;
  color: #ff4757;
  font-weight: 600;
  margin-right: 8px;
}

.original-price {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
}

.progress-container {
  margin-bottom: 8px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.progress-text,
.stock-text {
  font-size: 10px;
  color: #666;
}

// 响应式适配
@media screen and (max-width: 750px) {
  .product-item {
    width: 100px;
  }
  
  .product-image-wrapper {
    height: 70px;
  }
  
  .seckill-title {
    font-size: 16px;
  }
}
</style>
