<template>
    <view class="wsd-voice-input-mask-wrapper" @touchstart.stop="onTouchStart" @touchmove.stop="onTouchMove" @touchend.stop="onTouchEnd">
        <view v-show="modelValue" class="wsd-voice-input-mask" :class="{ 'is-cancel': cancelFlag, 'is-transform': transformFlag }" :style="maskStyle">
            <view class="wsd-voice-input-mask__bg" :class="maskClass"></view>
            <view class="wsd-voice-input-mask__fg">
                <view
                    class="wsd-voice-input-mask__popover"
                    :class="[cancelFlag ? cancelClass : confirmClass, showTriangle && 'wsd-voice-input-mask__popover--triangle']"
                    :style="voiceStyle"
                >
                    <!-- voice input icon -->
                    <slot name="voice">
                        <wsd-voice-input-icon ref="voiceInputIconRef" :weight="speakVolume" v-bind="popoverIcon"></wsd-voice-input-icon>
                    </slot>
                    <view v-show="!!localPopoverTips" class="wsd-voice-input-mask__tip">
                        <text>{{ localPopoverTips }}</text>
                        <text v-if="showCountdown">{{ countdownTips }}</text>
                    </view>
                </view>
                <view class="wsd-voice-input-mask__trigger" :style="triggerStyle">
                    <view v-if="showCancelBtn || showTransferBtn" class="wsd-voice-input-mask__toolbox">
                        <!--取消按钮-->
                        <view
                            v-if="showCancelBtn"
                            class="wsd-voice-input-mask__toolbox-btn wsd-voice-input-mask__toolbox-btn--cancel"
                            :class="[toolboxClass, cancelFlag && 'is-hover', cancelFlag && toolboxActiveClass]"
                        >
                            <view class="wsd-voice-input-mask__toolbox-btn-inner"></view>
                        </view>
                        <view
                            v-if="showTransferBtn"
                            class="wsd-voice-input-mask__toolbox-btn wsd-voice-input-mask__toolbox-btn--transform"
                            :class="[toolboxClass, transformFlag && 'is-hover', transformFlag && toolboxActiveClass]"
                        >
                            <view class="wsd-voice-input-mask__toolbox-btn-inner"></view>
                        </view>
                        <!--翻译文本按钮-->
                    </view>
                    <view class="wsd-voice-input-mask__record-btn" :class="[triggerClass, triggerActive && triggerActiveClass]">
                        <view class="wsd-voice-input-mask__record-btn-inner">
                            <view class="wsd-voice-input-mask__record-btn-icon">
                                <slot name="icon">
                                    <wsd-voice-icon :strokeColor="triggerActive ? triggerActiveIconColor : triggerIconColor" v-bind="triggerIcon"></wsd-voice-icon>
                                </slot>
                            </view>
                        </view>
                    </view>
                    <view v-if="triggerShadow" class="wsd-voice-input-mask__trigger-shadow" :style="triggerShadowStyle"></view>
                </view>
            </view>
        </view>
        <view class="wsd-voice-input-mask__refreence">
            <slot name="reference"></slot>
        </view>
    </view>
</template>

<script setup lang="ts">
import { VoiceInputIconProps } from '../wsd-voice-input-icon/wsd-voice-input-icon.vue';
import { VoiceIconProps } from '../wsd-voice-icon/wsd-voice-icon.vue';
import { computed, getCurrentInstance, nextTick, onMounted, ref, unref, watch } from 'vue';
import type { CSSProperties } from 'vue';
import { useRecorderManager, UseRecorderManagerEmits, UseRecorderManagerProps } from '../../hooks/useRecorderManager';
import { onHide } from '@dcloudio/uni-app';
// import debounce from "../../utils/debounce";

defineOptions({
    name: 'wsd-voice-input-mask'
});

const { windowHeight, windowWidth } = uni.getWindowInfo();
const _instance = getCurrentInstance();

export interface VoiceInputMaskProps extends UseRecorderManagerProps {
    modelValue: boolean;
    zIndex?: number;
    maskColor?: string;
    maskClass?: string;
    triggerSize?: number; // 触发器宽度比(与屏幕宽度) [1, 5]
    triggerRadio?: number; // 触发器宽高比 [1, 10]
    confirmColor?: string;
    cancelColor?: string;
    confirmClass?: string;
    cancelClass?: string;
    showTriangle?: boolean;
    triangleSize?: number;
    triangleRadius?: number;
    tipsColor?: string;
    tipsOffset?: number;
    popoverRadius?: number;
    popoverTips?: string;
    popoverCancelTips?: string;
    popoverTransformTips?: string;
    popoverWidth?: string | number;
    popoverOffset?: number;
    popoverIcon?: Partial<VoiceInputIconProps>;
    toolboxSize?: number;
    toolboxOffset?: number;
    toolboxRotateOffset?: number;
    toolboxClass?: string;
    toolboxActiveClass?: string;
    toolboxColor?: string;
    toolboxActiveColor?: string;
    toolboxTextColor?: string;
    toolboxActiveTextColor?: string;
    toolboxFontSize?: string | number;
    triggerClass?: string;
    triggerActiveClass?: string;
    triggerColor?: string;
    triggerActiveColor?: string;
    triggerIconColor?: string;
    triggerActiveIconColor?: string;
    triggerIcon?: Partial<VoiceIconProps>;
    triggerShadow?: boolean;
    triggerShadowSize?: number;
    triggerShadowColor?: string;
    triggerShadowBlur?: number;
    showCancelBtn?: boolean;
    showTransferBtn?: boolean;
    allowCancelByMove?: boolean;
    cancelMoveDistance?: number;
    moveOffset?: number;
    countdownLimit?: number;
    remote?: boolean; // 默认上传方式
    remoteUrl?: string; // 默认上传请求url
    remoteFilename?: string; // 默认上传文件名称
    remoteParams?: Record<string, any>; // 默认上传其他请求参数
    remoteHeaders?: Record<string, any>; // 默认上传请求头
    remoteMethod?: (tempFilePath: string) => Promise<any>; // 自定义上传方式
    preAuthorize?: boolean;
    delay?: number; // 延时开始录音，避免点击reference
    lackTips?: boolean;
}

export interface VoiceInputMaskEmits extends UseRecorderManagerEmits {
    (e: 'update:modelValue', visible: boolean): void;
    (e: 'record', tempFilePath: string, isTimeout?: boolean): void;
    (e: 'upload', res: VoiceUploadResponse): void;
}

export interface VoiceUploadResponse {
    type: 'success' | 'error' | 'fail';
    filePath: string;
    message?: string;
    result?: any;
}

const props = withDefaults(defineProps<VoiceInputMaskProps>(), {
    zIndex: 99,
    triggerSize: 1.2,
    triggerRadio: 2.4,
    showTriangle: true,
    triangleSize: 16,
    triangleRadius: 4,
    tipsColor: 'rgba(232, 232, 232, 1)',
    tipsOffset: 16,
    popoverTips: '松开发送，手指上滑取消发送',
    popoverCancelTips: '松开取消',
    popoverTransformTips: '转文字',
    popoverRadius: 12,
    popoverOffset: 0,
    popoverWidth: '60vw',
    maskColor: 'rgba(0, 0, 0, .65)',
    confirmColor: 'green',
    cancelColor: 'red',
    toolboxSize: 56,
    toolboxOffset: 24,
    toolboxRotateOffset: 20,
    toolboxClass: '',
    toolboxActiveClass: '',
    toolboxColor: 'rgba(50, 50, 50, 1)',
    toolboxActiveColor: 'rgba(255, 255, 255, 1)',
    toolboxTextColor: '#fff',
    toolboxActiveTextColor: '#000',
    toolboxFontSize: '44rpx',
    triggerClass: '',
    triggerActiveClass: '',
    triggerColor: 'rgba(50, 50, 50, 1)',
    triggerActiveColor: 'rgba(255, 255, 255, 1)',
    triggerIconColor: '#fff',
    triggerActiveIconColor: 'red',
    triggerShadowSize: 32,
    triggerShadowColor: 'rgba(0, 0, 0, 0.15)',
    triggerShadowBlur: 6,
    showTransferBtn: true,
    showCancelBtn: true,
    allowCancelByMove: false,
    cancelMoveDistance: 0,
    moveOffset: 5,
    analysisVolumn: true, // 监测音量
    duration: 60000, // 默认1分钟 ms
    countdown: true, // 倒计时提示
    countdownLimit: 10000, // 倒计时显示阈值 ms - 不设置默认当成一直显示
    popoverIcon: () => {
        const { windowWidth } = uni.getWindowInfo();
        return {
            strokeCount: Math.floor((0.6 * windowWidth - 36) / 4), // (width - padding) / (2 * strokeWidth)
            strokeHeight: 8,
            strokeWidth: 2,
            maxScale: 6,
            height: 48
        };
    },
    triggerIcon: () => ({}),
    remote: false,
    remoteFilename: 'file',
    preAuthorize: true,
    minDuration: 1000,
    delay: 400,
    lackTips: true
});

const emits = defineEmits<VoiceInputMaskEmits>();

const {
    recorderManager,
    // recordingCancel,
    recordingError,
    recording,
    pausing,
    stoping,
    isSpeaking,
    isTimeout,
    isLack,
    speakVolume,
    countdownValue,
    audioPath,
    // transformText,
    // pause,
    // resume,
    // start,
    stop
} = useRecorderManager(props, emits);

let recordPrepareTimeout: ReturnType<typeof setTimeout>;

const recordPrepare = ref(false);
const ignoreTimeout = ref(false);
const cancelFlag = ref(false);
const transformFlag = ref(false);
const cancelBtnRect = ref<UniApp.NodeInfo>(null);
const transformBtnRect = ref<UniApp.NodeInfo>(null);
const triggerBtnRect = ref<UniApp.NodeInfo>(null);
const touchFirst = ref<Touch>(null);
const touchMoving = ref<Touch>(null);
const authorized = ref<boolean>(false);

const voiceInputIconRef = ref(null);

const localPopoverTips = computed<string>(() => {
    if (unref(cancelFlag)) return props.popoverCancelTips;
    if (unref(transformFlag)) return props.popoverTransformTips;
    return props.popoverTips;
});

const triggerActive = computed<boolean>(() => !(unref(cancelFlag) || unref(transformFlag)));

const finalcountdownLimit = computed(() => Math.max(0, props.countdownLimit) / 1000);
const showCountdown = computed(() => props.countdown && !!unref(countdownValue) && (!unref(finalcountdownLimit) || unref(countdownValue) <= unref(finalcountdownLimit)));

const countdownTips = computed<string>(() =>
    unref(showCountdown) ? `(${unref(countdownValue)}s 后自动${unref(cancelFlag) ? '取消' : unref(transformFlag) ? '转文字' : '发送'})` : ''
);

const finalTirggerSize = computed(() => Math.max(1, Math.min(props.triggerSize, 5)));

const finalTirggerRatio = computed(() => Math.max(1, Math.min(props.triggerRadio, 10)));

const triggerWidth = computed<number>(() => {
    return Math.ceil(windowWidth * unref(finalTirggerSize));
});

const triggerHeight = computed<number>(() => {
    return Math.ceil(unref(triggerWidth) / unref(finalTirggerRatio));
});

const toolboxOutsideSize = computed(() => props.toolboxSize + props.toolboxOffset);

// 语音触发按钮的椭圆
const ellipsisA1 = computed<number>(() => unref(triggerWidth) / 2);
const ellipsisB1 = computed<number>(() => unref(triggerHeight) / 2);

// 工具按钮的椭圆
const ellipsisA2 = computed<number>(() => unref(ellipsisA1) + unref(toolboxOutsideSize) / 2);
const ellipsisB2 = computed<number>(() => unref(ellipsisB1) + unref(toolboxOutsideSize) / 2);

const toolboxPositionX = computed<number>(() => {
    return props.showTransferBtn && props.showCancelBtn ? Math.ceil(windowWidth / 4) : Math.ceil(windowWidth / 2);
});

const toolboxPositionY = computed<number>(() => {
    return getEllipisiY(unref(ellipsisA2), unref(ellipsisB2), Math.abs(unref(toolboxPositionX) - Math.ceil(windowWidth / 2)));
});

const toolboxRotateDeg = computed<number>(() => {
    const angle = unref(toolboxPositionY) === unref(ellipsisB2) ? 0 : Math.atan(unref(toolboxPositionX) / unref(toolboxPositionY));
    return Math.max(0, (angle * 180) / Math.PI - props.toolboxRotateOffset);
});

const maskStyle = computed<CSSProperties>(() => {
    return {
        '--voice-input-mask-color': props.maskColor,
        opacity: props.modelValue ? 1 : 0,
        zIndex: props.zIndex
    };
});

const voiceStyle = computed<CSSProperties>(() => {
    return {
        '--voice-popover-offset': `${props.popoverOffset}px`,
        '--voice-popover-radius': `${props.popoverRadius}px`,
        '--voice-popover-color': props.confirmColor,
        '--voice-popover-color--cancel': props.cancelColor,
        '--voice-popover-triangle-size': `${props.triangleSize}px`,
        '--voice-popover-triangle-radius': `${props.triangleRadius}px`,
        '--voice-popover-tips-color': props.tipsColor,
        '--voice-popover-tips-offset': `${props.tipsOffset}px`,
        '--voice-popover-padding': '20rpx 24rpx',
        width: typeof props.popoverWidth === 'number' ? `${props.popoverWidth}px` : props.popoverWidth
    };
});

const triggerStyle = computed<CSSProperties>(() => {
    return {
        '--trigger-width': `${unref(triggerWidth)}px`,
        '--trigger-height': `${unref(triggerHeight)}px`,
        '--trigger-background-color': props.triggerColor,
        '--trigger-background-color--active': props.triggerActiveColor,
        '--trigger-position-bottom': `-${unref(triggerHeight) / 2}px`,
        '--toolbox-width': `${unref(toolboxOutsideSize)}px`,
        '--toolbox-height': `${unref(toolboxOutsideSize)}px`,
        '--toolbox-inner-width': `${props.toolboxSize}px`,
        '--toolbox-inner-height': `${props.toolboxSize}px`,
        '--toolbox-background-color': props.toolboxColor,
        '--toolbox-background-color--active': props.toolboxActiveColor,
        '--toolbox-font-size': typeof props.toolboxFontSize === 'string' ? props.toolboxFontSize : `${props.toolboxFontSize}px`,
        '--toolbox-front-color': props.toolboxTextColor,
        '--toolbox-front-color--active': props.toolboxActiveTextColor,
        '--toolbox-position-bottom': `${unref(toolboxPositionY) - unref(toolboxOutsideSize) / 2}px`,
        '--toolbox-position-left': `${unref(toolboxPositionX) - unref(toolboxOutsideSize) / 2}px`,
        '--toolbox-position-right': `${unref(toolboxPositionX) - unref(toolboxOutsideSize) / 2}px`,
        '--toolbox-rotate-left': `${-1 * unref(toolboxRotateDeg)}deg`,
        '--toolbox-rotate-right': `${unref(toolboxRotateDeg)}deg`
    };
});

const triggerShadowStyle = computed<CSSProperties>(() => {
    return {
        '--trigger-shadow-color': props.triggerShadowColor,
        '--trigger-shadow-blur': `${props.triggerShadowBlur}px`,
        '--trigger-shadow-position-bottom': `-${(unref(triggerHeight) + props.triggerShadowSize) / 2}px`,
        '--trigger-shadow-width': `${unref(triggerWidth) + props.triggerShadowSize}px`,
        '--trigger-shadow-height': `${unref(triggerHeight) + props.triggerShadowSize}px`,
        '--trigger-shadow-background': `radial-gradient(
            closest-side,
            rgba(0, 0, 0, 0) ${(unref(triggerWidth) - 2 * props.triggerShadowSize) / 2}px,
            var(--trigger-shadow-color) ${(unref(triggerWidth) - props.triggerShadowSize) / 2}px,
            rgba(0, 0, 0, 0) 100%
        );`
    };
});

// const updateTouchPositionDebounce = debounce(updateTouchPosition, 10);

watch(recordingError, (val) => {
    if (val) {
        uni.showToast({
            icon: 'error',
            title: '语音输入异常',
            duration: 1000
        });
        closeMask();
    }
});

watch(isTimeout, (val) => {
    if (val && !ignoreTimeout.value && touchFirst.value) {
        // 录制超时
        // 保存语音
        const shouldSend = triggerActive.value; // 提前缓存
        const shouldTransform = transformFlag.value; // 提前缓存
        if (audioPath.value && shouldSend) {
            if (props.remote) {
                if (props.remoteMethod) {
                    props.remoteMethod?.(audioPath.value);
                } else {
                    defaultRemote(audioPath.value);
                }
            }
            emits('record', audioPath.value, true);
        } else if (audioPath.value && shouldTransform) {
            // 翻译文本
        } else {
            // 取消
        }
        closeMask();
    }
});

watch(isLack, (val) => {
    if (val) {
        props.lackTips &&
            uni.showToast({
                icon: 'none',
                title: '录音时长不足1s',
                duration: 1000
            });
    }
});

watch(
    () => [unref(isSpeaking), unref(pausing), unref(stoping), unref(recording)],
    () => {
        if (!unref(recording)) {
            voiceInputIconRef.value?.stopRecording();
        } else {
            if (unref(pausing) || unref(stoping)) {
                voiceInputIconRef.value?.stopRecording();
            } else if (!unref(isSpeaking)) {
                voiceInputIconRef.value?.awaitRecording();
            } else {
                voiceInputIconRef.value?.startRecording();
            }
        }
    }
);

watch(
    () => props.modelValue,
    (val) => {
        if (val) {
            // 开启语音-等待输入
            setTimeout(() => {
                voiceInputIconRef.value?.awaitRecording();
            }, 10);
        } else {
            voiceInputIconRef.value?.stopRecording();
        }
    },
    {
        immediate: true
    }
);

function getEllipisiY(a: number, b: number, x: number): number {
    // a > b > 0;
    if (!a || !b) return 0;
    // ellipsis func = x^2 / a ^ 2 + y ^ 2 / b ^ 2 = 1
    // y ^ 2 = b ^ 2 * (1 - (x ^ 2 / a ^ 2))
    return Math.ceil(Math.sqrt(Math.pow(b, 2) * (1 - Math.pow(x, 2) / Math.pow(a, 2))));
}

function getBtnRect(callback?: (rects: UniApp.NodeInfo[]) => any) {
    const query = uni.createSelectorQuery().in(_instance);
    query.select('.wsd-voice-input-mask__toolbox-btn--cancel').boundingClientRect();
    query.select('.wsd-voice-input-mask__toolbox-btn--transform').boundingClientRect();
    query.select('.wsd-voice-input-mask__record-btn').boundingClientRect();
    query.exec(function (rects: UniApp.NodeInfo[]) {
        cancelBtnRect.value = rects[0];
        transformBtnRect.value = rects[1];
        triggerBtnRect.value = rects[2];
        callback && callback(rects);
    });
}

function updateTouchPosition(position: Touch) {
    touchMoving.value = position;

    const x = position.pageX;
    const y = position.pageY;
    const offset = props.moveOffset;

    // 语音触发按边界
    const ey = getEllipisiY(unref(ellipsisA1), unref(ellipsisB1), Math.abs(x - Math.ceil(windowWidth / 2)));
    const my = windowHeight - y;

    if (my > ey + offset) {
        // 语音触发按边界外
        if (props.showCancelBtn && props.showTransferBtn) {
            cancelFlag.value = x <= windowWidth / 2;
            transformFlag.value = x > windowWidth / 2;
        } else if (props.showCancelBtn) {
            cancelFlag.value = true;
            transformFlag.value = false;
        } else if (props.showTransferBtn) {
            cancelFlag.value = false;
            transformFlag.value = true;
        } else if (props.allowCancelByMove) {
            cancelFlag.value = my - (ey + offset) >= props.cancelMoveDistance;
            transformFlag.value = false;
        } else {
            cancelFlag.value = false;
            transformFlag.value = false;
        }
    } else {
        cancelFlag.value = false;
        transformFlag.value = false;
    }

    // TO: 频繁切换 暂停-继续 会导致录音失效，也为了方便计数
    /* if (recording.value) {
        if (!(cancelFlag.value || transformFlag.value)) {
            // 继续录音
            resume();
        } else {
            // 暂停录音
            pause();
        }
    } */
}

function onTouchStart(e: TouchEvent) {
    // console.log(">>>onTouchStart", e);
    e.preventDefault();
    if (touchFirst.value) return;
    if (e.touches.length > 1) return;

    recordPrepare.value = false;
    ignoreTimeout.value = false;

    recordPrepareTimeout = setTimeout(async () => {
        recordPrepare.value = true;
        const afterTouchStart = () => {
            touchFirst.value = e.touches[0];
            updateTouchPosition(touchFirst.value);
            emits('update:modelValue', true);
        };
        if (!authorized.value) {
            try {
                // 请求录音权限
                const authResult = await uni.authorize({
                    scope: 'scope.record'
                });
                if (authResult.errMsg === 'authorize:ok') {
                    // 授权成功，开始录音操作
                    authorized.value = true;
                    // afterTouchStart();
                } else {
                    authorized.value = false;
                    console.error('授权失败');
                }
                closeMask();
            } catch (error) {
                authorized.value = false;
                if (error.errMsg.startsWith('authorize:fail')) {
                    uni.showToast({
                        icon: 'error',
                        title: '录音未授权！',
                        duration: 1000
                    });
                }
                closeMask();
                console.error('授权异常:', error);
            }
        } else {
            afterTouchStart();
        }
    }, props.delay);
}

function onTouchMove(e: TouchEvent) {
    // console.log(">>>onTouchMove", e);
    e.preventDefault();
    if (!touchFirst.value || !e.changedTouches || !e.changedTouches.length) return;
    const changedTouch = Array.from(e.changedTouches).find((t: Touch) => t.identifier === touchFirst.value.identifier);
    if (!!changedTouch) {
        updateTouchPosition(changedTouch);
    }
}

function onTouchEnd(e: TouchEvent) {
    // console.log(">>>onTouchEnd", e);
    e.preventDefault();
    if (!recordPrepare.value && recordPrepareTimeout) {
        clearTimeout(recordPrepareTimeout);
    } else {
        if (touchFirst.value) {
            if (recording.value) {
                const shouldSend = triggerActive.value; // 提前缓存
                const shouldTransform = transformFlag.value; // 提前缓存
                stop((tempFilePath: string) => {
                    if (tempFilePath && shouldSend) {
                        if (isTimeout.value) {
                            ignoreTimeout.value = true;
                        }
                        // 保存语音
                        console.log('>>>tempFilePath', tempFilePath);
                        if (props.remote) {
                            if (props.remoteMethod) {
                                props.remoteMethod?.(tempFilePath);
                            } else {
                                defaultRemote(tempFilePath);
                            }
                        }

                        emits('record', tempFilePath);
                    } else if (tempFilePath && shouldTransform) {
                        // 翻译文本
                    } else {
                        // 取消
                    }
                });
            }
        }
        closeMask();
    }
}

function closeMask() {
    // try stop
    if (recording.value) {
        stop();
    }
    voiceInputIconRef.value?.stopRecording();
    cancelFlag.value = false;
    transformFlag.value = false;
    touchFirst.value = null;
    recordPrepare.value = false;
    if (props.countdown) {
        countdownValue.value = 0;
    }
    nextTick(() => {
        emits('update:modelValue', false);
    });
}

function defaultRemote(tempFilePath: string): Promise<VoiceUploadResponse> {
    return new Promise((resolve, reject) => {
        if (!props.remoteUrl) {
            const res: VoiceUploadResponse = {
                type: 'error',
                filePath: tempFilePath,
                message: '属性【remoteUrl】不能为空!'
            };
            reject(res);
            emits('upload', res);
            return;
        }
        if (!props.remoteFilename) {
            const res: VoiceUploadResponse = {
                type: 'error',
                filePath: tempFilePath,
                message: '属性【remoteFilename】不能为空!'
            };
            reject(res);
            emits('upload', res);
            return;
        }
        uni.uploadFile({
            url: props.remoteUrl, // 服务器地址
            filePath: tempFilePath,
            name: props.remoteFilename, // 文件对应的 key，服务器端根据此获取文件
            formData: {
                ...(props.remoteParams || {}),
                t: Date.now()
            },
            header: {
                ...(props.remoteHeaders || {}),
                'Content-Type': 'multipart/form-data'
            },
            success: (result) => {
                // console.log("上传成功:", res);
                const res: VoiceUploadResponse = {
                    type: 'success',
                    filePath: tempFilePath,
                    message: 'upload success',
                    result: result
                };
                reject(res);
                emits('upload', res);
            },
            fail: (err) => {
                // console.error("上传失败:", err);
                const res: VoiceUploadResponse = {
                    type: 'fail',
                    filePath: tempFilePath,
                    message: 'upload fail',
                    result: err
                };
                reject(res);
                emits('upload', res);
            }
        });
    });
}

onHide(() => {
    closeMask();
});

onMounted(async () => {
    // 提前授权录音
    if (props.preAuthorize) {
        try {
            // 请求录音权限
            const authResult = await uni.authorize({
                scope: 'scope.record'
            });
            if (authResult.errMsg === 'authorize:ok') {
                // 授权成功，开始录音操作
                authorized.value = true;
            } else {
                authorized.value = false;
                console.error('授权失败');
            }
        } catch (error) {
            authorized.value = false;
            console.error('授权异常:', error);
        }
    }
    nextTick(() => {
        getBtnRect();
    });
});

defineExpose({
    cancelFlag,
    transformFlag,
    closeMask
});
</script>

<style scoped lang="scss">
.wsd-voice-input-mask-wrapper {
    position: relative;
}

.wsd-voice-input-mask__refreence {
    position: relative;
    box-sizing: border-box;
}

.wsd-voice-input-mask {
    position: fixed;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100%;

    transition: opacity 0.2s ease-in;

    &__bg {
        position: absolute;
        z-index: 1;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        background: var(--voice-input-mask-color);
    }

    &__fg {
        position: absolute;
        z-index: 3;
        width: 100%;
        height: 50%;
        left: 0;
        top: 0;
        background-color: transparent;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    &__popover {
        position: relative;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: var(--voice-popover-padding);
        transform: translateY(calc(-1 * var(--voice-popover-offset)));
        background: var(--voice-popover-color);
        border-radius: var(--voice-popover-radius);

        &.wsd-voice-input-mask__popover--triangle {
            &::before {
                position: absolute;
                content: '';
                display: inline-block;
                z-index: 0;
                width: var(--voice-popover-triangle-size);
                height: var(--voice-popover-triangle-size);
                left: calc(50% - calc(var(--voice-popover-triangle-size) / 2));
                bottom: calc(1px - calc(var(--voice-popover-triangle-size) / 2));
                background: var(--voice-popover-color);
                border-radius: var(--voice-popover-triangle-radius);
                transform: rotate(45deg);
            }

            .wsd-voice-input-mask__tip {
                top: calc(100% + calc(var(--voice-popover-tips-offset) + calc(var(--voice-popover-triangle-size) / 2)));
            }
        }
    }

    &__tip {
        position: absolute;
        z-index: 2;
        width: 100%;
        left: 0;
        top: calc(100% + var(--voice-popover-tips-offset));
        display: inline-flex;
        align-items: center;
        justify-content: center;
        flex-wrap: wrap;
        padding: -8rpx -10rpx;

        text {
            display: inline-block;
            text-align: center;
            font-size: 24rpx;
            color: var(--voice-popover-tips-color);
            line-height: 40rpx;
            word-break: break-all;
            margin: 8rpx 10rpx;
        }
    }

    &__trigger {
        position: relative;
        background-color: transparent;
    }

    &__trigger-shadow {
        position: fixed;
        z-index: 1;
        width: var(--trigger-shadow-width);
        height: var(--trigger-shadow-height);
        left: calc(calc(100vw - var(--trigger-shadow-width)) / 2);
        bottom: var(--trigger-shadow-position-bottom);
        background-image: var(--trigger-shadow-background);
        filter: blur(var(--trigger-shadow-blur));
    }

    &__toolbox-btn {
        position: fixed;
        z-index: 2;
        display: flex;
        align-items: center;
        justify-content: center;
        width: var(--toolbox-width);
        height: var(--toolbox-height);
        border-radius: 100%;
        background: transparent;
        color: var(--toolbox-front-color);
        transform-origin: center center;
        box-sizing: border-box;

        &-inner {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            width: var(--toolbox-inner-width);
            height: var(--toolbox-inner-height);
            border-radius: 100%;
            background: transparent;
            transform-origin: center center;
            box-sizing: border-box;
            transition: all 0.15s linear;

            &::before {
                position: relative;
                content: '';
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: var(--toolbox-font-size);
                width: var(--toolbox-inner-width);
                height: var(--toolbox-inner-height);
                background: var(--toolbox-background-color);
                color: var(--toolbox-front-color);
                transition: all 0.15s linear;
                border-radius: 100%;
            }
        }

        &.is-hover {
            .wsd-voice-input-mask__toolbox-btn-inner {
                width: calc(1.15 * var(--toolbox-inner-width));
                height: calc(1.15 * var(--toolbox-inner-height));
                &::before {
                    background: var(--toolbox-background-color--active);
                    color: var(--toolbox-front-color--active);
                    width: calc(1.15 * var(--toolbox-inner-width));
                    height: calc(1.15 * var(--toolbox-inner-height));
                    // transform: scale(1.15); // 影响文字大小
                }
            }
        }
    }

    &__toolbox-btn--cancel {
        bottom: var(--toolbox-position-bottom);
        left: var(--toolbox-position-left);
        transform: rotate(var(--toolbox-rotate-left));
        .wsd-voice-input-mask__toolbox-btn-inner {
            &::before {
                content: 'X';
            }
        }
    }

    &__toolbox-btn--transform {
        bottom: var(--toolbox-position-bottom);
        right: var(--toolbox-position-right);
        transform: rotate(var(--toolbox-rotate-right));
        .wsd-voice-input-mask__toolbox-btn-inner {
            &::before {
                content: '文';
            }
        }
    }

    &__record-btn {
        position: fixed;
        z-index: 2;
        width: var(--trigger-width);
        height: var(--trigger-height);
        left: calc(calc(100vw - var(--trigger-width)) / 2);
        bottom: var(--trigger-position-bottom);
        background: transparent;

        &-inner {
            position: relative;
            z-index: 5;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            clip-path: ellipse(closest-side farthest-side);
            background: var(--trigger-background-color--active);
        }

        &-icon {
            position: relative;
            z-index: 5;
            transform: translateY(calc(-1 * calc(var(--trigger-height) / 4)));
        }
    }

    &.is-cancel {
        .wsd-voice-input-mask__popover {
            background: var(--voice-popover-color--cancel);

            &.wsd-voice-input-mask__popover--triangle {
                &::before {
                    background: var(--voice-popover-color--cancel);
                }
            }
        }
    }

    &.is-transform,
    &.is-cancel {
        .wsd-voice-input-mask__record-btn {
            &-inner {
                background: var(--trigger-background-color);
            }
        }
    }
}
</style>
