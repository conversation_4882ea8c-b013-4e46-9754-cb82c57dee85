import { reactive, onMounted, toRefs } from 'vue';

export const useSystemInfo = () => {
    const state = reactive<{
        systemInfo: any;
        statusBarHeight: number;
        menuButtonHeight: number;
        safeHeight:number
    }>({
        systemInfo: {},
        statusBarHeight: 0,
        menuButtonHeight: 44,
        safeHeight: 0
    });

    onMounted(() => {
        const systemInfo = uni.getSystemInfoSync();
        state.systemInfo = systemInfo;
        const menuButton = uni.getMenuButtonBoundingClientRect();
        state.menuButtonHeight = menuButton.height + (menuButton.top - systemInfo.statusBarHeight) * 2;
        state.statusBarHeight = systemInfo.statusBarHeight + state.menuButtonHeight;
        state.safeHeight= systemInfo.safeArea.height;
    });

    return toRefs(state);
};
