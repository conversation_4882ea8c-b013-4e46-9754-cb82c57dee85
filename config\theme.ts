/**
 * 主题配置文件
 * 统一管理颜色、字体、间距等设计规范
 */

export interface ThemeConfig {
  colors: {
    primary: string;
    secondary: string;
    success: string;
    warning: string;
    error: string;
    info: string;
    text: {
      primary: string;
      secondary: string;
      disabled: string;
      inverse: string;
    };
    background: {
      primary: string;
      secondary: string;
      card: string;
      overlay: string;
    };
    border: {
      light: string;
      medium: string;
      dark: string;
    };
  };
  spacing: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
    xxl: number;
  };
  borderRadius: {
    sm: number;
    md: number;
    lg: number;
    xl: number;
    round: number;
  };
  fontSize: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
    xxl: number;
  };
  fontWeight: {
    light: number;
    normal: number;
    medium: number;
    bold: number;
  };
  shadows: {
    sm: string;
    md: string;
    lg: string;
  };
}

export const theme: ThemeConfig = {
  colors: {
    primary: '#00b678',
    secondary: '#6366f1',
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#3b82f6',
    text: {
      primary: '#1f2937',
      secondary: '#6b7280',
      disabled: '#9ca3af',
      inverse: '#ffffff',
    },
    background: {
      primary: '#ffffff',
      secondary: '#f9fafb',
      card: '#ffffff',
      overlay: 'rgba(0, 0, 0, 0.5)',
    },
    border: {
      light: '#f3f4f6',
      medium: '#e5e7eb',
      dark: '#d1d5db',
    },
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48,
  },
  borderRadius: {
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
    round: 9999,
  },
  fontSize: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
  },
  fontWeight: {
    light: 300,
    normal: 400,
    medium: 500,
    bold: 600,
  },
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
  },
};

/**
 * 获取主题颜色
 */
export const getThemeColor = (colorPath: string): string => {
  const paths = colorPath.split('.');
  let color: any = theme.colors;
  
  for (const path of paths) {
    color = color[path];
    if (!color) break;
  }
  
  return color || theme.colors.primary;
};

/**
 * 获取主题间距
 */
export const getThemeSpacing = (size: keyof ThemeConfig['spacing']): number => {
  return theme.spacing[size] || theme.spacing.md;
};

/**
 * 获取主题字体大小
 */
export const getThemeFontSize = (size: keyof ThemeConfig['fontSize']): number => {
  return theme.fontSize[size] || theme.fontSize.md;
};
