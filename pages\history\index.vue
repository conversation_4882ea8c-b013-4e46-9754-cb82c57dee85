<template>
    <view class="content">
        <view class="record">
            <scroll-view scroll-y class="scroll-view" @scrolltolower="onQuery()" :enable-back-to-top="true">
                <view class="list">
                    <view class="item" v-for="(item, index) in state.quotationHistoryList || []" :key="index">
                        <view class="header">
                            <view class="no">{{ item.quotationBatchNo }}</view>
                            <view class="status complete" v-if="item.parseStatus === TaskParseStatusEnum.PARSED">{{ TaskParseStatusEnumMapDesc[item.parseStatus] }}</view>
                            <view class="status parsing" v-else-if="item.parseStatus === TaskParseStatusEnum.PARSING">{{ TaskParseStatusEnumMapDesc[item.parseStatus] }}</view>
                            <view class="status stop" v-else-if="item.parseStatus === TaskParseStatusEnum.PARSING_STOP">{{ TaskParseStatusEnumMapDesc[item.parseStatus] }}</view>
                            <view class="status parsing" v-else>{{ TaskParseStatusEnumMapDesc[item.parseStatus] }}</view>
                        </view>
                        <view class="quotation-info">
                            <view class="info-item">
                                <view class="label">询价时间</view>
                                <view class="value">{{ item.inquiryTime }}</view>
                            </view>
                            <view class="info-item">
                                <view class="label">询价单位</view>
                                <view class="value">{{ item.inquiryOrgName }}</view>
                            </view>
                            <view class="info-item">
                                <view class="label">报价金额</view>
                                <view class="value total">{{ formatThousands(item.quotationAmount) }}</view>
                            </view>
                        </view>
                        <view class="action">
                            <view class="re-quote" @click="reQuote(item)">
                                <image class="refresh-icon" src="/static/refresh.png"></image>
                                <view class="name">重新报价</view>
                            </view>
                            <view class="preview-detail">
                                <view class="detail" @click="reQuote(item)">明细</view>
                                <view class="preview" v-if="item.parseStatus === TaskParseStatusEnum.PARSED" @click="onFilePreview(item)">预览</view>
                            </view>
                        </view>
                    </view>
                </view>
            </scroll-view>
        </view>
        <!--        <view class="tip">
            <view class="desc">
                <image class="warning-icon" src="/static/warning.png"></image>
                <view class="explain">说明</view>
            </view>
            <view class="desc-list">
                <view class="desc-list-item">1. 个人报价历史，滚动加载</view>
                <view class="desc-list-item">2. 右上角筛选可以暂时不实现</view>
                <view class="desc-list-item">3. 重新报价默认使用最新版本（后续迭代再支持先选择版本）</view>
                <view class="desc-list-item">4. 查看明细、重新报价暂时跳转到询价页面（查看明细可以先把编辑按钮去掉即可）</view>
                <view class="desc-list-item">5. 报价单预览，若没有生成报价单，系统生成后自动预览</view>
            </view>
        </view> -->
        <loading-page :loading="loading"></loading-page>
    </view>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, reactive, ref } from 'vue';
import { IQuotationHistory } from '@/models';
import { queryQuotationHistory } from '@/api/quotation-history';
import { chooseMedia, chooseMessageFile, filePreview, formatThousands, navigateTo, showToast } from '@/utils';
import { useLoadingFn } from '@/hooks';
import { onPullDownRefresh, onShow, onHide } from '@dcloudio/uni-app';
import { TaskParseStatusEnumMapDesc, TaskParseStatusEnum } from '@/enums';
import { getQuotationInquiryDetail, quotationInquiryExport } from '@/api/quotation';
import { createPolling } from '@/utils/polling';
import { getShareFile } from '@/api/file';

const loading = ref(true);
let polling = null;
let preQuotationAttachmentId = null;
let pageNo: number = 1;
let isDisabledQuery: boolean = false;
const state = reactive<{
    quotationHistoryList: Array<IQuotationHistory>;
}>({
    quotationHistoryList: []
});

const handleQueryQuotationHistory = useLoadingFn(onQueryQuotationHistory, loading);

onShow(() => {
    if (isDisabledQuery) {
        return;
    }
    pageNo = 1;
    state.quotationHistoryList = [];
    handleQueryQuotationHistory();
});

onPullDownRefresh(async () => {
    pageNo = 1;
    isDisabledQuery = false;
    state.quotationHistoryList = [];
    await handleQueryQuotationHistory();
    uni.stopPullDownRefresh();
});

onHide(() => {
    polling?.stop();
});

onUnmounted(() => {
    isDisabledQuery = false;
    polling?.stop();
});

const onQuery = () => {
    pageNo = pageNo + 1;
    isDisabledQuery = false;
    handleQueryQuotationHistory();
};

async function onQueryQuotationHistory() {
    const { data } = await queryQuotationHistory({ pageNo, pageSize: 20 });
    state.quotationHistoryList = state.quotationHistoryList.concat(data.list);
}

const onFilePreview = async (quotationHistory: IQuotationHistory) => {
    isDisabledQuery = true;
    if (quotationHistory.quotationAttachmentId) {
        const { data: fileUrlData } = await getShareFile(quotationHistory.quotationAttachmentId);
        filePreview({ url: fileUrlData }, quotationHistory.quotationAttachmentName || quotationHistory.quotationBatchNo);
        return;
    }

    // 生成报价单
    loading.value = true;
    await quotationInquiryExport({ taskId: quotationHistory.id });
    polling = createPolling({
        fn: async (): Promise<any> => {
            return getQuotationInquiryDetail(quotationHistory.id);
        },
        interval: 2000,
        maxRetry: 3,
        onError: (err: Error) => {
            console.error('轮询错误:', err);
        },
        onSuccess: async ({ data }) => {
            if (data.quotationAttachmentId && preQuotationAttachmentId !== data.quotationAttachmentId) {
                preQuotationAttachmentId = data.quotationAttachmentId;
                quotationHistory.quotationAttachmentId = data.quotationAttachmentId;
                quotationHistory.quotationAttachmentUrl = data.quotationAttachmentUrl;
                loading.value = false;
                polling.stop();
                const { data: fileUrlData } = await getShareFile(quotationHistory.quotationAttachmentId);
                filePreview({ url: fileUrlData }, quotationHistory.quotationAttachmentName || quotationHistory.quotationBatchNo);
            }
        }
    });
    polling.start();
};

const reQuote = (data: IQuotationHistory) => {
    navigateTo({ url: `/pages/inquiry/index?taskId=${data.id}` });
};
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>
