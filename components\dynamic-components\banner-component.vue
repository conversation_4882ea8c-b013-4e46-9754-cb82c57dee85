<template>
  <view class="banner-component" :style="containerStyle">
    <uv-swiper
      :list="swiperList"
      :autoplay="config.autoplay"
      :interval="config.interval"
      :circular="true"
      :indicatorDots="config.indicatorDots"
      :indicatorColor="config.indicatorColor"
      :indicatorActiveColor="config.indicatorActiveColor"
      :height="config.height"
      :borderRadius="config.borderRadius"
      keyName="url"
      @click="handleSwiperClick"
      @change="handleSwiperChange"
    />
  </view>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { BannerConfig } from '../../types/dynamic-page';
import { convertStyleToCSS, sizeUtils } from '../../utils/style-helper';
import { theme } from '../../config/theme';

interface Props {
  config: BannerConfig['config'];
  style?: BannerConfig['style'];
}

const props = defineProps<Props>();
const currentIndex = ref(0);

// 容器样式
const containerStyle = computed(() => {
  const baseStyle = convertStyleToCSS(props.style);
  return {
    ...baseStyle,
    width: '100%',
    height: sizeUtils.convertSize(props.config.height),
  };
});

// 轮播数据处理
const swiperList = computed(() => {
  return props.config.images.map((item, index) => ({
    ...item,
    id: index,
    url: item.url,
    link: item.link,
    alt: item.alt || `轮播图${index + 1}`,
  }));
});

// 处理轮播点击事件
const handleSwiperClick = (item: any) => {
  if (item.link) {
    handleNavigation(item.link);
  }
};

// 处理轮播切换事件
const handleSwiperChange = (e: any) => {
  currentIndex.value = e.current || 0;
};

// 处理导航
const handleNavigation = (url: string) => {
  if (!url) return;
  
  // 判断是否为外部链接
  if (url.startsWith('http://') || url.startsWith('https://')) {
    // 外部链接，使用webview打开
    uni.navigateTo({
      url: `/pages/webview/index?url=${encodeURIComponent(url)}`
    });
  } else if (url.startsWith('/')) {
    // 内部页面链接
    uni.navigateTo({
      url: url,
      fail: () => {
        // 如果navigateTo失败，尝试switchTab
        uni.switchTab({ 
          url: url,
          fail: () => {
            uni.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      }
    });
  } else {
    // 其他类型的链接处理
    console.warn('Unsupported link type:', url);
  }
};
</script>

<style lang="scss" scoped>
.banner-component {
  position: relative;
  overflow: hidden;
  
  // 确保轮播图片完全填充容器
  :deep(.uv-swiper) {
    width: 100%;
    height: 100%;
  }
  
  :deep(.uv-swiper-item) {
    width: 100%;
    height: 100%;
  }
  
  :deep(.uv-swiper-item image) {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

// 响应式适配
@media screen and (max-width: 750px) {
  .banner-component {
    margin: 0 8px;
    border-radius: 8px;
    overflow: hidden;
  }
}
</style>
