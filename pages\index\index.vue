<template>
  <view class="index-page">
    <!-- 头部介绍 -->
    <view class="header-section">
      <view class="logo-area">
        <text class="logo-text">📱</text>
        <text class="app-title">动态页面系统</text>
        <text class="app-subtitle">基于JSON配置的页面渲染</text>
      </view>
    </view>

    <!-- 主要功能 -->
    <view class="main-section">
      <view class="main-button" @click="navigateToDynamicPage">
        <view class="button-icon">🚀</view>
        <text class="button-title">查看动态页面</text>
        <text class="button-desc">根据JSON配置渲染页面和组件</text>
      </view>
    </view>

    <!-- 配置说明 -->
    <view class="info-section">
      <text class="info-title">支持的组件类型</text>
      <view class="component-list">
        <text class="component-item">• notice - 公告组件</text>
        <text class="component-item">• image - 图片组件</text>
        <text class="component-item">• banner - 轮播图组件</text>
        <text class="component-item">• grid-menu - 九宫格菜单</text>
        <text class="component-item">• product-list - 商品列表</text>
        <text class="component-item">• flash-sale - 秒杀组件</text>
        <text class="component-item">• group-buy - 团购组件</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      title: '动态页面系统'
    };
  },
  methods: {
    navigateToDynamicPage() {
      uni.navigateTo({
        url: '/pages/dynamic-page/index',
        fail: () => {
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    }
  },

  onLoad() {
    console.log('首页加载');
  }
};
</script>

<style lang="scss" scoped>
.index-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
}

.header-section {
  padding: 80px 20px 40px;
  text-align: center;
}

.logo-area {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 40px 20px;
  backdrop-filter: blur(10px);
}

.logo-text {
  font-size: 64px;
  display: block;
  margin-bottom: 20px;
}

.app-title {
  font-size: 32px;
  font-weight: 600;
  color: white;
  display: block;
  margin-bottom: 12px;
}

.app-subtitle {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.8);
  display: block;
}

.main-section {
  padding: 40px 20px;
  text-align: center;
}

.main-button {
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 40px 20px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);

  &:active {
    transform: scale(0.98);
    opacity: 0.9;
  }
}

.button-icon {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.button-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  display: block;
}

.button-desc {
  font-size: 16px;
  color: #666;
  line-height: 1.4;
  display: block;
}

.info-section {
  padding: 0 20px 40px;
}

.info-title {
  font-size: 20px;
  font-weight: 600;
  color: white;
  margin-bottom: 20px;
  text-align: center;
  display: block;
}

.component-list {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  backdrop-filter: blur(10px);
}

.component-item {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.8;
  margin-bottom: 8px;
  display: block;

  &:last-child {
    margin-bottom: 0;
  }
}
</style>
