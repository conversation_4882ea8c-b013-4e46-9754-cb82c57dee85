<template>
  <view class="coupon-component" :style="containerStyle">
    <view class="coupon-wrapper" :style="wrapperStyle">
      <scroll-view
        scroll-x
        :show-scrollbar="false"
        class="coupon-scroll"
        v-if="config.displayMode === 'horizontal'"
      >
        <view class="coupon-list horizontal">
          <view
            v-for="(coupon, index) in visibleCoupons"
            :key="coupon.id"
            class="coupon-item"
            :style="couponItemStyle"
            @click="handleCouponClick(coupon, index)"
          >
            <view class="coupon-content">
              <!-- 优惠金额/折扣 -->
              <view class="coupon-discount">
                <text class="discount-symbol">{{ coupon.discountType === 'amount' ? '¥' : '' }}</text>
                <text class="discount-value">{{ coupon.discount }}</text>
                <text class="discount-unit">{{ coupon.discountType === 'percent' ? '折' : '' }}</text>
              </view>
              
              <!-- 优惠券信息 -->
              <view class="coupon-info">
                <text class="coupon-title">{{ coupon.title }}</text>
                <text class="coupon-subtitle">{{ coupon.subtitle }}</text>
                <text class="coupon-time">{{ formatTime(coupon.startTime, coupon.endTime) }}</text>
              </view>
              
              <!-- 领取按钮 -->
              <view class="coupon-action">
                <uv-button
                  text="立即领取"
                  :color="config.primaryColor || theme.colors.primary"
                  size="mini"
                  :custom-style="actionButtonStyle"
                  @click.stop="handleReceiveCoupon(coupon)"
                />
              </view>
            </view>
            
            <!-- 装饰性锯齿边 -->
            <view class="coupon-decoration">
              <view class="decoration-left"></view>
              <view class="decoration-right"></view>
            </view>
          </view>
        </view>
      </scroll-view>
      
      <!-- 垂直布局 -->
      <view v-else class="coupon-list vertical">
        <view
          v-for="(coupon, index) in visibleCoupons"
          :key="coupon.id"
          class="coupon-item vertical-item"
          :style="couponItemStyle"
          @click="handleCouponClick(coupon, index)"
        >
          <view class="coupon-content vertical-content">
            <view class="coupon-left">
              <view class="coupon-discount">
                <text class="discount-symbol">{{ coupon.discountType === 'amount' ? '¥' : '' }}</text>
                <text class="discount-value">{{ coupon.discount }}</text>
                <text class="discount-unit">{{ coupon.discountType === 'percent' ? '折' : '' }}</text>
              </view>
            </view>
            
            <view class="coupon-center">
              <text class="coupon-title">{{ coupon.title }}</text>
              <text class="coupon-subtitle">{{ coupon.subtitle }}</text>
              <text class="coupon-time">{{ formatTime(coupon.startTime, coupon.endTime) }}</text>
            </view>
            
            <view class="coupon-right">
              <uv-button
                text="领取"
                :color="config.primaryColor || theme.colors.primary"
                size="mini"
                :custom-style="actionButtonStyle"
                @click.stop="handleReceiveCoupon(coupon)"
              />
            </view>
          </view>
        </view>
      </view>
      
      <!-- 查看更多按钮 -->
      <view v-if="hasMoreCoupons" class="more-action">
        <uv-button
          text="查看全部优惠券"
          type="info"
          size="small"
          plain
          @click="handleViewMore"
        />
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { convertStyleToCSS } from '../../utils/style-helper';
import { theme } from '../../config/theme';

interface Coupon {
  id: string;
  title: string;
  subtitle: string;
  discount: string;
  discountType: 'amount' | 'percent';
  startTime: string;
  endTime: string;
}

interface CouponConfig {
  displayMode: 'horizontal' | 'vertical';
  couponCount: number;
  maxVisibleCount: number;
  spacing: number;
  coupons: Coupon[];
  backgroundColor: string;
  primaryColor: string;
  textColor: string;
  borderRadius: number;
  showBorder: boolean;
  borderColor: string;
  clickable: boolean;
  clickType: string;
  url: string;
  message: string;
  margin: number;
  height: number;
}

interface Props {
  config: CouponConfig;
  style?: any;
}

const props = defineProps<Props>();

// 容器样式
const containerStyle = computed(() => {
  if (!props.style) {
    return { width: '100%' };
  }
  
  const baseStyle = convertStyleToCSS(props.style);
  
  const processedStyle = {};
  Object.keys(baseStyle).forEach(key => {
    processedStyle[key] = String(baseStyle[key]);
  });
  
  return {
    ...processedStyle,
    width: '100%',
  };
});

// 包装器样式
const wrapperStyle = computed(() => {
  return {
    padding: `${props.config.margin || theme.spacing.md}px`,
    backgroundColor: props.config.backgroundColor || 'transparent',
  };
});

// 优惠券项样式
const couponItemStyle = computed(() => {
  return {
    backgroundColor: '#ffffff',
    borderRadius: `${props.config.borderRadius || theme.borderRadius.md}px`,
    border: props.config.showBorder ? `1px solid ${props.config.borderColor || theme.colors.border.light}` : 'none',
    marginRight: props.config.displayMode === 'horizontal' ? `${props.config.spacing || theme.spacing.sm}px` : '0',
    marginBottom: props.config.displayMode === 'vertical' ? `${props.config.spacing || theme.spacing.sm}px` : '0',
    height: `${props.config.height || 80}px`,
    minWidth: props.config.displayMode === 'horizontal' ? '200px' : 'auto',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
    overflow: 'hidden',
    position: 'relative',
  };
});

// 按钮样式
const actionButtonStyle = computed(() => {
  return {
    backgroundColor: props.config.primaryColor || theme.colors.primary,
    color: '#ffffff',
    borderRadius: '12px',
    fontSize: '12px',
    padding: '4px 12px',
  };
});

// 可见的优惠券
const visibleCoupons = computed(() => {
  const maxCount = props.config.maxVisibleCount || 3;
  return props.config.coupons.slice(0, maxCount);
});

// 是否有更多优惠券
const hasMoreCoupons = computed(() => {
  return props.config.coupons.length > (props.config.maxVisibleCount || 3);
});

// 格式化时间
const formatTime = (startTime: string, endTime: string) => {
  const start = new Date(startTime).toLocaleDateString();
  const end = new Date(endTime).toLocaleDateString();
  return `${start} - ${end}`;
};

// 处理优惠券点击
const handleCouponClick = (coupon: Coupon, index: number) => {
  if (props.config.clickable) {
    if (props.config.clickType === 'navigate' && props.config.url) {
      uni.navigateTo({
        url: props.config.url.replace('{id}', coupon.id),
        fail: () => {
          uni.showToast({ title: '页面跳转失败', icon: 'none' });
        }
      });
    } else if (props.config.clickType === 'message' && props.config.message) {
      uni.showToast({ title: props.config.message, icon: 'none' });
    }
  }
};

// 处理领取优惠券
const handleReceiveCoupon = (coupon: Coupon) => {
  uni.showToast({
    title: `领取成功: ${coupon.title}`,
    icon: 'success'
  });
  
  // 可以在这里添加领取优惠券的逻辑
  uni.$emit('coupon-receive', { coupon });
};

// 处理查看更多
const handleViewMore = () => {
  uni.navigateTo({
    url: '/pages/coupon/list',
    fail: () => {
      uni.showToast({ title: '查看全部优惠券', icon: 'none' });
    }
  });
};
</script>

<style lang="scss" scoped>
.coupon-component {
  width: 100%;
}

.coupon-wrapper {
  width: 100%;
}

.coupon-scroll {
  width: 100%;
  white-space: nowrap;
}

.coupon-list {
  &.horizontal {
    display: flex;
    flex-direction: row;
    padding-right: 16px;
  }
  
  &.vertical {
    display: flex;
    flex-direction: column;
  }
}

.coupon-item {
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:active {
    transform: scale(0.98);
    opacity: 0.9;
  }
  
  &.vertical-item {
    width: 100%;
  }
}

.coupon-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 12px;
  position: relative;
  z-index: 2;
  
  &.vertical-content {
    justify-content: space-between;
  }
}

.coupon-left {
  flex-shrink: 0;
  margin-right: 12px;
}

.coupon-center {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.coupon-right {
  flex-shrink: 0;
  margin-left: 12px;
}

.coupon-discount {
  display: flex;
  align-items: baseline;
  margin-bottom: 4px;
}

.discount-symbol {
  font-size: 14px;
  color: #ff4757;
  font-weight: 600;
}

.discount-value {
  font-size: 24px;
  color: #ff4757;
  font-weight: 700;
  margin: 0 2px;
}

.discount-unit {
  font-size: 14px;
  color: #ff4757;
  font-weight: 600;
}

.coupon-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin: 0 12px;
}

.coupon-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.coupon-subtitle {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.coupon-time {
  font-size: 10px;
  color: #999;
}

.coupon-action {
  flex-shrink: 0;
}

.coupon-decoration {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  pointer-events: none;
  z-index: 1;
}

.decoration-left,
.decoration-right {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 12px;
  height: 12px;
  background-color: #f5f5f5;
  border-radius: 50%;
}

.decoration-left {
  left: -6px;
}

.decoration-right {
  right: -6px;
}

.more-action {
  margin-top: 16px;
  text-align: center;
}

// 响应式适配
@media screen and (max-width: 750px) {
  .coupon-item {
    min-width: 180px;
  }
  
  .discount-value {
    font-size: 20px;
  }
  
  .coupon-title {
    font-size: 14px;
  }
}
</style>
