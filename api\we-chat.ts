import { IWechatUserInfo, Response, WeChatUserPhoneReq, WeChatUserReq } from '@/models';
import { get, post } from '@/utils';

/** 获取用户微信信息 */
export const getWeChatUserInfo = (data: WeChatUserReq): Promise<Response<IWechatUserInfo>> => {
    return get('admin-api/system/wechat/ma/userInfo', { data });
};

/** 获取手机号码 */
export const getWeChatUserPhone = (data: WeChatUserPhoneReq): Promise<Response<IWechatUserInfo>> => {
    return post('admin-api/system/wechat/ma/getPhoneNumber', { data });
};

/** 获取微信用户OpenId */
export const getWeChatUserOpenId = (jsCode: string): Promise<Response<IWechatUserInfo>> => {
    return get(`admin-api/system/wechat/ma/code2Session?jsCode=${jsCode}`);
};
