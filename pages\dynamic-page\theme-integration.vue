<template>
    <view class="themed-dynamic-page" :style="pageContainerStyle">
        <!-- 主题切换器 -->
        <view class="theme-controls" :style="controlsStyle">
            <text class="controls-title">主题化动态页面</text>
            <theme-switcher />
        </view>
        
        <!-- 页面内容 -->
        <view class="page-content">
            <!-- 动态组件渲染 -->
            <view 
                v-for="component in sortedComponents" 
                :key="component.id" 
                class="dynamic-component-wrapper"
                :style="getComponentWrapperStyle(component)"
            >
                <!-- 商品组件 -->
                <product-component 
                    v-if="component.type === 'product'" 
                    :config="getThemedConfig(component.config)" 
                    :style="getComponentStyle(component)" 
                />

                <!-- 搜索组件 -->
                <search-component 
                    v-else-if="component.type === 'search'" 
                    :config="getThemedConfig(component.config)" 
                    :style="getComponentStyle(component)" 
                />

                <!-- 公告组件 -->
                <notice-component 
                    v-else-if="component.type === 'notice'" 
                    :config="getThemedConfig(component.config)" 
                    :style="getComponentStyle(component)" 
                />

                <!-- 其他组件... -->
                <view v-else class="component-placeholder" :style="placeholderStyle">
                    <text class="placeholder-text">{{ component.type }} 组件</text>
                    <text class="placeholder-desc">已应用当前主题样式</text>
                </view>
            </view>
        </view>
        
        <!-- 主题信息面板 -->
        <view class="theme-info-panel" :style="infoPanelStyle">
            <text class="panel-title">当前主题</text>
            <view class="theme-colors">
                <view class="color-item">
                    <view class="color-dot" :style="{ backgroundColor: currentColors.primary }"></view>
                    <text class="color-name">主色</text>
                </view>
                <view class="color-item">
                    <view class="color-dot" :style="{ backgroundColor: currentColors.secondary }"></view>
                    <text class="color-name">辅助</text>
                </view>
                <view class="color-item">
                    <view class="color-dot" :style="{ backgroundColor: currentColors.accent }"></view>
                    <text class="color-name">强调</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import ProductComponent from '../../components/dynamic-components/product-component.vue';
import SearchComponent from '../../components/dynamic-components/search-component.vue';
import NoticeComponent from '../../components/dynamic-components/notice-component.vue';
import ThemeSwitcher from '../../components/theme/theme-switcher.vue';
import { convertStyleToCSS } from '../../utils/style-helper';
import { 
    currentThemeColors, 
    dynamicTheme, 
    initTheme,
    applyThemeToComponent 
} from '../../config/theme-system';

// 示例组件配置
const pageConfig = ref({
    pageConfig: {
        background: {
            type: 'solid',
            color: 'auto' // 使用主题颜色
        },
        padding: {
            top: 20,
            right: 16,
            bottom: 20,
            left: 16
        }
    },
    components: [
        {
            id: 'search-themed',
            type: 'search',
            config: {
                placeholder: '搜索商品...',
                backgroundColor: 'auto', // 使用主题颜色
                borderColor: 'auto',
                primaryColor: 'auto'
            },
            style: {
                padding: { top: 0, right: 0, bottom: 0, left: 0 },
                margin: { top: 0, right: 0, bottom: 16, left: 0 },
                borderRadius: { topLeft: 8, topRight: 8, bottomLeft: 8, bottomRight: 8 }
            },
            sort: 0
        },
        {
            id: 'notice-themed',
            type: 'notice',
            config: {
                content: '这是一个主题化的公告组件',
                backgroundColor: 'auto',
                textColor: 'auto',
                iconColor: 'auto'
            },
            style: {
                padding: { top: 0, right: 0, bottom: 0, left: 0 },
                margin: { top: 0, right: 0, bottom: 16, left: 0 },
                borderRadius: { topLeft: 8, topRight: 8, bottomLeft: 8, bottomRight: 8 }
            },
            sort: 1
        },
        {
            id: 'product-themed',
            type: 'product',
            config: {
                columns: 2,
                showPrice: true,
                showTitle: true,
                backgroundColor: 'auto',
                primaryColor: 'auto',
                products: [
                    {
                        id: '1',
                        title: '主题化商品1',
                        image: 'https://m.360buyimg.com/seckillcms/s150x150_jfs/t20280409/276203/1/18347/52095/67f788dcF34d4e028/f5f4b1484638c602.jpg',
                        price: 99.99,
                        originalPrice: 199.99
                    },
                    {
                        id: '2',
                        title: '主题化商品2',
                        image: 'https://m.360buyimg.com/seckillcms/s150x150_jfs/t1/149444/5/43456/63752/66d6744aFcf132eca/885f81c729aeb1ca.jpg',
                        price: 199.99,
                        originalPrice: 299.99
                    }
                ]
            },
            style: {
                padding: { top: 0, right: 0, bottom: 0, left: 0 },
                margin: { top: 0, right: 0, bottom: 16, left: 0 },
                borderRadius: { topLeft: 8, topRight: 8, bottomLeft: 8, bottomRight: 8 }
            },
            sort: 2
        }
    ]
});

// 当前主题
const currentColors = computed(() => currentThemeColors.value);
const theme = computed(() => dynamicTheme.value);

// 排序后的组件
const sortedComponents = computed(() => {
    return [...pageConfig.value.components].sort((a, b) => a.sort - b.sort);
});

// 页面容器样式
const pageContainerStyle = computed(() => {
    return {
        backgroundColor: theme.value.colors.background.secondary,
        paddingTop: `${pageConfig.value.pageConfig.padding.top}px`,
        paddingRight: `${pageConfig.value.pageConfig.padding.right}px`,
        paddingBottom: `${pageConfig.value.pageConfig.padding.bottom}px`,
        paddingLeft: `${pageConfig.value.pageConfig.padding.left}px`,
        minHeight: '100vh',
        width: '100%',
        boxSizing: 'border-box',
    };
});

// 控制栏样式
const controlsStyle = computed(() => ({
    backgroundColor: theme.value.colors.background.primary,
    borderRadius: '12px',
    padding: '16px',
    marginBottom: '20px',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    boxShadow: theme.value.shadows.sm,
}));

// 信息面板样式
const infoPanelStyle = computed(() => ({
    backgroundColor: theme.value.colors.background.primary,
    borderRadius: '12px',
    padding: '16px',
    marginTop: '20px',
    boxShadow: theme.value.shadows.sm,
}));

// 占位符样式
const placeholderStyle = computed(() => ({
    backgroundColor: theme.value.colors.background.primary,
    borderRadius: '8px',
    padding: '20px',
    textAlign: 'center',
    border: `2px dashed ${theme.value.colors.border.medium}`,
}));

// 获取主题化配置
const getThemedConfig = (config) => {
    return applyThemeToComponent(config);
};

// 获取组件包装器样式
const getComponentWrapperStyle = (component) => {
    return {
        marginBottom: '16px',
    };
};

// 获取组件样式
const getComponentStyle = (component) => {
    if (!component.style) {
        return {};
    }
    return convertStyleToCSS(component.style);
};

// 初始化
onMounted(() => {
    initTheme();
    
    // 监听主题变更事件
    uni.$on('theme-changed', (data) => {
        console.log('主题已切换:', data);
    });
});
</script>

<style lang="scss" scoped>
.themed-dynamic-page {
    width: 100%;
}

.controls-title {
    font-size: 18px;
    font-weight: 600;
    color: #374151;
}

.page-content {
    width: 100%;
}

.dynamic-component-wrapper {
    position: relative;
    width: 100%;
}

.component-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100px;
}

.placeholder-text {
    font-size: 16px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 4px;
}

.placeholder-desc {
    font-size: 12px;
    color: #6b7280;
}

.panel-title {
    font-size: 16px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 12px;
    display: block;
}

.theme-colors {
    display: flex;
    gap: 16px;
}

.color-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.color-dot {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.color-name {
    font-size: 12px;
    color: #6b7280;
}

// 移动端适配
@media screen and (max-width: 750px) {
    .controls-title {
        font-size: 16px;
    }
    
    .theme-colors {
        flex-wrap: wrap;
        gap: 12px;
    }
}
</style>
