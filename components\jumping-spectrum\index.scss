.content {
    background-color: #ffffff;
    padding: 20px;
    .spectrum-container {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        height: 100%;
    }

    .spectrum-bar {
        margin-right: 2rpx;
        border-radius: 5px;
        transition: height 0.1s;
        /* 连贯性通过过渡效果实现 */
    }
    .action {
        display: flex;
        margin-top: 20px;
        gap: 8px;
        .confirm,
        .cancel {
            font-size: 14px;
            border-radius: 4px;
            flex: 1;
            padding: 5px 0;
            text-align: center;
        }
        .cancel {
            color: #303133;
            border: 1px solid #dcdfe6;
        }
        .confirm {
            color: #ffffff;
            background: #00b678;
        }
    }
}
