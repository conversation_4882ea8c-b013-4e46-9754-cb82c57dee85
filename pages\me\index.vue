<template>
    <view class="content">
        <view class="header">
            <!--   <image class="avatar" :src="state.avatarInfo?.url"></image> -->

            <view class="avatar_box">
                <button class="avatar" open-type="chooseAvatar" @chooseavatar="onChooseavatar">
                    <template v-if="state.avatarInfo?.url">
                        <image class="img" :src="state.avatarInfo?.url" mode="aspectFit"></image>
                    </template>
                    <template v-else>
                        <image class="default_avatar" src="/static/default-avatar.png"></image>
                        <image class="camera" src="/static/camera_add.png"></image>
                    </template>
                </button>
            </view>

            <view class="nick-name">{{ state.nickname }}</view>
        </view>
        <view class="body">
            <view class="item">
                <view class="label">
                    <image class="icon" src="/static/enterprise.png"></image>
                    <view class="name">企业：</view>
                </view>
                <view class="value">{{ state.tenantInfo?.comName }}</view>
            </view>
            <view class="item">
                <view class="label">
                    <image class="icon" src="/static/phone.png"></image>
                    <view class="name">手机号：</view>
                </view>
                <view class="value">
                    {{ state.mobile }}
                </view>
            </view>
            <view class="item">
                <view class="label">
                    <image class="icon" src="/static/nickname.png"></image>
                    <view class="name">昵称：</view>
                </view>
                <view class="value">
                    <uv-input v-model="state.nickname" placeholder="请输入" inputAlign="right" border="none" @blur="onUpdate()" />
                </view>
            </view>
            <view class="item">
                <view class="label">
                    <image class="icon" src="/static/email.png"></image>
                    <view class="name">邮箱：</view>
                </view>
                <view class="value">
                    <uv-input v-model="state.email" placeholder="请输入" inputAlign="right" border="none" @blur="onUpdate()" />
                </view>
            </view>
            <view class="item remark">
                <view class="label">
                    <image class="icon" src="/static/brief.png"></image>
                    <view class="name">个人简介：</view>
                </view>
                <view class="value">
                    <uv-textarea
                        v-model="state.remark"
                        autoHeight
                        placeholder="请输入"
                        border="none"
                        :customStyle="{
                            'text-align': 'right',
                            padding: '0'
                        }"
                        @blur="onUpdate()"
                    />
                </view>
            </view>
        </view>
    </view>
</template>

<script setup lang="ts">
import { onMounted, reactive } from 'vue';
import { IProfile } from '@/models';
import { editAccount, editAvatar, getProfile } from '@/api/account';
import { showToast } from '@/utils';
import { emailRegex, phoneReg } from '@/consts';

let changeAvatar: boolean;
const state = reactive<IProfile>({});

onMounted(async () => {
    const { data } = await getProfile();
    Object.assign(state, data);
    if (!state.avatarInfo || Object.keys(state.avatarInfo).length === 0) {
        state.avatarInfo = {};
    }
});

/** 获取头像*/
const onChooseavatar = async (res: any) => {
    const { data } = await editAvatar(res.detail.avatarUrl);
    state.avatarInfo.url = data as string;
    showToast({ title: '更新成功', icon: 'none' });
};

const onUpdateNickName = async () => {};

const onUpdate = async () => {
    const { nickname, mobile, email } = state;
    if (!nickname?.trim()) {
        showToast({ title: '昵称不能为空', icon: 'none' });
        return;
    }

    if (!mobile?.trim()) {
        showToast({ title: '手机号不能为空', icon: 'none' });
        return;
    }

    if (!phoneReg.test(mobile)) {
        showToast({ title: '手机号码格式不正确', icon: 'none' });
        return;
    }

    if (email?.trim() && !emailRegex.test(email.trim())) {
        showToast({ title: '邮箱格式不正确', icon: 'none' });
        return;
    }

    await editAccount({
        id: state.id,
        nickname: state.nickname,
        mobile: state.mobile,
        email: state.email,
        remark: state.remark,
        username: state.username
    });
    showToast({ title: '更新成功', icon: 'success' });
};
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>
