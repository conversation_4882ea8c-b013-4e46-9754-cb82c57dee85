# 语音输入组件

> 提供长按语音输入交互界面，后续增加语音输入处理。
> 提供语音图标组件，支持语音播放动画效果。
> 提供语音录制图标组件。

## 交互界面组件 WptVoiceInputMask

### Props

| 属性 | 描述 | 类型 |是否必填 | 默认值 |
| :-- | :-- | :-- | :-- | :-- |
| modelValue | 遮罩是否显示 | boolean | - | - |
| zIndex | 遮罩层级 | number | - | 99 |
| maskColor | 遮罩颜色 | string | - | 'rgba(0, 0, 0, .65)' |
| maskClass | 遮罩自定义样式类 | string | - | - |
| triggerSize | 底部触发按钮尺寸，与屏幕宽度比，范围[1, 5] | number | - | 1.2 |
| triggerRadio | 底部触发按钮尺寸，与按钮宽度比，范围[1, 10] | number | - | 2.4 |
| confirmColor | 发送状态下，popover背景色，主色 | string | - | 'green' |
| cancelColor | 取消发送状态下，popover背景色，错误色 | string | - | 'red' |
| confirmClass | 发送状态下，popover自定义样式类 | string | - | - |
| cancelClass | 取消发送状态下，popover自定义样式类 | string | - | - |
| showTriangle | popover是否展示箭头 | boolean | - | true |
| triangleSize | popover展示箭头箭头尺寸 | number | - | 16 |
| triangleRadius | popover展示箭头箭头圆角 | number | - | 4 |
| tipsColor | popover提示语字体颜色 | string | - | 'rgba(232, 232, 232, 1)' |
| tipsOffset | popover提示语偏移 | number | - | 16 |
| popoverRadius | popover圆角 | number | - | 12 |
| popoverTips | 发送状态popover提示语 | string | - | '松开发送，手指上滑取消发送' |
| popoverCancelTips | 取消状态popover提示语 | string | - | '松开取消' |
| popoverTransformTips | 转文字状态popover提示语 | string | - | '转文字' |
| popoverWidth | popover宽度 | string \| number | - | '60vw' |
| popoverOffset | popover偏移 | number | - | 0 |
| popoverIcon | popover语音录入图标配置 | `Partial<VoiceInputIconProps>` | - | - |
| toolboxSize | 操作按钮的尺寸 | number | - | 56 |
| toolboxOffset | 操作按钮与底部触发按钮的间距 | number | - | 24 |
| toolboxRotateOffset | 操作按钮旋转偏移角度 | number | - | 20 |
| toolboxClass | 操作按钮自定义样式类 | string | - | 12 |
| toolboxActiveClass | 操作按钮激活时自定义样式类 | string | - | 12 |
| toolboxColor | 操作按钮背景色 | string | - | 'rgba(50, 50, 50, 1)' |
| toolboxActiveColor | 操作按钮激活时背景色 | string | - | 'rgba(255, 255, 255, 1)' |
| toolboxTextColor | 操作按钮字体颜色 | string | - | '#fff' |
| toolboxActiveTextColor | 操作按钮激活时字体颜色 | string | - | '#000' |
| toolboxFontSize | 操作按钮字体大小 | string \| number | - | '44rpx' |
| triggerClass | 底部触发按钮自定义样式类 | string | - | 12 |
| triggerActiveClass | 底部触发按钮激活时自定义样式类 | string | - | 12 |
| triggerColor | 底部触发按钮背景色 | string | - | 'rgba(50, 50, 50, 1)' |
| triggerActiveColor | 底部触发按钮激活时背景色 | string | - | 'rgba(255, 255, 255, 1)' |
| triggerIconColor | 底部触发按钮语音图标颜色 | string | - | '#fff' |
| triggerActiveIconColor | 底部触发按钮激活时语音图标颜色 | string | - | 'rgba(202, 202, 202, 1)' |
| triggerIcon | 底部触发按钮语音图标的配置 | `Partial<VoiceIconProps>` | - | - |
| triggerShadow | 是否显示底部触发按钮阴影 | boolean | - | false |
| triggerShadowColor | 底部触发按钮阴影颜色 | string | - | 'rgba(0, 0, 0, .2)' |
| triggerShadowSize | 底部触发按钮阴影大小 | number | - | 32 |
| triggerShadowBlur | 底部触发按钮阴影模糊 | number | - | 6 |
| showCancelBtn | 是否显示取消按钮 | boolean | - | true |
| showTransferBtn | 是否显示转文字按钮 | boolean | - | true |
| allowCancelByMove | 是否允许根据移出距离判断取消 | boolean | - | false |
| cancelMoveDistance | 判定取消的移出距离 | number | - | 0 |
| moveOffset | 触发移动偏差 | number | - | 5 |
| countdownLimit | 倒计时显示阈值，ms，未设置则不显示倒计时 | number | - | 0 |
| remote | 开启远程请求，内置接口调用 | boolean | - | false |
| remoteUrl | 默认上传请求url | string | - | - |
| remoteFilename | 默认上传文件名称 | string | - | 'file' |
| remoteParams | 默认上传其他请求参数 | `Record<string, any>` | - | - |
| remoteHeaders | 默认上传请求头 | `Record<string, any>` | - | - |
| remoteMethod | 自定义上传方式 | `(tempFilePath: string) => Promise<any>` | - | - |
| preAuthorize | 提前获取微信授权，避免首次点击授权异步延时 | boolean | - | true |

继承`RecorderManager`属性:

```ts
export interface UseRecorderManagerProps {
    modelValue: boolean;
    duration?: number; // 指定录音的时长，单位 ms ，如果传入了合法的 duration ，在到达指定的 duration 后会自动停止录音，最大值 600000（10 分钟）,默认值 60000（1 分钟）	App、小程序支持
    sampleRate?: number; //	否	采样率，有效值 8000/16000/44100	App、小程序支持
    numberOfChannels?: number; //	Number	否	录音通道数，有效值 1/2	仅小程序支持
    encodeBitRate?: number; //	否	编码码率，有效值见下表格	仅小程序支持
    format?: "aac" | "mp3" | "wav" | "PCM"; //	否	音频格式，有效值 aac/mp3/wav/PCM。App默认值为mp3，小程序默认值aac	App、小程序支持
    frameSize?: number; //	ms 否	指定帧大小，单位 KB。传入 frameSize 后，每录制指定帧大小的内容后，会回调录制的文件内容，不指定则不会回调。暂仅支持 mp3 格式。	App、百度小程序不支持
    hideTips?: boolean; //	否	隐藏录音图标。	支付宝小程序10.1.85+
    audioSource?: string; //	否	指定录音的音频输入源。	微信小程序详见、支付宝小程序详见、百度小程序详见、快手小程序
    detectDecibel: boolean;
    transformText?: boolean;
    analysisVolumn?: boolean;
    defaultVolumeThreshold?: number; // 判断音量阈值（0, 100]
    maxVolumeThreshold?: number; // 音量最大阈值（0, 32767]
    weightVolumeThreshold?: number; // 音量权重 (0, 100]
    joinEnvironment?: number;
    countdown?: boolean;
    customTransformText?: (res: any) => Promise<string>;
    customAnalysisSpeaking?: (res: any) => Promise<[boolean, number]>;
}
```

### Events

| 事件 | 描述 | 类型 |
| :-- | :-- | :-- |
| update:modelValue | 遮罩是否显示 | `(visible: boolean) => void` |
| record | 获取录音临时文件地址，用于录音上传 | `(tempFilePath: string) => void` |
| upload | 内置录音上传 | `(res: VoiceUploadResponse) => void` |

一些 `RecorderManager` 事件

```ts
export interface UseRecorderManagerEmits {
    (e: "onError", res: any): void;
    (e: "onStart", res: any): void;
    (e: "onStop", tempFilePath: string, res: any): void;
    (e: "onPause", res: any): void;
    (e: "onResume", res: any): void;
    (e: "onFrameRecorded", res: any): void;
    (e: "onInterruptionBegin", res: any): void;
    (e: "onInterruptionEnd", res: any): void;
}
```

### Types

#### VoiceUploadResponse

```ts
export interface VoiceUploadResponse {
    type: "success" | "error" | "fail";
    filePath: string;
    message?: string;
    result?: any;
}
```

### slots

| 插槽名 | 描述 |
| :-- | :-- |
| voice | 自定义音量录制图标 |
| icon | 自定义语音图标 |
| reference | 语音交互遮罩触发器 |

### Exposes

| 属性 | 描述 | 类型 |
| :-- | :-- | :-- |
| cancelFlag | 取消 | `Ref<boolean>` |
| transformFlag | 转文字 | `Ref<boolean>` |
| closeMask | 关闭遮罩 | `() => void` |

---

## 语音图标组件 WptVoiceIcon

### Props

| 属性 | 描述 | 类型 |是否必填 | 默认值 |
| :-- | :-- | :-- | :-- | :-- |
| strokeWidth | 线宽 | number | - | 3 |
| strokeGutter | 线间距，未设置取strokeWidth值 | number | - | 3 |
| strokeColor | 线颜色 | string | - | 'rgba(143, 143, 143, 1)' |
| size | 扇形尺寸（尖头） | number | - | 8 |
| angle | 扇形角度（尖头） | number | - | 75 |
| animation | 开启动画 | number | - | false |
| animationSpeed | 动画速度，ms | number | - | 250 |
| autoAnimation | 挂载后直接播放 | number | - | false |

### Exposes

| 属性 | 描述 | 类型 |
| :-- | :-- | :-- |
| startAnimation | 开始动画 | `() => void` |
| endAnimation | 结束动画 | `() => void` |

---

## 语音录制图标组件 WptVoiceInputIcon

### Props

| 属性 | 描述 | 类型 |是否必填 | 默认值 |
| :-- | :-- | :-- | :-- | :-- |
| width | 图标容器宽度 | string\/number | - | 'auto' |
| height | 图标容器高度，建议按maxScale设置 | string\/number | - | - |
| strokeWidth | 线宽 | number | - | 2 |
| strokeHeight | 线高 | number | - | 10 |
| strokeColor | 线颜色 | string | - | 'rgba(50, 50, 50, 1)' |
| strokeCount | 线数量 | number | - | 16 |
| initialScale | 默认线缩放 | number | - | 1 |
| minScale | 最小线缩放 | number | - | 0.2 |
| maxScale | 最大线缩放 | number | - | 4 |
| awaitScale | 等待录音线缩放 | number | - | 1.8 |
| awaitDelay | 等待录音线动画延时，ms | number | - | 120 |
| awaitDuration | 等待录音线动画持续时间，ms | number | - | 3600 |
| startDuration | 正在录音线动画持续时间，ms | number | - | 240 |
| weight | 正在录音音量与缩放的权重基数 | number | - | 100 |

### Exposes

| 属性 | 描述 | 类型 |
| :-- | :-- | :-- |
| stopRecording | 停止所有动画 | `() => void` |
| startRecording | 录音收录动画 | `() => void` |
| awaitRecording | 录音等待动画 | `() => void` |


## 语音图标组件V2 WptVoiceIconV2

> 基于svg绘制实现，微信小程序对于svg支持比较特殊。

### Props

| 属性 | 描述 | 类型 |是否必填 | 默认值 |
| :-- | :-- | :-- | :-- | :-- |
| color | 图标颜色 | string | - | 'rgba(143, 143, 143, 1)' |
| size | 图标尺寸 | number | - | 24 |
| animation | 开启动画 | number | - | false |
| animationSpeed | 动画速度，ms | number | - | 250 |
| autoAnimation | 挂载后直接播放 | number | - | false |

### Exposes

| 属性 | 描述 | 类型 |
| :-- | :-- | :-- |
| startAnimation | 开始动画 | `() => void` |
| endAnimation | 结束动画 | `() => void` |

[Vue3演练场](https://play.vuejs.org/)

---
