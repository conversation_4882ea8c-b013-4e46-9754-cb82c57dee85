/**
 * 动态页面组件类型定义
 */

// 基础样式接口
export interface BaseStyle {
  padding: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
  margin: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
  borderRadius: {
    topLeft: number;
    topRight: number;
    bottomLeft: number;
    bottomRight: number;
  };
}

// 基础组件配置接口
export interface BaseComponentConfig {
  id: string;
  type: string;
  sort: number;
  style: BaseStyle;
}

// 公告组件配置
export interface NoticeConfig extends BaseComponentConfig {
  type: 'notice';
  config: {
    content: string;
    backgroundColor: string;
    textColor: string;
    fontSize: number;
    height: number;
    borderRadius: number;
    showIcon: boolean;
    iconType: string;
    iconColor: string;
    scrollable: boolean;
    scrollSpeed: number;
    showCloseButton: boolean;
    clickable: boolean;
    clickType: 'navigate' | 'message' | 'none';
    url: string;
    message: string;
    margin: number;
    padding: number;
  };
}

// 图片组件配置
export interface ImageConfig extends BaseComponentConfig {
  type: 'image';
  config: {
    url: string;
    link: string;
    alt: string;
    width: number | string; // 支持数字和字符串（如 "100%"）
    height: number;
    borderRadius: number;
  };
}

// 轮播组件配置
export interface BannerConfig extends BaseComponentConfig {
  type: 'banner';
  config: {
    images: Array<{
      url: string;
      link: string;
      alt: string;
    }>;
    height: number;
    borderRadius: number;
    autoplay: boolean;
    interval: number;
    indicatorDots: boolean;
    indicatorColor: string;
    indicatorActiveColor: string;
  };
}

// 九宫格菜单配置
export interface GridMenuConfig extends BaseComponentConfig {
  type: 'grid-menu';
  config: {
    columns: number;
    items: Array<{
      id: string;
      name: string;
      image: string;
      clickType: 'navigate' | 'message';
      url?: string;
      message?: string;
      sort: number;
    }>;
    backgroundColor: string;
    itemBackgroundColor: string;
    textColor: string;
    fontSize: number;
    itemPadding: number;
    itemMargin: number;
    itemBorderRadius: number;
    showBorder: boolean;
    borderColor: string;
  };
}

// 商品列表配置
export interface ProductListConfig extends BaseComponentConfig {
  type: 'product-list';
  config: {
    title: string;
    showMore: boolean;
    moreText: string;
    moreLink: string;
    layout: 'grid' | 'list';
    columns: number;
    products: Array<{
      id: string;
      name: string;
      image: string;
      price: number;
      originalPrice?: number;
      sales: number;
      tags?: string[];
      link: string;
    }>;
  };
}

// 秒杀组件配置
export interface FlashSaleConfig extends BaseComponentConfig {
  type: 'flash-sale';
  config: {
    title: string;
    subtitle: string;
    endTime: string;
    backgroundColor: string;
    textColor: string;
    products: Array<{
      id: string;
      name: string;
      image: string;
      price: number;
      originalPrice: number;
      progress: number;
      link: string;
    }>;
  };
}

// 团购组件配置
export interface GroupBuyConfig extends BaseComponentConfig {
  type: 'group-buy';
  config: {
    title: string;
    subtitle: string;
    backgroundColor: string;
    products: Array<{
      id: string;
      name: string;
      image: string;
      groupPrice: number;
      originalPrice: number;
      groupSize: number;
      currentCount: number;
      link: string;
    }>;
  };
}

// 商品组件配置
export interface ProductConfig extends BaseComponentConfig {
  type: 'product';
  config: {
    layout: 'grid' | 'list';
    columns: number;
    showPrice: boolean;
    showTitle: boolean;
    showRating: boolean;
    products: Array<{
      id: string;
      title: string;
      price: number;
      originalPrice?: number;
      image: string;
      rating?: number;
      link: string;
    }>;
  };
}

// 搜索组件配置
export interface SearchConfig extends BaseComponentConfig {
  type: 'search';
  config: {
    placeholder: string;
    backgroundColor: string;
    borderColor: string;
    borderRadius: number;
    height: number;
    fontSize: number;
    textColor: string;
    placeholderColor: string;
    showSearchIcon: boolean;
    iconPosition: 'left' | 'right';
    iconColor: string;
    showClearButton: boolean;
    buttonText: string;
    buttonBackgroundColor: string;
    buttonTextColor: string;
    buttonBorderRadius: number;
    showButton: boolean;
    searchType: string;
    margin: number;
  };
}

// 轮播图组件配置
export interface CarouselConfig extends BaseComponentConfig {
  type: 'carousel';
  config: {
    images: Array<{
      id: string;
      url: string;
      title?: string;
      link?: string;
    }>;
    autoplay: boolean;
    interval: number;
    height: number;
    showIndicators: boolean;
    showArrows: boolean;
  };
}

// 优惠券组件配置
export interface CouponConfig extends BaseComponentConfig {
  type: 'coupon';
  config: {
    displayMode: 'horizontal' | 'vertical';
    couponCount: number;
    maxVisibleCount: number;
    spacing: number;
    coupons: Array<{
      id: string;
      title: string;
      subtitle: string;
      discount: string;
      discountType: 'amount' | 'percent';
      startTime: string;
      endTime: string;
    }>;
    backgroundColor: string;
    primaryColor: string;
    textColor: string;
    borderRadius: number;
    showBorder: boolean;
    borderColor: string;
    clickable: boolean;
    clickType: string;
    url: string;
    message: string;
    margin: number;
    height: number;
  };
}

// 分割线组件配置
export interface DividerConfig extends BaseComponentConfig {
  type: 'divider';
  config: {
    text: string;
    textColor: string;
    textSize: number;
    textPosition: 'left' | 'center' | 'right';
    lineColor: string;
    borderStyle: 'solid' | 'dashed' | 'dotted';
    marginTop: number;
    marginBottom: number;
    halfWidth: boolean;
    height: number;
    backgroundColor: string;
  };
}

// 秒杀组件配置
export interface SeckillConfig extends BaseComponentConfig {
  type: 'seckill';
  config: {
    title: string;
    endTime: string;
    showCountdown: boolean;
    products: Array<{
      id: string;
      title: string;
      image: string;
      seckillPrice: number;
      originalPrice: number;
      soldCount: number;
      stock: number;
      link: string;
    }>;
    backgroundColor: string;
    progressColor: string;
    buttonColor: string;
    spacing: number;
    margin: number;
  };
}

// 商品标签页组件配置
export interface ProductTabConfig extends BaseComponentConfig {
  type: 'product-tab';
  config: {
    displayMode: 'horizontal' | 'vertical';
    maxVisibleProducts: number;
    productSpacing: number;
    categories: Array<{
      id: string;
      name: string;
      products: Array<{
        id: string;
        title: string;
        price: number;
        originalPrice?: number;
        image: string;
        categoryId: string;
      }>;
    }>;
    backgroundColor: string;
    tabBackgroundColor: string;
    activeTabColor: string;
    inactiveTabColor: string;
    tabTextColor: string;
    activeTabTextColor: string;
    indicatorColor: string;
    borderRadius: number;
    showBorder: boolean;
    borderColor: string;
    margin: number;
    productColumns: number;
  };
}

// 团购组件配置
export interface GroupBuyConfig extends BaseComponentConfig {
  type: 'groupbuy';
  config: {
    title: string;
    description: string;
    badgeText: string;
    headerAlign: 'left' | 'center' | 'right';
    products: Array<{
      id: string;
      title: string;
      image: string;
      groupPrice: number;
      originalPrice: number;
      groupSize: number;
      successGroups: number;
      currentParticipants: number;
      targetParticipants: number;
      status: 'active' | 'success' | 'failed';
      endTime: string;
      link: string;
    }>;
    backgroundColor: string;
    headerBackgroundColor: string;
    badgeBackgroundColor: string;
    badgeTextColor: string;
    progressColor: string;
    buttonColor: string;
    spacing: number;
    margin: number;
    showRules: boolean;
    minGroupSize: number;
    timeLimit: number;
  };
}

// 接龙购买组件配置
export interface ChainBuyConfig extends BaseComponentConfig {
  type: 'chainbuy';
  config: {
    title: string;
    description: string;
    badgeText: string;
    headerAlign: 'left' | 'center' | 'right';
    chains: Array<{
      id: string;
      product: {
        id: string;
        title: string;
        image: string;
        chainPrice: number;
        originalPrice: number;
      };
      organizer: {
        id: string;
        name: string;
        avatar: string;
      };
      participants: Array<{
        id: string;
        name: string;
        avatar: string;
      }>;
      targetCount: number;
      status: 'active' | 'success' | 'ended';
      endTime: string;
      createTime: string;
    }>;
    backgroundColor: string;
    headerBackgroundColor: string;
    badgeBackgroundColor: string;
    badgeTextColor: string;
    progressColor: string;
    buttonColor: string;
    spacing: number;
    margin: number;
    maxVisibleChains: number;
  };
}

// 服务商品组件配置
export interface ServiceProductConfig extends BaseComponentConfig {
  type: 'service-product';
  config: {
    title: string;
    description: string;
    displayMode: 'grid' | 'list';
    columns: number;
    maxVisibleCount: number;
    services: Array<{
      id: string;
      title: string;
      subtitle: string;
      image: string;
      price: number;
      originalPrice?: number;
      priceUnit: string;
      tag?: string;
      features: string[];
      rating?: number;
      reviewCount?: number;
      link: string;
    }>;
    backgroundColor: string;
    headerBackgroundColor: string;
    iconColor: string;
    tagBackgroundColor: string;
    tagTextColor: string;
    buttonColor: string;
    ratingColor: string;
    spacing: number;
    margin: number;
    showRating: boolean;
  };
}

// 海报组件配置
export interface PosterConfig extends BaseComponentConfig {
  type: 'poster';
  config: {
    type: 'image' | 'video';
    imageUrl: string;
    videoUrl?: string;
    title?: string;
    subtitle?: string;
    content?: string;
    description?: string;
    badge?: string;
    tags?: string[];
    width: number | string;
    height: number | string;
    borderRadius: number;
    imageMode: string;
    overlayPosition: 'top' | 'center' | 'bottom';
    overlayBackground: string;
    titleColor: string;
    titleSize: number;
    subtitleColor: string;
    subtitleSize: number;
    contentColor: string;
    contentSize: number;
    badgeBackgroundColor: string;
    badgeTextColor: string;
    badgePosition: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
    showButton: boolean;
    buttonText: string;
    buttonColor: string;
    buttonSize: string;
    showBottomInfo: boolean;
    showShareButton: boolean;
    backgroundColor: string;
    clickable: boolean;
    clickType: string;
    url: string;
    message: string;
    margin: number;
  };
}

// 文章组件配置
export interface ArticleConfig extends BaseComponentConfig {
  type: 'article';
  config: {
    title: string;
    description: string;
    displayMode: 'list' | 'grid';
    columns: number;
    maxVisibleCount: number;
    articles: Array<{
      id: string;
      title: string;
      summary?: string;
      image?: string;
      author?: string;
      category?: string;
      tags?: string[];
      publishTime: string;
      readCount?: number;
      likeCount?: number;
      link: string;
    }>;
    showHeader: boolean;
    backgroundColor: string;
    headerBackgroundColor: string;
    iconColor: string;
    tagBackgroundColor: string;
    tagTextColor: string;
    categoryBackgroundColor: string;
    categoryTextColor: string;
    spacing: number;
    margin: number;
  };
}

// 组件联合类型
export type ComponentConfig =
  | NoticeConfig
  | ImageConfig
  | BannerConfig
  | GridMenuConfig
  | ProductListConfig
  | ProductConfig
  | SearchConfig
  | CarouselConfig
  | CouponConfig
  | DividerConfig
  | SeckillConfig
  | ProductTabConfig
  | GroupBuyConfig
  | ChainBuyConfig
  | ServiceProductConfig
  | PosterConfig
  | ArticleConfig
  | FlashSaleConfig;

// 页面背景配置
export interface PageBackground {
  type: 'solid' | 'gradient' | 'image';
  color?: string;
  gradient?: {
    type: 'linear' | 'radial';
    colors: string[];
    direction?: string;
  };
  image?: {
    url: string;
    repeat: 'no-repeat' | 'repeat' | 'repeat-x' | 'repeat-y';
    position: string;
    size: 'cover' | 'contain' | 'auto';
  };
}

// 页面内边距配置
export interface PagePadding {
  top: number;
  right: number;
  bottom: number;
  left: number;
}

// 页面安全区域配置
export interface PageSafeArea {
  top: boolean;
  bottom: boolean;
}

// 页面导航栏配置
export interface PageNavigation {
  show: boolean;
  title?: string;
  backgroundColor?: string;
  textColor?: string;
  backButton?: boolean;
  customButtons?: Array<{
    id: string;
    text: string;
    icon?: string;
    position: 'left' | 'right';
    action: string;
  }>;
}

// 页面状态栏配置
export interface PageStatusBar {
  style: 'default' | 'light' | 'dark';
  backgroundColor?: string;
}

// 页面滚动配置
export interface PageScroll {
  enabled: boolean;
  bounces?: boolean;
  showScrollbar?: boolean;
  refreshEnabled?: boolean;
  loadMoreEnabled?: boolean;
}

// 页面动画配置
export interface PageAnimation {
  transition?: {
    type: 'slide' | 'fade' | 'zoom' | 'none';
    duration: number;
    direction?: 'left' | 'right' | 'up' | 'down';
  };
  loading?: {
    show: boolean;
    type: 'spinner' | 'skeleton' | 'custom';
    text?: string;
    backgroundColor?: string;
  };
}

// 页面SEO配置
export interface PageSEO {
  title?: string;
  description?: string;
  keywords?: string[];
  shareImage?: string;
}

// 页面级配置
export interface PageLevelConfig {
  background: PageBackground;
  padding: PagePadding;
  previewMode: 'mobile' | 'desktop';
  safeArea?: PageSafeArea;
  navigation?: PageNavigation;
  statusBar?: PageStatusBar;
  scroll?: PageScroll;
  animation?: PageAnimation;
  seo?: PageSEO;
  customCSS?: string;
  customJS?: string;
}

// 元数据配置
export interface PageMetadata {
  version: string;
  timestamp: number;
  exportedAt: string;
}

// 完整页面配置接口
export interface PageConfig {
  pageConfig: PageLevelConfig;
  components: ComponentConfig[];
  metadata: PageMetadata;
}

// 组件注册映射
export interface ComponentRegistry {
  [key: string]: any;
}
